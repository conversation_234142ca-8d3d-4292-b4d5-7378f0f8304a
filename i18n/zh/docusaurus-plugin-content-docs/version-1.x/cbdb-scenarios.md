---
title: 使用场景
---

本文档介绍 Apache Cloudberry 的使用场景。

**场景一：离线批处理数据仓库和数据集市建设 (Data Warehousing and Data Marts)**

- 构建高性能的 Apache Cloudberry 数据仓库和数据集市，用于存储和查询大规模数据集，包含贴源层、明细层、汇总层等等，支持贴源模型建设、范式化模型建设、维度表和事实表建设等等，支持多种方式将源数据加载至数据仓库。
- 支持多种类型的数据加工处理。
- 支持高并发、高性能、低运维的数据仓库和数据集市建设。
- 支持复杂的数据分析和查询需求，包括数据聚合、多维分析、关联查询等。

**场景二：实时数据仓库建设**

- 支持高时效的数据仓库建设，支持流式数据的采集和处理，实现数据实时分析。

**场景三：数据中台建设**

- 支持数据中台中 MPP 数据平台的建设，支持分布式并行处理架构。
- 支持数据中台数据仓库的建设，支持多种主流 ETL 工具的对接。

**场景四：湖仓一体建设**

- 支持企业湖仓一体建设，支持数据湖和数据仓库之间数据高效的互访。

**场景五：现有 MPP 数据库替换**

- 支持非国产数据库的替换，例如 Oracle、TeraData、Greenplum、Vertical 等。
- 支持其他类型 MPP 数据库的替换，例如 Gbase 8a、GaussDB 等。

**场景六：地理信息系统 (GIS) 应用 (Geographic Information System Applications)**

- 在 Apache Cloudberry 上构建地理信息系统 (GIS) 应用。
- 存储和查询地理位置数据，支持空间数据分析、地理编码和地图可视化等功能。
- 可以应用于城市规划、地理分析、地图导航等领域。
