---
title: 性能问题的常见原因
---

# 性能问题的常见原因

本文档阐述了常见性能问题的排查过程以及可能的解决方案。

## 识别硬件和 Segment 故障

Apache Cloudberry 的性能取决于其运行的硬件和 IT 基础设施。Apache Cloudberry 由多个服务器（主机）组成，它们共同构成一个内聚的系统（阵列）。作为诊断性能问题的第一步，请确保所有的 Apache Cloudberry Segment 都在线。Apache Cloudberry 集群的性能取决于阵列中最慢的主机。CPU 使用率、内存管理、I/O 处理或网络负载等问题都会影响性能。常见的硬件相关问题包括：

- **磁盘故障** – 如果你正在使用 RAID，单个磁盘故障通常不会对数据库性能产生巨大影响，但磁盘的重新同步过程会消耗故障磁盘所在主机的资源。`gpcheckperf` 实用程序可以帮助识别存在磁盘 I/O 问题的 Segment 主机。
- **主机故障** – 当一台主机离线时，该主机上的 Segment 将无法运行。这意味着阵列中的其他主机必须承担双倍的工作负载，因为它们需要同时运行主 Segment 和多个镜像 Segment。如果没有启用镜像，服务将会中断。在恢复失败的 Segment 期间，服务会暂时中断。`gpstate` 实用程序有助于识别发生故障的 Segment。
- **网络故障** – 网卡、交换机或 DNS 服务器的故障都可能导致 Segment 宕机。如果在 Apache Cloudberry 集群内部无法解析主机名或 IP 地址，系统日志中会显示互连错误（interconnect errors）。`gpcheckperf` 实用程序有助于识别存在网络问题的 Segment 主机。
- **磁盘容量** – Segment 主机的磁盘使用率不应超过 70%。Apache Cloudberry 在运行时需要一定的可用空间进行处理。要在加载或更新数据后回收已删除行占用的磁盘空间，请运行 `VACUUM` 命令。`gp_toolkit` 管理模式（administrative schema）提供了许多视图，可用于检查分布式数据库对象的大小。

## 管理工作负载

数据库系统的 CPU、内存和磁盘 I/O 资源都是有限的。当多个工作负载争用这些资源时，数据库性能就会下降。资源管理的目标是在满足不同业务需求的同时，最大化系统吞吐量。Apache Cloudberry 提供了资源队列（resource queues）和资源组（resource groups）来帮助你管理这些系统资源。

资源队列和资源组可以限制特定队列或组中的资源使用量和并发查询总数。通过将数据库角色（role）分配到合适的队列或组，管理员可以控制并发用户查询，防止系统过载。关于资源队列和资源组的更多信息，包括如何为你的 Apache Cloudberry 环境选择合适的方案，请参阅[管理资源](./manage-resources-using-resource-queues.md)。

Apache Cloudberry 管理员应在业务低峰时段（例如下班后）运行维护性工作负载，例如数据加载和 `VACUUM ANALYZE` 操作。不要与数据库用户争用系统资源，请在低使用率时段执行管理任务。

## 避免资源争用

当多个用户或工作负载试图以相互冲突的方式使用系统时，就会产生争用。例如，当两个事务试图同时更新同一个表时，就会发生争用。一个试图获取表级锁或行级锁的事务会无限期地等待，直到冲突的锁被释放。应用程序不应长时间保持事务开启，例如，在等待用户输入时。

## 维护数据库统计信息

Apache Cloudberry 使用基于成本的查询优化器，该优化器依赖于数据库的统计信息。准确的统计信息能让查询优化器更精确地估算查询检索的行数，从而选择最高效的查询计划。如果缺少数据库统计信息，查询优化器就无法估算返回的记录数。优化器不会假定有足够的内存来执行某些操作（如聚合），因此会采取最保守的策略，通过读写磁盘来完成这些操作。这比在内存中执行要慢得多。`ANALYZE` 命令用于收集查询优化器所需的数据库统计信息。

:::note 注意
当使用 GPORCA 运行 SQL 命令时，如果收集命令所引用的一个或一组列的统计信息可以提高命令性能，Apache Cloudberry 会发出警告。该警告会显示在命令行中，并且相关信息会添加到 Apache Cloudberry 的日志文件中。有关收集表列统计信息的信息，请参阅 [`ANALYZE`](../sql-stmts/analyze.md) 命令。
:::

### 识别查询计划中的统计信息问题

在使用 `EXPLAIN` 或 `EXPLAIN ANALYZE` 分析查询计划之前，请先熟悉你的数据，这有助于识别潜在的统计信息问题。检查查询计划中是否存在以下表明统计信息不准确的迹象：

- **优化器的估算值是否接近实际情况？** 运行 `EXPLAIN ANALYZE`，查看优化器估算的行数是否与查询操作实际返回的行数接近。
- **选择性高的谓词是否在计划早期就被应用？** 选择性最高的筛选条件应在计划的早期阶段应用，这样可以减少在计划树向上传递的行数。
- **优化器是否选择了最佳的连接顺序？** 当查询涉及多个表的连接（join）时，请确保优化器选择了最具选择性的连接顺序。能够过滤掉最多行数的连接操作应在计划的早期执行，以减少向计划树上方传递的行数。

有关解读查询计划的更多信息，请参阅[分析查询性能](./optimize-queries/analyze-query-performance.md)。

### 调整统计信息收集

以下配置参数控制用于统计信息收集的数据采样量：

- `default_statistics_target`

这些参数在系统级别控制统计信息采样。更好的做法是只对查询谓词中最常使用的列增加统计信息采样。你可以使用以下命令调整特定列的统计信息：

`ALTER TABLE...SET STATISTICS`

例如：

```sql
ALTER TABLE sales ALTER COLUMN region SET STATISTICS 50;
```

这等同于为特定列更改 `default_statistics_target` 的值。随后的 `ANALYZE` 操作将为该列收集更多的统计数据，从而产生更好的查询计划。

## 优化数据分布

在 Apache Cloudberry 中创建表时，你必须声明一个分布键（distribution key），以确保数据在系统中的所有 Segment 之间均匀分布。由于所有 Segment 并行处理查询，Apache Cloudberry 的整体速度取决于最慢的那个 Segment。如果数据分布不均，拥有更多数据的 Segment 返回结果的速度会较慢，从而拖慢整个系统的性能。

## 优化数据库设计

许多性能问题都可以通过优化数据库设计来改善。请检查你的数据库设计，并考虑以下几点：

- 模式（schema）是否反映了数据的访问方式？
- 能否将大表分解为分区（partition）？
- 是否使用了尽可能小的数据类型来存储列值？
- 用于连接表的列是否具有相同的数据类型？
- 你创建的索引是否被有效利用？

### Apache Cloudberry 的最大限制

为了帮助优化数据库设计，请回顾 Apache Cloudberry 支持的最大限制：

|维度|限制|
|---------|-----|
|数据库大小|无限制|
|表大小|无限制，每个 Segment 每个分区 128 TB|
|行大小|1.6 TB (1600 列 \* 1 GB)|
|字段大小|1 GB|
|每张表的行数|281474976710656 (2^48)|
|每张表/视图的列数|1600|
|每张表的索引数|无限制|
|每个索引的列数|32|
|每张表的表级约束数|无限制|
|表名长度|63 字节（受 `name` 数据类型限制）|

标记为“无限制”的维度本身不受 Apache Cloudberry 的内在限制。然而，在实践中，它们会受到可用磁盘空间和内存/交换空间的限制。当这些值异常大时，性能可能会下降。

:::note 注意
系统可同时存在的对象（包括表、索引和视图，但不包括行）数量存在上限。此限制为 `4294967296` (2^32)。
:::
