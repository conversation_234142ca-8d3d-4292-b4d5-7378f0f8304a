---
title: 资源管理
---

# 资源管理

Apache Cloudberry 具备强大的资源管理能力，可以根据业务需求为查询分配资源、设定优先级。当资源不足时，系统会阻止新查询运行，从而保障整体稳定性。

资源管理主要用于控制并发查询数、单个查询的内存使用量以及 CPU 占用。为此，Apache Cloudberry 提供了两种管理方案：[资源队列](./manage-resources-using-
resource-queues.md)和[资源组](./manage-resources-using-resource-groups.md)。

在 Apache Cloudberry 中，资源队列和资源组两种方案相互排斥，只能启用其一。你可以通过设置 `gp_resource_manager` 这个服务参数来选择启用的方案。

下表详细对比了这两种方案的差异。

|对比维度|资源队列|资源组|
|------|---------------|---------------|
|并发控制|定义了同时可用的查询槽（slot）数量|定义了同时可用的事务槽（slot）数量|
|CPU|通过查询优先级来间接控制 CPU|可以指定 CPU 资源的百分比或核心数；基于 Linux Control Groups (cgroups) 实现|
|内存|在队列和算子（operator）级别进行管理；允许用户超额申请（over-subscribe）|在事务级别进行管理，分配和追踪能力更强；允许用户超额申请|
|用户|限制只对非管理员用户生效|限制对超级用户（`SUPERUSER`）、非管理员用户以及非用户类的系统进程均生效|
|磁盘 I/O|不支持|可以限制最大磁盘读写吞吐量，以及每秒最大读写 IO 操作数（IOPS）|
|排队机制|当没有可用槽位或可用内存不足时，查询需要排队等待|仅当没有可用槽位时，查询才需要排队等待|
|查询失败|如果查询申请的内存超过了系统可用内存和溢写（spill）限制，查询可能会立即失败|如果查询申请的内存超过了系统可用内存和溢写限制，查询可能会失败|
|限制豁免|超级用户（`SUPERUSER`）以及某些特定的算子和函数不受限制|`SET`、`RESET` 和 `SHOW` 命令不受限制。此外，某些查询可以被配置为绕过并发数限制|
|外部组件|不支持|可以管理 PL/Container 的 CPU 资源|
