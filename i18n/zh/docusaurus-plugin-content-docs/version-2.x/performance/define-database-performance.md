---
title: 定义数据库性能
---

# 定义数据库性能

系统性能管理包括测量性能、找出性能问题的根源，并应用你所掌握的工具和技术来解决问题。

Apache Cloudberry 基于数据库管理系统（DBMS）向请求者提供信息的速率来衡量数据库性能。

## 理解性能因素

有几个关键的性能因素会影响数据库性能。理解这些因素有助于你发现性能优化的机会并规避问题：

- [系统资源](#系统资源)
- [工作负载](#工作负载)
- [吞吐量](#吞吐量)
- [资源争用](#资源争用)
- [优化](#优化)

### 系统资源

数据库性能在很大程度上依赖于磁盘 I/O 和内存使用情况。为了准确地设定性能预期，你需要了解你的 DBMS 部署所在硬件的基准性能。CPU、硬盘、磁盘控制器、内存（RAM）和网络接口等硬件组件的性能会显著影响数据库的运行速度。

:::note 注意
如果你在 Apache Cloudberry 主机上使用端点安全软件，可能会影响数据库的性能和稳定性。更多信息请参阅[关于端点安全软件](../security/index.md#关于终端安全软件)。
:::

### 工作负载

工作负载指的是 DBMS 在任何特定时间点所承受的总需求，它由用户查询、应用程序、批处理作业、事务和系统命令共同构成，并且会随时间波动。例如，在运行月底报表时工作负载会增加，而在大多数用户下班的周末则会减少。工作负载对数据库性能有很大影响。了解你的工作负载和高峰需求时段，有助于你规划如何最有效地利用系统资源，从而能够处理尽可能大的工作负载。

### 吞吐量

系统的吞吐量定义了其处理数据的整体能力。DBMS 的吞吐量通常以每秒查询数、每秒事务数或平均响应时间来衡量。DBMS 的吞吐量与底层系统（如磁盘 I/O、CPU 速度、内存带宽等）的处理能力密切相关，因此在设定 DBMS 吞吐量目标时，了解硬件的吞吐能力至关重要。

### 资源争用

争用（Contention）是指工作负载中的两个或多个组件试图以相互冲突的方式使用系统时发生的情况——例如，多个查询试图同时更新同一条数据，或者多个大型工作负载争夺系统资源。随着争用的加剧，吞吐量会下降。

### 优化

DBMS 的优化可以影响整体系统性能。SQL 的编写方式、数据库配置参数、表设计、数据分布等，都有助于数据库查询优化器创建最高效的访问计划。

## 确定可接受的性能

在着手进行性能调优时，你应该了解系统的预期性能水平，并定义可衡量的性能要求，以便准确评估系统性能。在设定性能目标时，请考虑以下几点：

- [硬件基准性能](#硬件基准性能)
- [性能基准测试](#性能基准测试)

### 硬件基准性能

大多数数据库性能问题并非由数据库本身引起，而是由其运行的底层系统造成的。I/O 瓶颈、内存问题和网络问题都会显著降低数据库性能。在进行数据库级或查询级的调优之前，了解你的硬件和操作系统（OS）的基准能力，将有助于你识别和排查与硬件相关的问题。

### 性能基准测试

为了保持良好性能或解决性能问题，你应该了解你的 DBMS 在特定工作负载下的处理能力。基准测试（benchmark）是一种预定义的工作负载，它会产生一组已知的结果。定期运行相同的基准测试，有助于你及时发现系统相关的性能退化问题。使用基准测试来比较不同工作负载，并找出需要优化的查询或应用程序。

许多第三方组织，如事务处理性能委员会（TPC），为数据库行业提供基准测试工具。TPC 提供了 TPC-H，这是一个决策支持系统，它通过审查大量数据、运行高复杂度的查询，为关键业务问题提供答案。

有关 TPC-H 的更多信息，请访问：[http://www.tpc.org/tpch](http://www.tpc.org/tpch)。
