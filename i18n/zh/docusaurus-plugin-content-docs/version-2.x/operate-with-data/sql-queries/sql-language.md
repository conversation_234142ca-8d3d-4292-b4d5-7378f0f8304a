---
title: SQL 语言
---

# SQL 语言

本文介绍了 Apache Cloudberry 所使用的 SQL 语言的基础组成部分。了解这些基础内容对于编写查询语句至关重要，它们构成了查询语句的基本构件。

## SQL 词汇系统

SQL 是一种用于访问数据库的标准语言。它由用于数据存储、检索、分析、查看、操作等方面的元素组成。你可以使用 SQL 命令来构建 Apache Cloudberry 引擎能够识别的查询与操作语句。SQL 查询由一系列命令组成，每个命令由语法合法的标记顺序排列，并以分号（`;`）结尾。

Apache Cloudberry 的结构与语法大体遵循 PostgreSQL 的实现，但存在一些差异。关于 PostgreSQL 中的 SQL 规则与概念，详见 PostgreSQL 官方文档的 "SQL Syntax" 章节。

## SQL 值表达式

SQL 中的值表达式由一个或多个值、符号、运算符、SQL 函数和数据组合而成。表达式用于比较数据或执行计算，并返回一个结果值。计算可以是逻辑运算、算术运算或集合操作。

以下是常见的值表达式类型：

- 聚合表达式
- 数组构造表达式
- 列引用
- 常量或字面值
- 相关子查询
- 字段选择表达式
- 函数调用
- `INSERT` 或 `UPDATE` 中的新列值
- 运算符调用中的列引用
- 函数定义或预处理语句体中的位置参数引用
- 行构造表达式
- 标量子查询
- `WHERE` 子句中的搜索条件
- `SELECT` 命令中的目标列表
- 类型转换表达式
- 括号括起来的值表达式，用于分组子表达式或更改运算优先级
- 窗口表达式

SQL 中的一些结构如函数和运算符本身也是表达式，但它们不遵循统一的通用语法规则。关于这些结构的详细用法，参见[使用函数与运算符](../../functions/index.md)。
