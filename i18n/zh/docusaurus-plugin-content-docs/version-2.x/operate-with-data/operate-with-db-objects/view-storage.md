---
title: 关于视图的存储
---

# 关于视图的存储

视图类似于表，二者都是关系对象——即“具有列的数据结构”。这类对象都存储在系统目录表 `pg_class` 中。二者的主要区别如下：

- 视图没有数据文件（因为视图不存储数据）。
- 在 `pg_class` 中，视图的 `relkind` 字段值为 `v`，而表的为 `r`。
- 视图有一个名为 `_RETURN` 的 `ON SELECT` 查询重写规则。

    该重写规则包含视图的定义，存储在目录表 `pg_rewrite` 的 `ev_action` 列中。

有关视图的更多技术细节，可参考 PostgreSQL 官方文档中的 [Views and the Rule System](https://www.postgresql.org/docs/14/rules-views.html)。

此外，视图的定义不是以字符串形式存储的，而是以查询语法树的形式存储。视图在创建时会被解析，这带来几个影响：

- 在执行 `CREATE VIEW` 时，系统会解析对象名称，因此当前的 `search_path` 设置会影响视图的定义。
- 系统使用内部不可变的对象 ID 引用对象，而不是名称。因此，即使视图定义中引用的对象或列被重命名，也不需要删除视图。
- Apache Cloudberry 能准确识别视图定义中使用了哪些对象，并为这些对象建立依赖关系。

需要注意的是，Apache Cloudberry 对视图的处理方式与函数的处理方式不同：函数体是以字符串形式存储的，创建时不会解析。因此，系统无法知道某个函数依赖哪些对象。

## 视图的依赖信息存储位置

以下系统目录表用于记录视图依赖的表信息：

- `pg_class` - 包含表和视图等对象的信息。`relkind` 字段表示对象类型。
- `pg_depend` - 数据库专有对象（非共享对象）的依赖信息。
- `pg_rewrite` - 表和视图的重写规则。
- `pg_attribute` - 表的列信息。
- `pg_namespace` - schema（命名空间）信息。

需要特别说明的是，视图本身并不直接依赖其引用的对象：真正的依赖关系存在于视图的重写规则上。这使得视图的依赖信息存在一层间接关系。
