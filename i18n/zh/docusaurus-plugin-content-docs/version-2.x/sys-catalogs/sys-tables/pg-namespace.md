---
title: pg_namespace
---

# pg_namespace

`pg_namespace` 系统目录表用于存储命名空间信息。命名空间是 SQL 模式（schema）的底层实现，每个命名空间可以包含一组独立的关系、类型等对象，从而避免命名冲突。

| 列名       | 类型       | 引用                  | 说明                                                                 |
|------------|------------|-----------------------|----------------------------------------------------------------------|
| `oid`      | oid        |                       | 行标识符（隐藏属性，需显式选择）。                                    |
| `nspname`  | name       |                       | 命名空间名称。                                                       |
| `nspowner` | oid        | pg_authid.oid         | 命名空间的所有者。                                                   |
| `nspacl`   | ARRAY      |                       | 通过 `GRANT` 和 `REVOKE` 设置的访问权限。                            |
