---
title: gp_partition_template
---

# gp_partition_template

`gp_partition_template` 系统目录表位于 `pg_catalog` 模式中，用于描述分区表与分区层次结构中每一级定义的子分区模板之间的关系。

:::info
Apache Cloudberry 仅支持通过经典语法创建的分区表使用子分区模板。
:::

每个子分区模板的存在都依赖于下一层级模板的定义。

| 列名       | 类型            | 引用                  | 描述                                                                 |
|------------|-----------------|-----------------------|----------------------------------------------------------------------|
| `relid`    | oid             | `pg_class.oid`        | 根分区表的对象标识符。                                               |
| `level`    | smallint        |                       | 分区在层次结构中的级别。级别编号规则如下：级别 `0` 表示根分区表本身，级别 `1` 表示根分区表的直接子分区，依此类推。叶子分区的级别编号最高。 |
| `template` | `pg_node_tree`  |                       | 该层级分区定义的子分区模板的表达式表示。                             |
