<!--Thank you for contributing! -->

<!--In case of an existing issue or discussions, please reference it-->
closes: #ISSUE_Number
<!--Remove this section if no corresponding issue.-->

---

## Change logs

> Describe your change clearly, including what problem is being solved or what document is being added or updated.

## Contributor's checklist

Here are some reminders before you submit your pull request:

* Make sure that your Pull Request has a clear title and commit message. You can take the [Git commit template](https://github.com/apache/cloudberry/blob/main/.gitmessage) as a reference.
* Learn the [code contribution](https://cloudberry.apache.org/contribute/code) and [doc contribution](https://cloudberry.apache.org/contribute/doc) guides for better collaboration.
* Make sure that your changes deployment preview is successful.
* List your communications in the [GitHub Issues](https://github.com/apache/cloudberry-site/issues) or [Discussions](https://github.com/apache/cloudberry/discussions) (if has or needed).
* Feel free to ask for the [cloudberry committers](https://github.com/orgs/apache/teams/cloudberry-committers) or other people to help review and approve.
