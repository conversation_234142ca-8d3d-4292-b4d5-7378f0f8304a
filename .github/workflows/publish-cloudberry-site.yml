# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# publish-cloudberry-site.yml
#
# This workflow builds and publishes the Apache Cloudberry website to 
# cloudberry.apache.org.
# The site is built using Node.js and published to the asf-site branch,
# which Apache infrastructure then serves at cloudberry.apache.org
#
# Triggered by:
# - Push to main branch: Builds and publishes to asf-site
# - Pull requests: Builds only (no publish) to verify changes
#
# Requirements:
# - Node.js project with build script in package.json
# - .asf.yaml file in repository root
#
# Notes:
# - Publication only occurs on push events, not pull requests
# - The asf-site branch is protected - only this workflow should modify it
# - Build artifacts are placed in ./build directory

name: Publish Cloudberry Site

on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  publish:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
      
      # Install dependencies and build site
      - name: Install dependencies
        run: npm install
      - name: Build site
        run: npm run build
      - name: Copy ASF config
        run: cp .asf.yaml build/.asf.yaml

      ## Deploy to asf-staging for PR preview
      ## You can view the staging site at https://cloudberry.staged.apache.org
      #- name: Deploy to staging
      #  if: github.event_name == 'pull_request'
      #  uses: peaceiris/actions-gh-pages@v4
      #  with:
      #    github_token: ${{ secrets.GITHUB_TOKEN }}
      #    publish_dir: ./build
      #    publish_branch: asf-staging
          
      # Deploy to asf-site for production
      - name: Deploy to production
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./build
          publish_branch: asf-site
