---
title: gpactivatestandby
---

# gpactivatestandby

Activates a standby coordinator host and makes it the active coordinator for the Apache Cloudberry system.

## Synopsis

```shell
gpactivatestandby [-d <standby_coordinator_datadir>] [-f] [-a] [-q] 
    [-l <logfile_directory>]

gpactivatestandby -v 

gpactivatestandby -? | -h | --help
```

## Description

The `gpactivatestandby` utility activates a backup, standby coordinator host and brings it into operation as the active coordinator instance for a Apache Cloudberry system. The activated standby coordinator effectively becomes the Apache Cloudberry coordinator, accepting client connections on the coordinator port.

>**NOTE**
>Before running `gpactivatestandby`, be sure to run `gpstate -f` to confirm that the standby coordinator is synchronized with the current coordinator node. If synchronized, the final line of the `gpstate -f` output will look similar to this: `20230607:06:50:06:004205 gpstate:test1-m:gpadmin-[INFO]:--Sync state: sync`

When you initialize a standby coordinator, the default is to use the same port as the active coordinator. For information about the coordinator port for the standby coordinator, see [gpinitstandby](/docs/sys-utilities/gpinitstandby.md). 

You must run this utility from the coordinator host you are activating, not the failed coordinator host you are deactivating. Running this utility assumes you have a standby coordinator host configured for the system (see [gpinitstandby](/docs/sys-utilities/gpinitstandby.md)).

The utility will perform the following steps:

- Stops the synchronization process (`walreceiver`) on the standby coordinator
- Updates the system catalog tables of the standby coordinator using the logs
- Activates the standby coordinator to be the new active coordinator for the system
- Restarts the Apache Cloudberry system with the new coordinator host

A backup, standby Cloudberry coordinator host serves as a 'warm standby' in the event of the primary Cloudberry coordinator host becoming non-operational. The standby coordinator is kept up to date by transaction log replication processes (the `walsender` and `walreceiver`), which run on the primary coordinator and standby coordinator hosts and keep the data between the primary and standby coordinator hosts synchronized.

If the primary coordinator fails, the log replication process is shutdown, and the standby coordinator can be activated in its place by using the `gpactivatestandby` utility. Upon activation of the standby coordinator, the replicated logs are used to reconstruct the state of the Cloudberry coordinator host at the time of the last successfully committed transaction.

In order to use `gpactivatestandby` to activate a new primary coordinator host, the coordinator host that was previously serving as the primary coordinator cannot be running. The utility checks for a `postmaster.pid` file in the data directory of the deactivated coordinator host, and if it finds it there, it will assume the old coordinator host is still active. In some cases, you may need to remove the `postmaster.pid` file from the deactivated coordinator host data directory before running `gpactivatestandby` (for example, if the deactivated coordinator host process was terminated unexpectedly).

After activating a standby coordinator, run `ANALYZE` to update the database query statistics. For example:

```shell
psql <dbname> -c 'ANALYZE;'
```

After you activate the standby coordinator as the primary coordinator, the Apache Cloudberry system no longer has a standby coordinator configured. You might want to specify another host to be the new standby with the [gpinitstandby](/docs/sys-utilities/gpinitstandby.md) utility.

## Options

**`-a (do not prompt)`**

Do not prompt the user for confirmation.

**`-d standby_coordinator_datadir`**

The absolute path of the data directory for the coordinator host you are activating.

If this option is not specified, `gpactivatestandby` uses the value of the `COORDINATOR_DATA_DIRECTORY` environment variable setting on the coordinator host you are activating. If this option is specified, it overrides any setting of `COORDINATOR_DATA_DIRECTORY`.

If a directory cannot be determined, the utility returns an error.

**`-f (force activation)`**

Use this option to force activation of the backup coordinator host. Use this option only if you are sure that the standby and primary coordinator hosts are consistent.

**`-l logfile_directory`**

The directory to write the log file. Defaults to `~/gpAdminLogs`.

**`-q (no screen output)`**

Run in quiet mode. Command output is not displayed on the screen, but is still written to the log file.

**`-v (show utility version)`**

Displays the version, status, last updated date, and check sum of this utility.

**`-? | -h | --help (help)`**

Displays the online help.

## Example

Activate the standby coordinator host and make it the active coordinator instance for a Apache Cloudberry system (run from backup coordinator host you are activating):

```shell
gpactivatestandby -d /gpdata
```

## See also

[gpinitsystem](/docs/sys-utilities/gpinitsystem.md), [gpinitstandby](/docs/sys-utilities/gpinitstandby.md)
