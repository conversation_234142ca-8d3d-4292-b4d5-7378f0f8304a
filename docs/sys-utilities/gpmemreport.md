---
title: gpmemreport
---

# gpmemreport

Interprets the output created by the [gpmemwatcher](/docs/sys-utilities/gpmemwatcher.md) utility and generates output files in a readable format.

## Synopsis

```
gpmemreport [<GZIP_FILE>] [[-s <START>] | [--start= <START>]] [[-e <END>] | [--end= <END>]] 
        
gpmemreport --version

gpmemreport -h | --help 
```

## Description

The `gpmemreport` utility helps interpret the output file created by the [gpmemwatcher](/docs/sys-utilities/gpmemwatcher.md) utility.

When running `gpmemreport` against the `.gz` files generated by `gpmemwatcher`, it generates a series of files, where each file corresponds to a 60 second period of data collected by `gpmemwatcher` converted into a readable format.

## Options

**`-s | --start start_time`**

Indicates the start of the reporting period. Timestamp format must be `'%Y-%m-%d %H:%M:%S'`.

**`-e | --end end_time`**

Indicates the end of the reporting period. Timestamp format must be `'%Y-%m-%d %H:%M:%S'`.

**`--version`**

Displays the version of this utility.

**`-h | --help`**

Displays the online help.

## Examples

**Example 1: Extract all the files generated by `gpmemwatcher` for the Cloudberry coordinator**

Locate the output `.gz` file from `gpmemwatcher` and run `gpmemreport` against it:

```shell
$ gpmemreport cdw.ps.out.gz

>>>21:11:19:15:37:18<<<

>>>21:11:19:15:38:18<<<

>>>21:11:19:15:39:18<<<
```

Check that the generated files are listed under the current directory:

```shell
$ ls -thrl

-rw-rw-r--. 1 <USER> <GROUP> 1.2K Nov 19 15:50 20211119-153718
-rw-rw-r--. 1 <USER> <GROUP> 1.2K Nov 19 15:50 20211119-153818
-rw-rw-r--. 1 <USER> <GROUP> 1.2K Nov 19 15:50 20211119-153918
```

**Example 2: Extract the files generated by `gpmemwatcher` for the Cloudberry coordinator starting after a certain timestamp**

Locate the output `.gz` file from `gpmemwatcher` and run `gpmemreport` against it, indicating the start time as `2021-11-19 15:38:00`:

```shell
$ gpmemreport cdw.ps.out.gz --start='2021-11-19 15:38:00'

>>>21:11:19:15:37:18<<<

>>>21:11:19:15:38:18<<<

>>>21:11:19:15:39:18<<<
```

Check under the current directory that only the selected timestamp files are listed:

```shell
$ ls -thrl

-rw-rw-r--. 1 <USER> <GROUP> 1.2K Nov 19 15:50 20211119-153818
-rw-rw-r--. 1 <USER> <GROUP> 1.2K Nov 19 15:50 20211119-153918
```

## See also

[gpmemwatcher](/docs/sys-utilities/gpmemwatcher.md)
