---
title: pg_proc
---

# pg_proc

The `pg_proc` system catalog table stores information about functions (or procedures), both built-in functions and those defined by `CREATE FUNCTION`. The table contains data for aggregate and window functions as well as plain functions. If `proisagg` is true, there should be a matching row in `pg_aggregate`.

For compiled functions, both built-in and dynamically loaded, `prosrc` contains the function's C-language name (link symbol). For all other currently-known language types, `prosrc` contains the function's source text. `probin` is unused except for dynamically-loaded C functions, for which it gives the name of the shared library file containing the function.

|column|type|references|description|
|------|----|----------|-----------|
|`oid`|oid| |Row identifier (hidden attribute; must be explicitly selected).|
|`proname`|name| |Name of the function.|
|`pronamespace`|oid|pg_namespace.oid|The OID of the namespace that contains this function.|
|`proowner`|oid|pg_authid.oid|Owner of the function.|
|`prolang`|oid|pg_language.oid|Implementation language or call interface of this function.|
|`procost`|real| |Estimated execution cost (in cpu_operator_cost units); if `proretset` is `true`, identifies the cost per row returned.|
|`prorows`|real| |Estimated number of result rows (zero if not `proretset`).|
|`provariadic`|oid|pg_type.oid|Data type of the variadic array parameter's elements, or zero if the function does not have a variadic parameter.|
|`prosupport`|regproc|pg_proc.oid|Planner support function for this function|
|`prokind`|char| |`f` for a normal function, `p` for a procedure, `a` for an aggregate function, or `w` for a window function|
|`prosecdef`|boolean| |Function is a security definer (for example, a 'setuid' function).|
|`proleakproof`|boolean| |The function has no side effects. No information about the arguments is conveyed except via the return value. Any function that might throw an error depending on the values of its arguments is not leak-proof.|
|`proisstrict`|boolean| |Function returns NULL if any call argument is NULL. In that case the function will not actually be called at all. Functions that are not strict must be prepared to handle NULL inputs.|
|`proretset`|boolean| |Function returns a set (multiple values of the specified data type).|
|`provolatile`|char| |Tells whether the function's result depends only on its input arguments, or is affected by outside factors. `i` = *immutable* (always delivers the same result for the same inputs), `s` = *stable* (results (for fixed inputs) do not change within a scan), or `v` = *volatile* (results may change at any time or functions with side-effects).|
|`proparallel`|char| |Indicates whether a function can be safely run in a parallel backend, or only in non-parallel mode||
|`pronargs`|smallint| |Number of arguments.|
|`pronargdefaults`|smallint| |Number of arguments that have default values.|
|`prorettype`|oid|pg_type.oid|Data type of the return value, or null for a procedure.|
|`proargtypes`|ARRAY|pg_type.oid|An array with the data types of the function arguments. This includes only input arguments (including `INOUT` and `VARIADIC` arguments), and thus represents the call signature of the function.|
|`proallargtypes`|ARRAY|pg_type.oid|An array with the data types of the function arguments. This includes all arguments (including `OUT` and `INOUT` arguments); however, if all of the arguments are `IN` arguments, this field will be null. Note that subscripting is 1-based, whereas for historical reasons `proargtypes` is subscripted from 0.|
|`proargmodes`|ARRAY| |An array with the modes of the function arguments: `i` = `IN`, `o` = `OUT` , `b` = `INOUT`, `v` = `VARIADIC`. If all the arguments are IN arguments, this field will be null. Note that subscripts correspond to positions of `proallargtypes`, not `proargtypes`.|
|`proargnames`|ARRAY| |An array with the names of the function arguments. Arguments without a name are set to empty strings in the array. If none of the arguments have a name, this field will be null. Note that subscripts correspond to positions of `proallargtypes` not `proargtypes`.|
|`proargdefaults`|pg_node_tree| |Expression trees (in `nodeToString()` representation) for default argument values. This is a list with `pronargdefaults` elements, corresponding to the last N input arguments (i.e., the last N `proargtypes` positions). If none of the arguments have defaults, this field will be null.|
|`protrftypes`|ARRAY| |Types for which to apply transforms|
|`prosrc`|text| |This tells the function handler how to invoke the function. It might be the actual source code of the function for interpreted languages, a link symbol, a file name, or just about anything else, depending on the implementation language/call convention.|
|`probin`|text| |Additional information about how to invoke the function. Again, the interpretation is language-specific.|
|`prosqlbody`|pg_node_tree| |If `prolang` is `sql` and `prokind` is not `a` (for example, it is a plain function or procedure, not an aggregate), this field contains the pre-parsed SQL body of the function or procedure. This is primarily for internal use by the database. For functions in other languages, this field is null.|
|`proconfig`|ARRAY| |Function's local settings for run-time configuration variables.|
|`proacl`|ARRAY| |Access privileges for the function as given by `GRANT`/`REVOKE`.|
|`prodataaccess`|char| |Indicates the SQL data access mode of the function. Possible values are: `c` = CONTAINS SQL, `n` = NO SQL, `r` = READS SQL DATA, `m` = MODIFIES SQL DATA.|
|`proexeclocation`|char| |Where the function runs when it is invoked: `m` - coordinator only, `a` - any segment instance, `s` - all segment instances, `i` - initplan.|
