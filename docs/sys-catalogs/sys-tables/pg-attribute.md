---
title: pg_attribute
---

# pg_attribute

The `pg_attribute` table in the `pg_catalog` schema stores information about table columns. There will be exactly one `pg_attribute` row for every column in every table in the database. (There will also be attribute entries for indexes, and all objects that have `pg_class` entries.) The term attribute is equivalent to column.

In a dropped column's `pg_attribute` entry, `atttypid` is reset to zero, but `attlen` and the other fields copied from `pg_type` are still valid. This arrangement is needed to address the situation where the dropped column's data type was later dropped, and so there is no `pg_type` row anymore. `attlen` and the other fields can be used to interpret the contents of a row of the table.

|Column|Type|References|Description|
|------|----|----------|-----------|
|`attrelid`|oid|`pg_class.oid`|The table this column belongs to.|
|`attname`|name| |The column name.|
|`atttypid`|oid|`pg_type.oid`|The data type of this column.|
|`attstattarget`|integer| |Controls the level of detail of statistics accumulated for this column by `ANALYZE`. A zero value indicates that no statistics should be collected. A negative value says to use the system default statistics target. The exact meaning of positive values is data type-dependent. For scalar data types, it is both the target number of "most common values" to collect, and the target number of histogram bins to create.|
|`attlen`|smallint| |A copy of `pg_type.typlen` of this column's type.|
|`attnum`|smallint| |The number of the column. Ordinary columns are numbered from 1 up. System columns, such as `ctid`, have (arbitrary) negative numbers.|
|`attndims`|integer| |Number of dimensions, if the column is an array type; otherwise `0`. (Currently, the number of dimensions of an array is not enforced, so any nonzero value effectively means it is an array.)|
|`attcacheoff`|integer| |Always `-1` in storage, but when loaded into a row descriptor in memory this may be updated to cache the offset of the attribute within the row.|
|`atttypmod`|integer| |Records type-specific data supplied at table creation time (for example, the maximum length of a `varchar` column). It is passed to type-specific input functions and length coercion functions. The value will generally be `-1` for types that do not need it.|
|`attbyval`|boolean| |A copy of `pg_type.typbyval` of this column's type.|
|`attstorage`|char| |Normally a copy of `pg_type.typstorage` of this column's type. For TOAST-able data types, this can be altered after column creation to control storage policy.|
|`attalign`|char| |A copy of `pg_type.typalign` of this column's type.|
|`attnotnull`|boolean| |This represents a not-null constraint. |
| `attcompression` | char | | Compression type for this column. Valid values are `n` for none, `r` for run-length encoding, `z` for zlib, and `s` for snappy. |
|`atthasdef`|boolean| |This column has a default expression or generation expression, in which case there will be a corresponding entry in the `pg_attrdef` catalog that actually defines the value. (Check `attgenerated` to determine whether this is a default or a generation expression.)|
|`atthasmissing`|boolean| |This column has a value which is used where the column is entirely missing from the row, as happens when a column is added with a non-volatile `DEFAULT` value after the row is created. The actual value used is stored in the `attmissingval` column.|
|`attidentity`|char| |If a zero byte (''), then not an identity column. Otherwise, `a` = generated always, `d` = generated by default.|
|`attgenerated`|char| |If a zero byte (''), then not a generated column. Otherwise, `s` = stored. (Other values might be added in the future.) |
|`attisdropped`|boolean| |This column has been dropped and is no longer valid. A dropped column is still physically present in the table, but is ignored by the parser and so cannot be accessed via SQL.|
|`attislocal`|boolean| |This column is defined locally in the relation. Note that a column may be locally defined and inherited simultaneously.|
|`attinhcount`|integer| |The number of direct ancestors this column has. A column with a nonzero number of ancestors cannot be dropped nor renamed.|
|`attcollation`|oid|`pg_collation.oid`|The defined collation of the column, or zero if the is not of a collatable data type.|
|`attacl`|aclitem[]| |Column-level access privileges, if any have been granted specifically on this column.|
|`attoptions`|text[]| |Attribute-level options, as "keyword=value" strings.|
|`attfdwoptions`|text[]| |Attribute-level foreign data wrapper options, as "keyword=value" strings.|
|`attmissingval`|anyarray| |This column has a one element array containing the value used when the column is entirely missing from the row, as happens when the column is added with a non-volatile `DEFAULT` value after the row is created. The value is only used when `atthasmissing` is `true`. If there is no value the column is null.|