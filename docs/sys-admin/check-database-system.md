---
title: Check Database System
toc_max_heading_level: 5
---

# Check Apache Cloudberry System

You can check a Apache Cloudberry system using a variety of tools included with the system or available as plugins.

Observing the Apache Cloudberry system day-to-day performance helps administrators understand the system behavior, plan workflow, and troubleshoot problems. This document introduces scenarios for diagnosing database performance and activity.

<!-- Also, be sure to review [Recommended Monitoring and Maintenance Tasks](../monitoring/monitoring.html) for monitoring activities you can script to quickly detect problems in the system. -->
As a Apache Cloudberry administrator, you need to check the system for problem events such as a segment going down or running out of disk space on a segment host. The following topics describe how to check the health of a Apache Cloudberry system and examine certain state information for a Apache Cloudberry system.

- [Check system state](#check-system-state)
- [Check disk space usage](#check-disk-space-usage)
- [Check for data distribution skew](#check-for-data-distribution-skew)
<!-- - [Check for and terminate overflowed backends](#check-for-and-terminate-overflowed-backends) -->
- [View metadata information about database objects](#view-metadata-information-about-database-objects)
- [View session memory usage information](#view-session-memory-usage-information)
- [View and log per-process memory usage information](#view-and-log-per-process-memory-usage-information)
- [View query workfile usage information](#view-query-workfile-usage-information)

## Check system state

A Apache Cloudberry system is comprised of multiple PostgreSQL instances (the coordinator and segments) spanning multiple machines. To check a Apache Cloudberry system, you need to know information about the system as a whole, as well as status information of the individual instances. The `gpstate` utility provides status information about a Apache Cloudberry system.

### View coordinator and segment status and configuration

The default `gpstate` action is to check segment instances and show a brief status of the valid and failed segments. For example, to see a quick status of your Apache Cloudberry system:

```shell
gpstate
```

To see more detailed information about your Apache Cloudberry array configuration, use `gpstate` with the `-s` option:

```shell
gpstate -s
```

### View your mirroring configuration and status

If you are using mirroring for data redundancy, you might want to see the list of mirror segment instances in the system, their current synchronization status, and the mirror to primary mapping. For example, to see the mirror segments in the system and their status:

```shell
gpstate -m
```

To see the primary to mirror segment mappings:

```shell
gpstate -c
```

To see the status of the standby coordinator mirror:

```shell
gpstate -f
```

## Check disk space usage

For database administrators, checking disk space usage is crucial. Keeping coordinator and segment data directories below 70% full is the key. Although a filled disk does not corrupt data, it can stop regular database activity, and eventually force server shutdown.

You can use the `gp_disk_free` external table in the `gp_toolkit` administrative schema to check for remaining free space (in kilobytes) on the segment host file systems. For example:

```sql
SELECT * FROM gp_toolkit.gp_disk_free ORDER BY dfsegment;
```

### Check the sizing of distributed databases and tables

The `gp_toolkit` administrative schema contains several views that you can use to determine the disk space usage for a distributed Apache Cloudberry database, schema, table, or index.

#### View disk space usage for a database

To see the total size of a database (in bytes), use the `gp_size_of_database` view in the `gp_toolkit` administrative schema. For example:

```sql
SELECT * FROM gp_toolkit.gp_size_of_database ORDER BY sodddatname;
```

#### View disk space usage for a table

The `gp_toolkit` administrative schema contains several views for checking the size of a table. The table sizing views list the table by object ID (not by name). For example:

```sql
SELECT relname AS name, sotdsize AS size, sotdtoastsize 
AS toast, sotdadditionalsize AS other 
FROM gp_toolkit.gp_size_of_table_disk as sotd, pg_class 
WHERE sotd.sotdoid=pg_class.oid ORDER BY relname;
```

To check the size of a table by name, you need to look up the relation name (`relname`) in the `pg_class` table. For example, to see the size of the table `test_table`:

```sql
SELECT relname AS name, sotdsize AS size, sotdtoastsize 
AS toast, sotdadditionalsize AS other 
FROM gp_toolkit.gp_size_of_table_disk as sotd, pg_class 
WHERE sotd.sotdoid=pg_class.oid AND pg_class.relname = 'test_table'
ORDER BY relname;
```

#### View disk space usage for indexes

The `gp_toolkit` administrative schema contains a number of views for checking index sizes. To see the total size of all index(es) on a table, use the `gp_size_of_all_table_indexes` view. To see the size of a particular index, use the `gp_size_of_index` view. The index sizing views list tables and indexes by object ID (not by name). For example, to see the size of all indexes on a table:

```sql
SELECT soisize, relname as indexname
FROM pg_class, gp_toolkit.gp_size_of_index
WHERE pg_class.oid=gp_size_of_index.soioid 
AND pg_class.relkind='i';
```

To check the size of an index by name, you need to look up the relation name (`relname`) in the `pg_class` table. For example, to check the size of the index `test_index`:

```sql
SELECT pg_class.relname AS indexname, gp_toolkit.gp_size_of_index.soioid, gp_toolkit.gp_size_of_index.soisize
FROM pg_class, gp_toolkit.gp_size_of_index
WHERE pg_class.oid = gp_toolkit.gp_size_of_index.soioid
AND pg_class.relkind = 'i'
AND pg_class.relname = 'test_index';
```

## Check for data distribution skew

All tables in Apache Cloudberry are distributed, meaning their data is divided across all of the segments in the system. Unevenly distributed data might diminish query processing performance. A table's distribution policy, set at table creation time, determines how the table's rows are distributed. For information about choosing the table distribution policy, see the following topics:

- [View a table's distribution key](#view-a-tables-distribution-key)
- [View data distribution](#view-data-distribution)
- [Check for query processing skew](#check-for-query-processing-skew)

The `gp_toolkit` administrative schema also contains a number of views for checking data distribution skew on a table.

### View a table's distribution key

To see the columns used as the data distribution key for a table, you can use the `\d+` meta-command in `psql` to examine the definition of a table. For example:

```sql
=# \d+ sales

                Table "retail.sales"
 Column      |     Type     | Modifiers | Description
-------------+--------------+-----------+-------------
 sale_id     | integer      |           |
 amt         | float        |           |
 date        | date         |           |
Has OIDs: no
Distributed by: (sale_id)
```

When you create a replicated table, Apache Cloudberry stores all rows in the table on every segment. Replicated tables have no distribution key. Where the `\d+` meta-command reports the distribution key for a normally distributed table, it shows `Distributed Replicated` for a replicated table.

### View data distribution

To see the data distribution of a table's rows (the number of rows on each segment), you can run a query such as:

```sql
SELECT gp_segment_id, count(*) 
FROM <table_name> GROUP BY gp_segment_id;
```

A table is considered to have a balanced distribution if all segments have roughly the same number of rows.

:::tip
If you run this query on a replicated table, it fails because Apache Cloudberry does not permit user queries to reference the system column `gp_segment_id` (or the system columns `ctid`, `cmin`, `cmax`, `xmin`, and `xmax`) in replicated tables. Because every segment has all of the tables' rows, replicated tables are evenly distributed by definition.
:::

### Check for query processing skew

When a query is being processed, all segments should have equal workloads to ensure the best possible performance. If you identify a poorly-performing query, you might need to investigate further using the `EXPLAIN` command.<!-- For information about using the `EXPLAIN` command and query profiling, see [Query Profiling](../query/topics/query-profiling.html).-->

Query processing workload can be skewed if the table's data distribution policy and the query predicates are not well matched. To check for processing skew, you can run a query such as:

```sql
=# SELECT gp_segment_id, count(*) FROM <table_name>
   WHERE <column>='<value>' GROUP BY gp_segment_id;
```

This will show the number of rows returned by segment for the given `WHERE` predicate.

As noted in [Viewing Data Distribution](#view-data-distribution), this query will fail if you run it on a replicated table because you cannot reference the `gp_segment_id` system column in a query on a replicated table.

#### Avoid an extreme skew warning

You might receive the following warning message while running a query that performs a hash join operation:

`Extreme skew in the innerside of Hashjoin`

This occurs when the input to a hash join operator is skewed. It does not prevent the query from completing successfully. You can follow these steps to avoid skew in the plan:

1. Ensure that all fact tables are analyzed.
2. Verify that any populated temporary table used by the query is analyzed.
3. View the `EXPLAIN ANALYZE` plan for the query and look for the following:

    - If there are scans with multi-column filters that are producing more rows than estimated, then set the `gp_selectivity_damping_factor` server configuration parameter to `2` or higher and retest the query.
    - If the skew occurs while joining a single fact table that is relatively small (less than 5000 rows), set the `gp_segments_for_planner` server configuration parameter to `1` and retest the query.

4. Check whether the filters applied in the query match distribution keys of the base tables. If the filters and distribution keys are the same, consider redistributing some of the base tables with different distribution keys.
5. Check the cardinality of the join keys. If they have low cardinality, try to rewrite the query with different joining columns or additional filters on the tables to reduce the number of rows. These changes could change the query semantics.

<!-- ## Check for and terminate overflowed backends

Sub-transaction overflow occurs when a Apache Cloudberry backend creates more than 64 sub-transactions, resulting in a high lookup cost for visibility checks. This slows query performance, but even more so when it occurs in combination with long-running transactions, which result in still more lookups. Terminating sub-overflowed backends and/or backends with long-running transactions can help prevent and alleviate performance problems.

Apache Cloudberry includes a view (`gp_suboverflowed_backend`) that is run over a user-defined function to help users query for sub-overflowed backends. Users can use segment id and process id information reported in the view to terminate the offending backends, thereby preventing degradation of performance.

### Steps for identifying and terminating overflowed backends

Follow these steps below to identify and terminate overflowed backends.

1. Select all from the view:

    ```sql
    select * from gp_suboverflowed_backend;
    ```

    This returns output similar to the following:
    
    ```sql
   segid |   pids    
    -------+-----------
    -1 | 
     0 | {1731513}
     1 | {1731514}
     2 | {1731515}
   (4 rows)
    ```

2. Connect to the database in utility mode and query `pg_stat_activity` to return the session id for the process id in the output for a segment. For example: 

    ```sql
    select sess_id from pg_stat_activity where pid=1731513;
    ```

    ```sql
    sess_id 
    ---------
      10
    (1 row)
    ```

3. Terminate the session, which will terminate all associated backends on all segments:

    ```sql
    select pg_terminate_backend(pid) from pg_stat_activity where sess_id=10;
    ```

4. Verify that there are no more sub-overflowed backends:

    ```sql
    select * from gp_suboverflowed_backend;
    ```
    
    ```sql
   segid |   pids    
    -------+-----------
    -1 | 
     0 |
     1 | 
     2 | 
    (4 rows)
    ``` -->

## View metadata information about database objects

Apache Cloudberry tracks various metadata information in its system catalogs about the objects stored in a database, such as tables, views, indexes and so on, as well as global objects such as roles and tablespaces.

### View the last operation performed

You can use the system views `pg_stat_operations` and `pg_stat_partition_operations` to look up actions performed on an object, such as a table. For example, to see the actions performed on a table, such as when it was created and when it was last vacuumed and analyzed:

```sql
=> SELECT schemaname as schema, objname as table, 
   usename as role, actionname as action, 
   subtype as type, statime as time 
   FROM pg_stat_operations 
   WHERE objname='test_table';

 schema |   table    |  role   | action  | type  |             time              
--------+------------+---------+---------+-------+-------------------------------
 public | test_table | gpadmin | CREATE  | TABLE | 2024-01-16 15:26:36.998256+08
 public | test_table | gpadmin | VACUUM  |       | 2024-01-16 15:26:42.073407+08
 public | test_table | gpadmin | ANALYZE |       | 2024-01-16 15:26:45.97546+08
(3 rows)
```

### View the definition of an object

To see the definition of an object, such as a table or view, you can use the `\d+` meta-command when working in `psql`. For example, to see the definition of a table:

```sql
\d+ <mytable>
```

## View session memory usage information

You can create and use the `session_level_memory_consumption` view that provides information about the current memory utilization for sessions that are running queries on Apache Cloudberry. The view contains session information and information such as the database that the session is connected to, the query that the session is currently running, and memory consumed by the session processes.

- [Create the `session_level_memory_consumption` view](#create-the-session_level_memory_consumption-view)
- [About the `session_level_memory_consumption` view](#about-the-session_level_memory_consumption-view)

### Create the `session_level_memory_consumption` view

To create the `session_state.session_level_memory_consumption` view in a Apache Cloudberry, run the command `CREATE EXTENSION gp_internal_tools;` once for each database. For example, to install the view in the database `testdb`, use this command:

```shell
psql -d testdb -c "CREATE EXTENSION gp_internal_tools;"
```

### About the `session_level_memory_consumption` view

The `session_state.session_level_memory_consumption` view provides information about memory consumption and idle time for sessions that are running SQL queries.

When resource queue-based resource management is active, the column `is_runaway` indicates whether Apache Cloudberry considers the session a runaway session based on the` vmem` memory consumption of the session's queries. Under the resource queue-based resource management scheme, Apache Cloudberry considers the session a runaway when the queries consume an excessive amount of memory. The Apache Cloudberry server configuration parameter `runaway_detector_activation_percent` controls the conditions under which Apache Cloudberry considers a session a runaway session.

The `is_runaway`, `runaway_vmem_mb`, and `runaway_command_cnt` columns are not applicable when resource group-based resource management is active.

|column|type|references|description|
|------|----|----------|-----------|
|`datname`|name| |Name of the database that the session is connected to.|
|`sess_id`|integer| |Session ID.|
|`usename`|name| |Name of the session user.|
|`query`|text| |Current SQL query that the session is running.|
|`segid`|integer| |Segment ID.|
|`vmem_mb`|integer| |Total vmem memory usage for the session in MB.|
|`is_runaway`|boolean| |Session is marked as runaway on the segment.|
|`qe_count`|integer| |Number of query processes for the session.|
|`active_qe_count`|integer| |Number of active query processes for the session.|
|`dirty_qe_count`|integer| |Number of query processes that have not yet released their memory. The value is `-1` for sessions that are not running.|
|`runaway_vmem_mb`|integer| |Amount of vmem memory that the session was consuming when it was marked as a runaway session.|
|`runaway_command_cnt`|integer| |Command count for the session when it was marked as a runaway session.|
|`idle_start`|timestamptz| |The last time a query process in this session became idle.|

## View and log per-process memory usage information

Apache Cloudberry allocates all memory within memory contexts. Memory contexts are a convenient way to manage memory that needs to live for differing amounts of time. Destroying a context releases all of the memory that was allocated in it.

Tracking the amount of memory used by a server process or a long-running query can help detect the source of a potential out-of-memory condition. Apache Cloudberry provides a system view and administration functions that you can use for this purpose.

### About the `pg_backend_memory_contexts` view

To display the memory usage of all active memory contexts in the server process attached to the current session, use the `pg_backend_memory_contexts` system view. This view is restricted to superusers, but access might be granted to other roles.

``` sql
SELECT * FROM pg_backend_memory_contexts;
```

### About the memory context admin functions

You can use the system administration function `pg_log_backend_memory_contexts()` to instruct Apache Cloudberry to dump the memory usage of other sessions running on the coordinator host into the server log. Execution of this function is restricted to superusers only, and cannot be granted to other roles.

The signature of `pg_log_backend_memory_contexts()` function follows:

```
pg_log_backend_memory_contexts( pid integer )
```

where `pid` identifies the process whose memory contexts you want dumped.

`pg_log_backend_memory_contexts()` returns `t` when memory context logging is successfully activated for the process on the local host. When logging is activated, Apache Cloudberry writes one message to the log for each memory context at the `LOG` message level. The log messages appear in the server log based on the log configuration set; refer to [Error Reporting and Logging](https://www.postgresql.org/docs/14/runtime-config-logging.html) in the PostgreSQL documentation for more information. *The memory context log messages are not sent to the client*.

<!-- Memory context logging functions that dump memory usage across all Cloudberry segments, or dump usage for a specific segment are named `gp_log_backend_memory_contexts()`.

`gp_log_backend_memory_contexts()` has two signatures:

```
gp_log_backend_memory_contexts( sess_id integer )
gp_log_backend_memory_contexts( sess_id integer, contentId integer )
```

where `sess_id` is the Apache Cloudberry identifier assigned to the session (typically obtained from the `pg_stat_activity` view), and `contentID` in the second signature identifies the segment instance of interest.

When you invoke `gp_log_backend_memory_contexts()` on the Apache Cloudberry coordinator host, it invokes `pg_log_backend_memory_contexts()` on the individual segments, which in turn triggers a memory usage dump to each segment log. The functions return an integer identifying the number of segments on which memory context logging was successfully activated. -->

### Sample log messages

The command:

``` sql
SELECT pg_log_backend_memory_contexts( pg_backend_pid() );
```

triggered the dumping of the following (subset of) memory context messages to the local server log file:

```
2024-01-16 16:45:57.228512 UTC,"gpadmin","testdb",p16389,th-557447104,"[local]",,2024-01-16 15:57:32 UTC,0,,cmd10,seg-1,,,,sx1,"LOG","00000","logging memory contexts of PID 16389",,,,,,"SELECT pg_log_backend_memory_contexts(pg_backend_pid());",0,,"mcxt.c",1278,
2024-01-16 16:45:57.229275 UTC,"gpadmin","testdb",p16389,th-557447104,"[local]",,2024-01-16 15:57:32 UTC,0,,cmd10,seg-1,,,,sx1,"LOG","00000","level: 0; TopMemoryContext: 108384 total in 6 blocks; 23248 free (21 chunks); 85136 used",,,,,,,0,,"mcxt.c",884,
2024-01-16 16:45:57.229822 UTC,"gpadmin","testdb",p16389,th-557447104,"[local]",,2024-01-16 15:57:32 UTC,0,,cmd10,seg-1,,,,sx1,"LOG","00000","level: 1; pgstat TabStatusArray lookup hash table: 8192 total in 1 blocks; 1416 free (0 chunks); 6776 used",,,,,,,0,,"mcxt.c",884,
2024-01-16 16:45:57.230387 UTC,"gpadmin","testdb",p16389,th-557447104,"[local]",,2024-01-16 15:57:32 UTC,0,,cmd10,seg-1,,,,sx1,"LOG","00000","level: 1; TopTransactionContext: 8192 total in 1 blocks; 7576 free (1 chunks); 616 used",,,,,,,0,,"mcxt.c",884,
2024-01-16 16:45:57.230961 UTC,"gpadmin","testdb",p16389,th-557447104,"[local]",,2024-01-16 15:57:32 UTC,0,,cmd10,seg-1,,,,sx1,"LOG","00000","level: 1; TableSpace cache: 8192 total in 1 blocks; 2056 free (0 chunks); 6136 used",,,,,,,0,,"mcxt.c",884,
```

## View query workfile usage information

The Apache Cloudberry administrative schema `gp_toolkit` contains views that display information about Apache Cloudberry workfiles. Apache Cloudberry creates workfiles on disk if it does not have sufficient memory to run the query in memory. This information can be used for troubleshooting and tuning queries. The information in the views can also be used to specify the values for the Apache Cloudberry configuration parameters `gp_workfile_limit_per_query` and `gp_workfile_limit_per_segment`.

These are the views in the schema `gp_toolkit`:

- The `gp_workfile_entries` view contains one row for each operator using disk space for workfiles on a segment at the current time.
- The `gp_workfile_usage_per_query` view contains one row for each query using disk space for workfiles on a segment at the current time.
- The `gp_workfile_usage_per_segment` view contains one row for each segment. Each row displays the total amount of disk space used for workfiles on the segment at the current time.

For information about using `gp_toolkit`, see [Using `gp_toolkit`](#use-gp_toolkit).

## View the database server log files

Every database instance in Apache Cloudberry (coordinator and segments) runs a PostgreSQL database server with its own server log file. Log files are created in the `log` directory of the coordinator and each segment data directory.

### Log file format

The server log files are written in comma-separated values (CSV) format. Some log entries will not have values for all log fields. For example, only log entries associated with a query worker process will have the `slice_id` populated. You can identify related log entries of a particular query by the query's session identifier (`gp_session_id`) and command identifier (`gp_command_count`).

The following fields are written to the log:

|Number|Field Name|Data Type|Description|
|------|----------|---------|-----------|
|1|event\_time|timestamp with time zone|Time that the log entry was written to the log|
|2|user\_name|varchar(100)|The database user name|
|3|database\_name|varchar(100)|The database name|
|4|process\_id|varchar(10)|The system process ID (prefixed with "p")|
|5|thread\_id|varchar(50)|The thread count (prefixed with "th")|
|6|remote\_host|varchar(100)|On the coordinator, the hostname/address of the client machine. On the segment, the hostname/address of the coordinator.|
|7|remote\_port|varchar(10)|The segment or coordinator port number|
|8|session\_start\_time|timestamp with time zone|Time session connection was opened|
|9|transaction\_id|int|Top-level transaction ID on the coordinator. This ID is the parent of any subtransactions.|
|10|gp\_session\_id|text|Session identifier number (prefixed with "con")|
|11|gp\_command\_count|text|The command number within a session (prefixed with "cmd")|
|12|gp\_segment|text|The segment content identifier (prefixed with "seg" for primaries or "mir" for mirrors). The coordinator always has a content ID of -1.|
|13|slice\_id|text|The slice ID (portion of the query plan being executed)|
|14|distr\_tranx\_id|text|Distributed transaction ID|
|15|local\_tranx\_id|text|Local transaction ID|
|16|sub\_tranx\_id|text|Subtransaction ID|
|17|event\_severity|varchar(10)|Values include: LOG, ERROR, FATAL, PANIC, DEBUG1, DEBUG2|
|18|sql\_state\_code|varchar(10)|SQL state code associated with the log message|
|19|event\_message|text|Log or error message text|
|20|event\_detail|text|Detail message text associated with an error or warning message|
|21|event\_hint|text|Hint message text associated with an error or warning message|
|22|internal\_query|text|The internally-generated query text|
|23|internal\_query\_pos|int|The cursor index into the internally-generated query text|
|24|event\_context|text|The context in which this message gets generated|
|25|debug\_query\_string|text|User-supplied query string with full detail for debugging. This string can be modified for internal use.|
|26|error\_cursor\_pos|int|The cursor index into the query string|
|27|func\_name|text|The function in which this message is generated|
|28|file\_name|text|The internal code file where the message originated|
|29|file\_line|int|The line of the code file where the message originated|
|30|stack\_trace|text|Stack trace text associated with this message|

### Search the Apache Cloudberry server log files

Apache Cloudberry provides a utility called `gplogfilter` can search through a Apache Cloudberry log file for entries matching the specified criteria. By default, this utility searches through the Apache Cloudberry coordinator log file in the default logging location. For example, to display the last three lines of each of the log files under the coordinator directory:

```shell
gplogfilter -n 3
```

<!-- To search through all segment log files at the same time, run `gplogfilter` through the `gpssh` utility. For example, to display the last 3 lines of each segment log file:

```shell
gpssh -f <seg_host_file>
```

```shell
=> source /usr/local/cloudberry/cloudberry-env.sh
=> gplogfilter -n 3 <seg_host_file>
``` -->

## Use `gp_toolkit`

Use the Apache Cloudberry administrative schema `gp_toolkit` to query the system catalogs, log files, and operating environment for system status information. The `gp_toolkit` schema contains several views you can access using SQL commands. The `gp_toolkit` schema is accessible to all database users. Some objects require superuser permissions. Use a command similar to the following to add the `gp_toolkit` schema to your schema search path:

```sql
=> ALTER ROLE myrole SET search_path TO myschema,gp_toolkit;
```

## SQL standard error codes

The following table lists all the defined error codes. Some are not used, but are defined by the SQL standard. The error classes are also shown. For each error class there is a standard error code having the last 3 characters `000`. This code is used only for error conditions that fall within the class but do not have any more-specific code assigned.

The PL/pgSQL condition name for each error code is the same as the phrase shown in the table, with underscores substituted for spaces. For example, code 22012, DIVISION BY ZERO, has condition name `DIVISION_BY_ZERO`. Condition names can be written in either upper or lower case.

:::tip
**How to view error codes**

When you execute SQL queries or perform other database operations in Apache Cloudberry, and an error occurs, the system returns an error message. However, this standard error message may not directly display the SQLSTATE error code. Here are some methods to view these error codes:

- Use PL/pgSQL exception handling. For example:

    ```sql
    DO $$
    BEGIN
    -- Replaces the following SQL with your query
    EXECUTE 'Your SQL Query';
    EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error code: %', SQLSTATE;
    END
    $$;
    ```

- Check the database log. Apache Cloudberry records detailed error information, including error codes, in its log files. Depending on your system setup, you can check the log files on the database server for this information.

- Use advanced database client tools. Some advanced database client or management tools may offer more detailed error reporting features that can directly display SQLSTATE error codes.

Note:

Not all errors have a specific SQLSTATE error code. Some errors might only have a general error class code, like 'XX000' for an internal error.

When using PL/pgSQL exception handling, ensure that your SQL query statement is correctly formatted as a string, especially when executing SQL statements dynamically.

PL/pgSQL does not recognize warning, as opposed to error, condition names; those are classes 00, 01, and 02.
:::


|Error Code|Meaning|Constant|
|----------|-------|--------|
|**Class 00**— Successful Completion|
|00000|SUCCESSFUL COMPLETION|successful_completion|
|Class 01 — Warning|
|01000|WARNING|warning|
|0100C|DYNAMIC RESULT SETS RETURNED|dynamic\_result\_sets\_returned|
|01008|IMPLICIT ZERO BIT PADDING|implicit\_zero\_bit\_padding|
|01003|NULL VALUE ELIMINATED IN SET FUNCTION|null\_value\_eliminated\_in\_set\_function|
|01007|PRIVILEGE NOT GRANTED|privilege\_not\_granted|
|01006|PRIVILEGE NOT REVOKED|privilege\_not\_revoked|
|01004|STRING DATA RIGHT TRUNCATION|string\_data\_right\_truncation|
|01P01|DEPRECATED FEATURE|deprecated\_feature|
|**Class 02** — No Data (this is also a warning class per the SQL standard)|
|02000|NO DATA|no\_data|
|02001|NO ADDITIONAL DYNAMIC RESULT SETS RETURNED|no\_additional\_dynamic\_result\_sets\_returned|
|**Class 03** — SQL Statement Not Yet Complete|
|03000|SQL STATEMENT NOT YET COMPLETE|sql\_statement\_not\_yet\_complete|
|**Class 08** — Connection Exception|
|08000|CONNECTION EXCEPTION|connection\_exception|
|08003|CONNECTION DOES NOT EXIST|connection\_does\_not\_exist|
|08006|CONNECTION FAILURE|connection\_failure|
|08001|SQLCLIENT UNABLE TO ESTABLISH SQLCONNECTION|sqlclient\_unable\_to\_establish\_sqlconnection|
|08004|SQLSERVER REJECTED ESTABLISHMENT OF SQLCONNECTION|sqlserver\_rejected\_establishment\_of\_sqlconnection|
|08007|TRANSACTION RESOLUTION UNKNOWN|transaction\_resolution\_unknown|
|08P01|PROTOCOL VIOLATION|protocol\_violation|
|**Class 09** — Triggered Action Exception|
|09000|TRIGGERED ACTION EXCEPTION|triggered\_action\_exception|
|**Class 0A** — Feature Not Supported|
|0A000|FEATURE NOT SUPPORTED|feature\_not\_supported|
|**Class 0B** — Invalid Transaction Initiation|
|0B000|INVALID TRANSACTION INITIATION|invalid\_transaction\_initiation|
|**Class 0F** — Locator Exception|
|0F000|LOCATOR EXCEPTION|locator\_exception|
|0F001|INVALID LOCATOR SPECIFICATION|invalid\_locator\_specification|
|**Class 0L** — Invalid Grantor|
|0L000|INVALID GRANTOR|invalid\_grantor|
|0LP01|INVALID GRANT OPERATION|invalid\_grant\_operation|
|**Class 0P** — Invalid Role Specification|
|0P000|INVALID ROLE SPECIFICATION|invalid\_role\_specification|
|**Class 21** — Cardinality Violation|
|21000|CARDINALITY VIOLATION|cardinality\_violation|
|**Class 22** — Data Exception|
|22000|DATA EXCEPTION|data\_exception|
|2202E|ARRAY SUBSCRIPT ERROR|array\_subscript\_error|
|22021|CHARACTER NOT IN REPERTOIRE|character\_not\_in\_repertoire|
|22008|DATETIME FIELD OVERFLOW|datetime\_field\_overflow|
|22012|DIVISION BY ZERO|division\_by\_zero|
|22005|ERROR IN ASSIGNMENT|error\_in\_assignment|
|2200B|ESCAPE CHARACTER CONFLICT|escape\_character\_conflict|
|22022|INDICATOR OVERFLOW|indicator\_overflow|
|22015|INTERVAL FIELD OVERFLOW|interval\_field\_overflow|
|2201E|INVALID ARGUMENT FOR LOGARITHM|invalid\_argument\_for\_logarithm|
|2201F|INVALID ARGUMENT FOR POWER FUNCTION|invalid\_argument\_for\_power\_function|
|2201G|INVALID ARGUMENT FOR WIDTH BUCKET FUNCTION|invalid\_argument\_for\_width\_bucket\_function|
|22018|INVALID CHARACTER VALUE FOR CAST|invalid\_character\_value\_for\_cast|
|22007|INVALID DATETIME FORMAT|invalid\_datetime\_format|
|22019|INVALID ESCAPE CHARACTER|invalid\_escape\_character|
|2200D|INVALID ESCAPE OCTET|invalid\_escape\_octet|
|22025|INVALID ESCAPE SEQUENCE|invalid\_escape\_sequence|
|22P06|NONSTANDARD USE OF ESCAPE CHARACTER|nonstandard\_use\_of\_escape\_character|
|22010|INVALID INDICATOR PARAMETER VALUE|invalid\_indicator\_parameter\_value|
|22020|INVALID LIMIT VALUE|invalid\_limit\_value|
|22023|INVALID PARAMETER VALUE|invalid\_parameter\_value|
|2201B|INVALID REGULAR EXPRESSION|invalid\_regular\_expression|
|22009|INVALID TIME ZONE DISPLACEMENT VALUE|invalid\_time\_zone\_displacement\_value|
|2200C|INVALID USE OF ESCAPE CHARACTER|invalid\_use\_of\_escape\_character|
|2200G|MOST SPECIFIC TYPE MISMATCH|most\_specific\_type\_mismatch|
|22004|NULL VALUE NOT ALLOWED|null\_value\_not\_allowed|
|22002|NULL VALUE NO INDICATOR PARAMETER|null\_value\_no\_indicator\_parameter|
|22003|NUMERIC VALUE OUT OF RANGE|numeric\_value\_out\_of\_range|
|22026|STRING DATA LENGTH MISMATCH|string\_data\_length\_mismatch|
|22001|STRING DATA RIGHT TRUNCATION|string\_data\_right\_truncation|
|22011|SUBSTRING ERROR|substring\_error|
|22027|TRIM ERROR|trim\_error|
|22024|UNTERMINATED C STRING|unterminated\_c\_string|
|2200F|ZERO LENGTH CHARACTER STRING|zero\_length\_character\_string|
|22P01|FLOATING POINT EXCEPTION|floating\_point\_exception|
|22P02|INVALID TEXT REPRESENTATION|invalid\_text\_representation|
|22P03|INVALID BINARY REPRESENTATION|invalid\_binary\_representation|
|22P04|BAD COPY FILE FORMAT|bad\_copy\_file\_format|
|22P05|UNTRANSLATABLE CHARACTER|untranslatable\_character|
|**Class 23** — Integrity Constraint Violation|
|23000|INTEGRITY CONSTRAINT VIOLATION|integrity\_constraint\_violation|
|23001|RESTRICT VIOLATION|restrict\_violation|
|23502|NOT NULL VIOLATION|not\_null\_violation|
|23503|FOREIGN KEY VIOLATION|foreign\_key\_violation|
|23505|UNIQUE VIOLATION|unique\_violation|
|23514|CHECK VIOLATION|check\_violation|
|**Class 24** — Invalid Cursor State|
|24000|INVALID CURSOR STATE|invalid\_cursor\_state|
|**Class 25** — Invalid Transaction State|
|25000|INVALID TRANSACTION STATE|invalid\_transaction\_state|
|25001|ACTIVE SQL TRANSACTION|active\_sql\_transaction|
|25002|BRANCH TRANSACTION ALREADY ACTIVE|branch\_transaction\_already\_active|
|25008|HELD CURSOR REQUIRES SAME ISOLATION LEVEL|held\_cursor\_requires\_same\_isolation\_level|
|25003|INAPPROPRIATE ACCESS MODE FOR BRANCH TRANSACTION|inappropriate\_access\_mode\_for\_branch\_transaction|
|25004|INAPPROPRIATE ISOLATION LEVEL FOR BRANCH TRANSACTION|inappropriate\_isolation\_level\_for\_branch\_transaction|
|25005|NO ACTIVE SQL TRANSACTION FOR BRANCH TRANSACTION|no\_active\_sql\_transaction\_for\_branch\_transaction|
|25006|READ ONLY SQL TRANSACTION|read\_only\_sql\_transaction|
|25007|SCHEMA AND DATA STATEMENT MIXING NOT SUPPORTED|schema\_and\_data\_statement\_mixing\_not\_supported|
|25P01|NO ACTIVE SQL TRANSACTION|no\_active\_sql\_transaction|
|25P02|IN FAILED SQL TRANSACTION|in\_failed\_sql\_transaction|
|**Class 26** — Invalid SQL Statement Name|
|26000|INVALID SQL STATEMENT NAME|invalid\_sql\_statement\_name|
|**Class 27** — Triggered Data Change Violation|
|27000|TRIGGERED DATA CHANGE VIOLATION|triggered\_data\_change\_violation|
|**Class 28** — Invalid Authorization Specification|
|28000|INVALID AUTHORIZATION SPECIFICATION|invalid\_authorization\_specification|
|**Class 2B** — Dependent Privilege Descriptors Still Exist|
|2B000|DEPENDENT PRIVILEGE DESCRIPTORS STILL EXIST|dependent\_privilege\_descriptors\_still\_exist|
|2BP01|DEPENDENT OBJECTS STILL EXIST|dependent\_objects\_still\_exist|
|**Class 2D** — Invalid Transaction Termination|
|2D000|INVALID TRANSACTION TERMINATION|invalid\_transaction\_termination|
|**Class 2F** — SQL Routine Exception|
|2F000|SQL ROUTINE EXCEPTION|sql\_routine\_exception|
|2F005|FUNCTION EXECUTED NO RETURN STATEMENT|function\_executed\_no\_return\_statement|
|2F002|MODIFYING SQL DATA NOT PERMITTED|modifying\_sql\_data\_not\_permitted|
|2F003|PROHIBITED SQL STATEMENT ATTEMPTED|prohibited\_sql\_statement\_attempted|
|2F004|READING SQL DATA NOT PERMITTED|reading\_sql\_data\_not\_permitted|
|**Class 34** — Invalid Cursor Name|
|34000|INVALID CURSOR NAME|invalid\_cursor\_name|
|**Class 38** — External Routine Exception|
|38000|EXTERNAL ROUTINE EXCEPTION|external\_routine\_exception|
|38001|CONTAINING SQL NOT PERMITTED|containing\_sql\_not\_permitted|
|38002|MODIFYING SQL DATA NOT PERMITTED|modifying\_sql\_data\_not\_permitted|
|38003|PROHIBITED SQL STATEMENT ATTEMPTED|prohibited\_sql\_statement\_attempted|
|38004|READING SQL DATA NOT PERMITTED|reading\_sql\_data\_not\_permitted|
|**Class 39** — External Routine Invocation Exception|
|39000|EXTERNAL ROUTINE INVOCATION EXCEPTION|external\_routine\_invocation\_exception|
|39001|INVALID SQLSTATE RETURNED|invalid\_sqlstate\_returned|
|39004|NULL VALUE NOT ALLOWED|null\_value\_not\_allowed|
|39P01|TRIGGER PROTOCOL VIOLATED|trigger\_protocol\_violated|
|39P02|SRF PROTOCOL VIOLATED|srf\_protocol\_violated|
|**Class 3B** — Savepoint Exception|
|3B000|SAVEPOINT EXCEPTION|savepoint\_exception|
|3B001|INVALID SAVEPOINT SPECIFICATION|invalid\_savepoint\_specification|
|**Class 3D** — Invalid Catalog Name|
|3D000|INVALID CATALOG NAME|invalid\_catalog\_name|
|**Class 3F** — Invalid Schema Name|
|3F000|INVALID SCHEMA NAME|invalid\_schema\_name|
|**Class 40** — Transaction Rollback|
|40000|TRANSACTION ROLLBACK|transaction\_rollback|
|40002|TRANSACTION INTEGRITY CONSTRAINT VIOLATION|transaction\_integrity\_constraint\_violation|
|40001|SERIALIZATION FAILURE|serialization\_failure|
|40003|STATEMENT COMPLETION UNKNOWN|statement\_completion\_unknown|
|40P01|DEADLOCK DETECTED|deadlock\_detected|
|**Class 42** — Syntax Error or Access Rule Violation|
|42000|SYNTAX ERROR OR ACCESS RULE VIOLATION|syntax\_error\_or\_access\_rule\_violation|
|42601|SYNTAX ERROR|syntax\_error|
|42501|INSUFFICIENT PRIVILEGE|insufficient\_privilege|
|42846|CANNOT COERCE|cannot\_coerce|
|42803|GROUPING ERROR|grouping\_error|
|42830|INVALID FOREIGN KEY|invalid\_foreign\_key|
|42602|INVALID NAME|invalid\_name|
|42622|NAME TOO LONG|name\_too\_long|
|42939|RESERVED NAME|reserved\_name|
|42804|DATATYPE MISMATCH|datatype\_mismatch|
|42P18|INDETERMINATE DATATYPE|indeterminate\_datatype|
|42809|WRONG OBJECT TYPE|wrong\_object\_type|
|42703|UNDEFINED COLUMN|undefined\_column|
|42883|UNDEFINED FUNCTION|undefined\_function|
|42P01|UNDEFINED TABLE|undefined\_table|
|42P02|UNDEFINED PARAMETER|undefined\_parameter|
|42704|UNDEFINED OBJECT|undefined\_object|
|42701|DUPLICATE COLUMN|duplicate\_column|
|42P03|DUPLICATE CURSOR|duplicate\_cursor|
|42P04|DUPLICATE DATABASE|duplicate\_database|
|42723|DUPLICATE FUNCTION|duplicate\_function|
|42P05|DUPLICATE PREPARED STATEMENT|duplicate\_prepared\_statement|
|42P06|DUPLICATE SCHEMA|duplicate\_schema|
|42P07|DUPLICATE TABLE|duplicate\_table|
|42712|DUPLICATE ALIAS|duplicate\_alias|
|42710|DUPLICATE OBJECT|duplicate\_object|
|42702|AMBIGUOUS COLUMN|ambiguous\_column|
|42725|AMBIGUOUS FUNCTION|ambiguous\_function|
|42P08|AMBIGUOUS PARAMETER|ambiguous\_parameter|
|42P09|AMBIGUOUS ALIAS|ambiguous\_alias|
|42P10|INVALID COLUMN REFERENCE|invalid\_column\_reference|
|42611|INVALID COLUMN DEFINITION|invalid\_column\_definition|
|42P11|INVALID CURSOR DEFINITION|invalid\_cursor\_definition|
|42P12|INVALID DATABASE DEFINITION|invalid\_database\_definition|
|42P13|INVALID FUNCTION DEFINITION|invalid\_function\_definition|
|42P14|INVALID PREPARED STATEMENT DEFINITION|invalid\_prepared\_statement\_definition|
|42P15|INVALID SCHEMA DEFINITION|invalid\_schema\_definition|
|42P16|INVALID TABLE DEFINITION|invalid\_table\_definition|
|42P17|INVALID OBJECT DEFINITION|invalid\_object\_definition|
|**Class 44** — WITH CHECK OPTION Violation|
|44000|WITH CHECK OPTION VIOLATION|with\_check\_option\_violation|
|**Class 53** — Insufficient Resources|
|53000|INSUFFICIENT RESOURCES|insufficient\_resources|
|53100|DISK FULL|disk\_full|
|53200|OUT OF MEMORY|out\_of\_memory|
|53300|TOO MANY CONNECTIONS|too\_many\_connections|
|**Class 54** — Program Limit Exceeded|
|54000|PROGRAM LIMIT EXCEEDED|program\_limit\_exceeded|
|54001|STATEMENT TOO COMPLEX|statement\_too\_complex|
|54011|TOO MANY COLUMNS|too\_many\_columns|
|54023|TOO MANY ARGUMENTS|too\_many\_arguments|
|**Class 55** — Object Not In Prerequisite State|
|55000|OBJECT NOT IN PREREQUISITE STATE|object\_not\_in\_prerequisite\_state|
|55006|OBJECT IN USE|object\_in\_use|
|55P02|CANT CHANGE RUNTIME PARAM|cant\_change\_runtime\_param|
|55P03|LOCK NOT AVAILABLE|lock\_not\_available|
|**Class 57** — Operator Intervention|
|57000|OPERATOR INTERVENTION|operator\_intervention|
|57014|QUERY CANCELED|query\_canceled|
|57P01|ADMIN SHUTDOWN|admin\_shutdown|
|57P02|CRASH SHUTDOWN|crash\_shutdown|
|57P03|CANNOT CONNECT NOW|cannot\_connect\_now|
|**Class 58** — System Error (errors external to Apache Cloudberry )|
|58030|IO ERROR|io\_error|
|58P01|UNDEFINED FILE|undefined\_file|
|58P02|DUPLICATE FILE|duplicate\_file|
|Class F0 — Configuration File Error|
|F0000|CONFIG FILE ERROR|config\_file\_error|
|F0001|LOCK FILE EXISTS|lock\_file\_exists|
|**Class P0** — PL/pgSQL Error|
|P0000|PLPGSQL ERROR|plpgsql\_error|
|P0001|RAISE EXCEPTION|raise\_exception|
|P0002|NO DATA FOUND|no\_data\_found|
|P0003|TOO MANY ROWS|too\_many\_rows|
|**Class XX** — Internal Error|
|XX000|INTERNAL ERROR|internal\_error|
|XX001|DATA CORRUPTED|data\_corrupted|
|XX002|INDEX CORRUPTED|index\_corrupted|
