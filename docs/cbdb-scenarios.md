---
title: User Scenario<PERSON>
---

This document introduces the use cases of Apache Cloudberry.

**Scenario 1: Batch processing data warehouse offline and building data marts**

- Builds high-performance Apache Cloudberry warehouses and data marts for storing and querying large-scale datasets. This includes Operational Data Store (ODS), Data Warehouse Detail (DWD), and Data Warehouse Summary (DWS). Supports building source model, normalization model, dimension tables, fact tables, and more, with multiple ways to load source data into the data warehouse.
- Supports multiple types of data processing.
- Supports building data warehouse and data marts with high concurrency, high performance, and low maintenance cost.
- Supports complex data analysis and query needs, including data aggregation, multi-dimensional analysis, and correlated queries.

**Scenario 2: Building data warehouse in real-time**

- Supports building data warehouse in real-time, and supports collecting and processing streaming data to make real-time data analysis possible.

**Scenario 3: Building mid-end**

- Supports building MPP data platform in the data mid-end. Supports the distributed parallel processing architecture.
- Supports building data warehouse in the data mid-end. Supports docking with mainstream ETL tools.

**Scenario 4: Building lake-warehouse integration**

- Supports building enterprise-level data lake-warehouse integration. Supports efficient data exchange between data lake and data warehouse.

**Scenario 5: Alternative to existing MPP databases**

- Supports replacing common databases, such as Oracle, TeraData, Greenplum, and Vertical.
- Supports replacing other types of MPP databases, such as Gbase 8a, and GaussDB.

**Scenario 6: Applicable to Geographic Information System (GIS) applications**

- Builds Geographic Information System (GIS) applications on Apache Cloudberry.
- Stores and queries geographic location data. Supports spatial data analysis, geocoding, and map visualization.
- Can be applied to city planning, geographic analysis, and map navigation.
