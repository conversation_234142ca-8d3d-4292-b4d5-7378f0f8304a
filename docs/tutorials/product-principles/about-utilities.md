---
title: About Management and Monitoring Utilities
---

# About Management and Monitoring Utilities

Apache Cloudberry provides standard command-line utilities for performing common monitoring and administration tasks.

Cloudberry command-line utilities are located in the $GPHOME/bin directory and are run on the coordinator host. Cloudberry provides utilities for the following administration tasks:

- Install Apache Cloudberry on an array
- Initialize a Apache Cloudberry System
- Start and stop Apache Cloudberry
- Add or remove a host
- Expand the array and redistribute tables among new segments
- Manage recovery for failed segment instances
- Manage failover and recovery for a failed coordinator instance
- Back up and restore a database (in parallel)
- Load data in parallel
- Transfer data between Apache Cloudberry
- System state reporting
