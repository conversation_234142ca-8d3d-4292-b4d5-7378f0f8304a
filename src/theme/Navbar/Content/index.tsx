import { ErrorCauseBoundary, useThemeConfig } from "@docusaurus/theme-common";
import { splitNavbarItems } from "@docusaurus/theme-common/internal";
import NavbarColorModeToggle from "@theme/Navbar/ColorModeToggle";
import Navbar<PERSON>ogo from "@theme/Navbar/Logo";
import NavbarMobileSidebarToggle from "@theme/Navbar/MobileSidebar/Toggle";
import NavbarSearch from "@theme/Navbar/Search";
import NavbarItem, { type Props as NavbarItemConfig } from "@theme/NavbarItem";
import SearchBar from "@theme/SearchBar";
import { type ReactNode } from "react";

import { useIsMobile } from "@site/src/hooks/useIsMobile";
import styles from "./styles.module.css";
import { useEffect, useState } from "react";

function useNavbarItems() {
  // TODO temporary casting until ThemeConfig type is improved
  return useThemeConfig().navbar.items as NavbarItemConfig[];
}

function NavbarItems({ items }: { items: NavbarItemConfig[] }): JSX.Element {
  return (
    <>
      {items.map((item, i) => (
        <ErrorCauseBoundary
          key={i}
          onError={() =>
            new Error(
              `A theme navbar item failed to render.
Please double-check the following navbar item (themeConfig.navbar.items) of your Docusaurus config:
${JSON.stringify(item, null, 2)}`
            )
          }
        >
          <NavbarItem {...item} />
        </ErrorCauseBoundary>
      ))}
    </>
  );
}

function NavbarContentLayout({
  left,
  right,
}: {
  left: ReactNode;
  right: ReactNode;
}) {
  return (
    <div className="navbar__inner">
      <div className="navbar__items">{left}</div>
      <div className="navbar__items navbar__items--right">{right}</div>
    </div>
  );
}

export default function NavbarContent(): JSX.Element {
  const isMobile = useIsMobile();
  const items = useNavbarItems();
  const [leftItems, rightItems] = splitNavbarItems(items);
  const searchBarItem = items.find((item) => item.type === "search");
  const [shouldCollapse, setShouldCollapse] = useState(false);

  useEffect(() => {
    const checkOverlap = () => {
      if (isMobile) {
        setShouldCollapse(false);
        return;
      }

      const logo = document.querySelector('.navbar__brand');
      const rightMenu = document.querySelector('.navbar__items--right');

      if (logo && rightMenu) {
        const logoRect = logo.getBoundingClientRect();
        const menuRect = rightMenu.getBoundingClientRect();

        // 检测是否即将重叠，留出50px的安全距离
        const gap = menuRect.left - (logoRect.left + logoRect.width);
        setShouldCollapse(gap < 50);
      }
    };

    // 初始检查
    checkOverlap();

    // 监听窗口大小变化
    window.addEventListener('resize', checkOverlap);

    // 使用 ResizeObserver 监听导航栏内容变化
    const navbar = document.querySelector('.navbar__inner');
    let resizeObserver: ResizeObserver | null = null;

    if (navbar && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(checkOverlap);
      resizeObserver.observe(navbar);
    }

    return () => {
      window.removeEventListener('resize', checkOverlap);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [isMobile]);
  return (
    <NavbarContentLayout
      left={
        // TODO stop hardcoding items?
        <>
          <div className="nav-left-logo">
            {(isMobile || shouldCollapse) && <NavbarMobileSidebarToggle />}
            <NavbarLogo />
          </div>
          <NavbarItems items={leftItems} />
        </>
      }
      right={
        // TODO stop hardcoding items?
        // Ask the user to add the respective navbar items => more flexible
        <>
          {!shouldCollapse && (
            <>
              <NavbarItems items={rightItems} />
              <NavbarColorModeToggle className={styles.colorModeToggle} />
              {!searchBarItem && (
                <NavbarSearch>
                  <SearchBar />
                </NavbarSearch>
              )}
            </>
          )}
          {shouldCollapse && !isMobile && <NavbarMobileSidebarToggle />}
        </>
      }
    />
  );
}
