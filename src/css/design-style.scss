a {
  text-decoration: none;
  color: inherit;
  &:hover {
    color: var(--active-color);
    text-decoration: underline;
  }
}

// navbar header
.navbar {
  background-color: var(--navbar-bg-color);
  padding: 0;
  .navbar__inner {
    box-sizing: border-box;
    padding: 0 70px;

    // mobile hide right menu
    .navbar__item {
      display: inline-block;
    }
  }
  .navbar__items--right {
    a {
      font-weight: 700;
    }
  }
  .navbar__link--active {
    color: var(--active-color);

    // specify the active link color
    &[href="/releases"] {
      color: var(--ifm-navbar-link-color);
    }
  }
  .navbar-sidebar {
    background-color: var(--normal-bg-1);
  }
  @media screen and (max-width: 1024px) {
    & {
      width: 100%;
      min-width: 100%;
    }
    --ifm-navbar-background-color: rgba(15, 15, 15);
    --ifm-navbar-height: 47px;

    .navbar__items--right {
      display: none;
    }
    .navbar__logo {
      width: 107px;
    }
  }
  @media screen and (max-width: 768px) {
    .navbar__inner {
      padding: 0 35px;
    }
  }
}
[data-theme="light"] {
  .navbar {
    --ifm-navbar-background-color: #fff;
  }
}

// markdown style
article {
  code {
    border: 1px solid var(--code-border-color);
    font-size: 14px;
  }
  .anchor {
    color: var(--active-color);
  }

  .hash-link {
    &::before {
      color: var(--active-color);
    }
  }
  a {
    color: var(--active-color);
    &:hover {
      text-decoration: underline;
    }
  }
  li::marker {
    color: var(--active-color);
  }
  li > ul {
    list-style: disc;
    > li::marker {
      color: rgb(201, 209, 217);
      font-size: 12px;
    }
  }

  // ul, ol style
.markdown ul {
  list-style-type: square; /* Options: circle, square */
}

.markdown ul, .markdown ol {
  font-size: 15px;
}

.markdown blockquote {
  border-left: 4px solid rgba(255, 168, 0, 1);
  background-color: rgba(255, 168, 0, 0.1);
  font-style: italic;
}

.markdown img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
  display: block;
  margin: 16px auto;
}

  // 1,2,3
  ol {
    counter-reset: list;
    list-style: none;

    > li {
      position: relative;
      &::before {
        counter-increment: list;
        content: counter(list);
        background-color: var(--ordered-list-bg-color);
        display: inline-block;
        position: absolute;
        width: 22px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        left: -30px;
        top: 1px;
        border-radius: 50%;
        font-size: 14px;
        color: var(--ordered-list-text-color);
      }
    }
  }

  // a,b,c
  li > ol {
    list-style: lower-alpha;
    > li {
      &::before {
        display: none;
      }
    }
  }

  // icon
  .theme-admonition {
    a {
      text-decoration-color: var(--active-color);
    }
    &.alert--warning {
      font-size: 14px;
      span::before {
        content: "";
        display: inline-block;
        width: 23px;
        height: 23px;
        font-size: 14px;
        background-image: url("/img/docs/warning.svg");
        background-size: contain;
      }
      svg {
        display: none;
      }
    }
    &.alert--danger {
      font-size: 14px;
      span::before {
        content: "";
        display: inline-block;
        width: 24px;
        height: 24px;
        background-image: url("/img/docs/danger.svg");
        background-size: contain;
      }
      svg {
        display: none;
      }
    }
    &.alert--success {
      font-size: 14px;
    }
    &.alert--secondary {
      font-size: 14px;
    }
  }
}

pre code .token {
  font-size: 12px;
}

.main-wrapper {
  // docs left menu released
  .container {
    a {
      text-decoration: none;
    }
    article {
      --ifm-color-primary: var(--active-color);
    }
  }
  @media screen and (min-width: 1440px) {
    > div {
      width: var(--global-main-width);
    }
  }
  // docs layout
  > div {
    max-width: var(--global-main-width);
    margin: 0 auto;
  }
  h1:first-child {
    font-size: 30px;
  }
  h2 {
    font-size: 24px;
  }

  h3 {
    font-size: 20px;
  }
  p,
  li {
    color: var(--sub-text-color-2);
  }
  strong {
    color: var(--title-text-color);
  }

  .alert--info {
    border: none;
    font-size: 14px;
    background-color: var(--docs-detail-bg);
    &[open] summary {
      &::after {
        transform: rotate(90deg);
        transition: transform 0.3s ease-in-out;
      }
    }
    summary {
      padding-left: 0;
      color: var(--active-color);

      &::before {
        display: none;
      }
      &::after {
        // content: "▶";
        content: "";
        position: relative;
        left: 10px;
        top: 5px;
        width: 20px;
        height: 20px;
        display: inline-block;
        background-image: url(@site/static/img/greater_than.png);
        background-repeat: no-repeat;
        transition: transform 0.3s ease-in-out;
      }
      // click to see detail inner line
      & + div {
        > div {
          border-top: none;
          padding-top: 1px;
        }
      }
    }
  }
}

// md right sidebar
.table-of-contents {
  border-left: none;
  .table-of-contents__link--active {
    color: var(--right-sidebar-active-color);
  }

  li {
    position: relative;
    margin-top: 19px;
    height: 21px;
    line-height: 16px;
    &:not(:last-child) {
      &::after {
        content: "";
        display: inline-block;
        position: absolute;
        width: 2px;
        background-color: var(--right-sidebar-circle-bg-color);
        height: 33px;
        left: -17px;
        top: 14px;
      }
    }
    a {
      &.table-of-contents__link--active {
        &::before {
          background-color: var(--right-sidebar-active-color);
        }
      }
      &::before {
        content: "";
        display: inline-block;
        position: absolute;
        width: 8px;
        height: 8px;
        background-color: var(--right-sidebar-circle-bg-color);
        border-radius: 50%;
        left: -20px;
        top: 6px;
      }
    }
  }
  li > ul {
    a {
      &::before {
        // left: -36px;
        display: none;
      }
    }
  }
  @media screen and (max-width: 996px) {
    li {
      &::after {
        display: none !important;
      }
      a {
        &::before {
          display: none !important;
        }
      }
    }
  }
}

.theme-doc-breadcrumbs {
  .breadcrumbs {
    display: flex;
    align-items: center;

    .breadcrumbs__item {
      display: inline-flex;
      align-items: center;

      .breadcrumbs__link {
        padding: 0 6px;
      }
      a.breadcrumbs__link {
        width: 18px;
        height: 18px;
        display: inline-block;
        background-image: url("/img/back_home.svg");
        background-repeat: no-repeat;
        background-size: cover;
        margin-right: 4px;
        margin-bottom: 3px;

        svg {
          display: none;
        }
      }
      &:not(:last-child) {
        &::after {
          opacity: 1;
          width: 12px;
          height: 12px;
          background-repeat: no-repeat;
          position: relative;
          top: -1px;
        }
      }
    }
    .breadcrumbs__item--active {
      .breadcrumbs__link {
        font-size: 13px;
        background-color: transparent;
      }
    }
  }
}

// docs right nav
.theme-doc-toc-desktop {
  overflow-y: visible !important;
}

// docs left nav  ul > li > div > a/ul
.theme-doc-sidebar-container {
  border-right: none !important;
  margin-right: 60px;
  font-size: 14px;
  .theme-doc-sidebar-menu {
    margin-right: 0px;
    // li
    .theme-doc-sidebar-item-category {
      position: relative;
      a {
        position: relative;
        &::after {
          display: none;
        }
        &::before {
          content: "";
          position: absolute;
          left: -1.5px;
          display: inline-block;
          width: 16px;
          height: 16px;
          background-image: url("/img/docs/sub-no-active.svg");
          top: 8px;
          transition: all 0.1s;
        }
      }
      // button {
      // display: none !important;
      // }

      // no active add (div)
      &.menu__list-item--collapsed {
        a {
          &::before {
            transition: all 0.1s;
            background-image: url("/img/docs/add-no-active.svg");
          }
        }
      }
      // first level(li)
      &.theme-doc-sidebar-item-category-level-1 {
        position: relative;
        > ul.menu__list {
          padding-left: 0;
          &::before {
            content: "";
            display: inline-block;
            width: 1px;
            height: calc(100% - 34px);
            position: absolute;
            background-color: var(--ifm-hover-overlay);
            left: 4px;
          }
        }
        &.menu__list-item {
          > .menu__list-item-collapsible {
            background: transparent;
            a {
              color: var(--title-text-color);
              &::before {
                background-image: url("/img/docs/sub.svg");
              }
            }
            &:hover {
              a {
                color: var(--active-color);
                &::before {
                  background-image: url("/img/docs/hover-sub-active.svg");
                }
              }
            }
          }
        }
        &.menu__list-item--collapsed {
          > .menu__list-item-collapsible {
            &:hover {
              a {
                &::before {
                  background-image: url("/img/docs/hover-add-active.svg") !important;
                }
              }
            }
          }
          a {
            &::before {
              background-image: url("/img/docs/add.svg") !important;
            }
          }
        }
      }

      // li
      &:not(.theme-doc-sidebar-item-category-level-1) {
        // foldable (div)
        .menu__list-item-collapsible {
          padding-left: var(--ifm-menu-link-padding-horizontal);
        }
        &.menu__list-item {
          .menu__link--active {
            &::before {
              background-image: url("/img/docs/hover-sub-active.svg");
            }
          }
        }
      }
      // leaf node (li)
      .theme-doc-sidebar-item-link {
        &.theme-doc-sidebar-item-link-level-3 {
          a {
            &::after {
              left: -14px;
            }
          }
        }
        a {
          &::before {
            display: none !important;
          }
        }
      }
    }
  }
}

// download
.thin-scrollbar {
  overflow-y: visible !important;
}

// blog detail bottom
.pagination-nav {
  margin-top: 25px;
  display: flex;
  justify-content: space-between;

  .pagination-nav__link {
    width: 298px;
    height: 60px;
    font-size: 14px;
    @media screen and (max-width: 768px) {
      width: 150px;
    }

    .pagination-nav__label {
      font-size: 14px;
      color: var(--active-color);
      font-weight: 400;
    }
    .pagination-nav__sublabel {
      color: var(--sub-text-color-2);
    }
    &:hover {
      background-color: var(--active-color);
      .pagination-nav__sublabel {
        color: var(--hover-full-bg-text-color);
      }
      .pagination-nav__label {
        color: var(--hover-full-bg-text-color);
      }
    }
  }
}

// hide table of content
@media screen and (max-width: 1440px) {
  .container {
    .row {
      > .col--3 {
        display: none;
      }
    }
  }
}

ul[role="tablist"] {
  border-bottom: 1px solid #383b3f;
  .tabs__item {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  .tabs__item--active {
    background-color: #333333;
    color: #fff;
    border-bottom: none;
  }
}

html[data-theme="light"] {
  .theme-doc-breadcrumbs {
    a.breadcrumbs__link {
      background-image: url("/img/back_home-light.svg") !important;
      color: auto !important;
    }
  }
  .theme-admonition {
    &.alert--warning {
      span::before {
        background-image: url("/img/docs/warning-light.svg");
      }
    }
    &.alert--danger {
      span::before {
        background-image: url("/img/docs/danger-light.svg");
      }
    }
  }
  ul[role="tablist"] {
    border-bottom: 1px solid #d6d9dd;
    .tabs__item--active {
      background-color: #ffeecc;
      color: #000;
    }
  }

  // docs left nav
  .theme-doc-sidebar-container {
    .theme-doc-sidebar-menu {
      // li
      .theme-doc-sidebar-item-category {
        a {
          &::before {
            background-image: url("/img/docs/sub-light.svg");
          }
        }

        // no active add (div)
        &.menu__list-item--collapsed {
          a {
            &::before {
              // inner
              background-image: url("/img/docs/add-light.svg");
            }
          }
        }
        // first level(li)
        &.theme-doc-sidebar-item-category-level-1 {
          > ul.menu__list {
            &::before {
              background-color: #ffe5b3;
            }
          }
          &.menu__list-item {
            > .menu__list-item-collapsible {
              a {
                &::before {
                  background-image: url("/img/docs/sub-light.svg");
                }
              }
              &:hover {
                a {
                  color: var(--active-color);
                  &::before {
                    background-image: url("/img/docs/hover-sub-active.svg");
                  }
                }
              }
            }
          }
          &.menu__list-item--collapsed {
            > .menu__list-item-collapsible {
              &:hover {
                a {
                  &::before {
                    background-image: url("/img/docs/hover-add-active.svg") !important;
                  }
                }
              }
            }
            a {
              &::before {
                background-image: url("/img/docs/add-light.svg") !important;
              }
            }
          }
        }

        // li
        &:not(.theme-doc-sidebar-item-category-level-1) {
          &.menu__list-item {
            .menu__link--active {
              &::before {
                background-image: url("/img/docs/sub-light.svg");
              }
            }
          }
        }
      }
    }
  }
}

.navbar-home-lighting {
  background: #000;
  .clean-btn {
    color: #fff;
  }
  .check-theme-switch {
    display: none !important;
  }
}

@media (max-width: 1024px) {
  .navbar__toggle {
    display: inherit;
  }
}

// 当动态检测到重叠时，也显示汉堡菜单
.navbar__toggle {
  display: none;
}

.navbar-sidebar--show .navbar__toggle,
.navbar__toggle[data-force-show="true"] {
  display: inherit !important;
}

.nav-left-logo {
  display: flex;
}


