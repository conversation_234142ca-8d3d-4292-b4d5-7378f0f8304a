---
title: Apache Cloudberry (Incubating) 2.0.0 Changelog
description: This page provides change logs for Apache Cloudberry (Incubating) 2.0.0.
---

## Commits

* [`8178d4faefe`](https://github.com/apache/cloudberry/commit/8178d4faefeca459f7ef2dd3aa502f23e0d7a5c4) - Replace pip3 download with curl for Python deps (<PERSON><PERSON><PERSON>)
* [`5cd937979c5`](https://github.com/apache/cloudberry/commit/5cd937979c5b76f1a26e456c3406e84629530571) - License: add & clean up license headers and files (<PERSON><PERSON><PERSON>)
* [`cd544ffc51a`](https://github.com/apache/cloudberry/commit/cd544ffc51ac245b0bc713cacde7fdd4e0abf4f5) - Release: Use pip3 download for Python packages (<PERSON><PERSON><PERSON> Wang)
* [`de84d5ab798`](https://github.com/apache/cloudberry/commit/de84d5ab7986d8ffef8072da97a81d38c28b9a14) - PAX: replace cpp-stub submodule with local sources (zhoujiaqi)
* [`868c4694f4c`](https://github.com/apache/cloudberry/commit/868c4694f4c70203b7a7235c2db8346de98f08c0) - Set Maven pom.xml project version to 2.0.0-incubating (Ed Espino)
* [`61e35f387e1`](https://github.com/apache/cloudberry/commit/61e35f387e169551ad29b3bc11708a5a8aee0f69) - Update version string from 2.0.0-incubating-rc1 to 2.0.0-incubating (Ed Espino)
* [`856531774c0`](https://github.com/apache/cloudberry/commit/856531774c07342aa57f666f082be618faa09c5f) - Add configure-time check for protobuf, zstd, cmake when --enable-pax is specified (Jianghua Yang)
* [`e3e3fcde8d5`](https://github.com/apache/cloudberry/commit/e3e3fcde8d53e7bd2c4512ff918f2e265a852c13) - Add the notice to greenplum-path* scripts (Dianjin Wang)
* [`ce1fc943898`](https://github.com/apache/cloudberry/commit/ce1fc9438989ac22146c1c7e483933f3560e6572) - Prepare 2.0.0-incubating-rc1 release candidate on REL_2_STABLE (Ed Espino)
* [`92deedce751`](https://github.com/apache/cloudberry/commit/92deedce751a41dbb3ef75602f0938a785bd0d26) - Fix format attribute for gp_fatal_log to avoid clang build warning (#1134) (Jianghua.yjh) [#1134](https://github.com/apache/cloudberry/pull/1134)
* [`9f37357a6ba`](https://github.com/apache/cloudberry/commit/9f37357a6ba7757c50ec8d28aa31fc3ffc850399) - Support new VERSION/BUILD\_NUMBER files for non-Git versioning fallback (Ed Espino) [#1135](https://github.com/apache/cloudberry/pull/1135)
* [`a3bcbcb17b5`](https://github.com/apache/cloudberry/commit/a3bcbcb17b549ea265bc4e39c94ff63af0ebad8e) - [AQUMV] Remove aqumv_adjust_simple_query (Zhang Mingli) [#1138](https://github.com/apache/cloudberry/pull/1138)
* [`54087f70bf8`](https://github.com/apache/cloudberry/commit/54087f70bf8c9b62e6ccef81ee1b7799cf8c233c) - Fix unstable scan rows caused by limit squelch. (Dongxiao Song) [#1140](https://github.com/apache/cloudberry/pull/1140)
* [`a279036ac4a`](https://github.com/apache/cloudberry/commit/a279036ac4a352844c5c399f96ca145ab7d063ed) - Doc: add DeepWiki Badge to README.md (Dianjin Wang) [#1113](https://github.com/apache/cloudberry/pull/1113)
* [`8631f4a57d0`](https://github.com/apache/cloudberry/commit/8631f4a57d0d0a8d4f06df73b0ab82cc900bfae4) - Fix case fail caused by change the default value of pax_enable_debug to false (#1093) (#1133) (DongxiaoSong) [#1133](https://github.com/apache/cloudberry/pull/1133)
* [`bc5881c742c`](https://github.com/apache/cloudberry/commit/bc5881c742cdcfee6458e8746b5cbdb89b29a804) - Add Apache RAT license metadata for PAX storage C++ submodules (Ed Espino) [#1136](https://github.com/apache/cloudberry/pull/1136)
* [`aeaf49b4bfe`](https://github.com/apache/cloudberry/commit/aeaf49b4bfe7147d50bb31ed2d07d621b4140431) - Add ASF incubation DISCLAIMER file for release compliance (Ed Espino) [#1137](https://github.com/apache/cloudberry/pull/1137)
* [`79c450896d1`](https://github.com/apache/cloudberry/commit/79c450896d1779f0dbba916e9f84088423806f2a) - [ORCA] Fix compile warning. (Zhang Mingli) [#1132](https://github.com/apache/cloudberry/pull/1132)
* [`15572b09ce3`](https://github.com/apache/cloudberry/commit/15572b09ce36779868cbff59adffa8769c214932) - [AQUMV] Fix materialized view rename to also update gp_matview_aux (Zhang Mingli) [#1122](https://github.com/apache/cloudberry/pull/1122)
* [`5317c80c9b7`](https://github.com/apache/cloudberry/commit/5317c80c9b7bf96323ffa5411a37d748ed0b27f8) - Fix incorrect strncmp usage when comparing access method names. (Jianghua Yang) [#1131](https://github.com/apache/cloudberry/pull/1131)
* [`1286c92b076`](https://github.com/apache/cloudberry/commit/1286c92b076b4a7357bd92d52168b94c9b0663ed) - Fix: Core happend when calling pg_relation_size on root partitioned table with PAX AM (#1128) (jiaqizho) [#1128](https://github.com/apache/cloudberry/pull/1128)
* [`114956860ba`](https://github.com/apache/cloudberry/commit/114956860ba86154aa331eeb8982e828a6ac9daf) - Fix unstable case of auto_explain. (#1127) (DongxiaoSong) [#1127](https://github.com/apache/cloudberry/pull/1127)
* [`94a81765e65`](https://github.com/apache/cloudberry/commit/94a81765e655be331de32d496a48fa461fba1108) - Update the version generation logic and info (Dianjin Wang) [#929](https://github.com/apache/cloudberry/pull/929)
* [`f4ad0f77e79`](https://github.com/apache/cloudberry/commit/f4ad0f77e7994d93123a87c533532c252f7c8d05) - ORCA: Support two phase window functions (jiaqizho) [#1014](https://github.com/apache/cloudberry/pull/1014)
* [`3fbebeae14e`](https://github.com/apache/cloudberry/commit/3fbebeae14e6cbe252e120cdc23552140deeba94) - fix coredump in interconnect_abort_callback (GongXun) [#1124](https://github.com/apache/cloudberry/pull/1124)
* [`fb8685159a1`](https://github.com/apache/cloudberry/commit/fb8685159a116939bc43fd3ff9c3cb242b15a80e) - performance: Change the default value of pax_enable_debug to false (#1093) (Xun Gong) [#1093](https://github.com/apache/cloudberry/pull/1093)
* [`c6bddb41db0`](https://github.com/apache/cloudberry/commit/c6bddb41db06f59eccd1da0d037164088ef55d31) - Change PAX Plugin to Be Disabled by Default (Dianjin Wang) [#1081](https://github.com/apache/cloudberry/pull/1081)
* [`bb6fef2ea0c`](https://github.com/apache/cloudberry/commit/bb6fef2ea0c4331e03dec31f36cf7bbce0a72007) - [AQUMV] Store view query in gp_matview_aux for view matching. (Zhang Mingli) [#1117](https://github.com/apache/cloudberry/pull/1117)
* [`1595fb9c272`](https://github.com/apache/cloudberry/commit/1595fb9c272e014ecd8d9a2e5775635ed30999d9) - PAX: make `cmake_minimum_required()` check at top (Dianjin Wang) [#1099](https://github.com/apache/cloudberry/pull/1099)
* [`853596b8b11`](https://github.com/apache/cloudberry/commit/853596b8b119cc02908052cfed2cfaa5eaa4f53b) - Doc: add dependency setup instructions for PAX (Dianjin Wang) [#1109](https://github.com/apache/cloudberry/pull/1109)
* [`44133378f08`](https://github.com/apache/cloudberry/commit/44133378f08f004c05e269bd36c1794904fdf582) - [AQUMV] Add cases of INSERT-SELECT queries using materialized views. (Zhang Mingli) [#1110](https://github.com/apache/cloudberry/pull/1110)
* [`86b66ac8914`](https://github.com/apache/cloudberry/commit/86b66ac89148e5c7afa32058a8d983e282d4a436) - Use callback instead of calling ExecFilterJunk() (#1059) (YueZhang) [#1059](https://github.com/apache/cloudberry/pull/1059)
* [`e37ca5b734f`](https://github.com/apache/cloudberry/commit/e37ca5b734fa508ba1e001344b30edfaa5c7d7ed) - Fix Pax build error in Rocky Linux 8 (GongXun) [#1105](https://github.com/apache/cloudberry/pull/1105)
* [`9f9d297c835`](https://github.com/apache/cloudberry/commit/9f9d297c83567bd78ca0488ecf6a8f9cbc7a15d3) - Ignore PAX file pax-cdbinit--1.0.sql (Zhang Mingli) [#1108](https://github.com/apache/cloudberry/pull/1108)
* [`fd7dcfb79a4`](https://github.com/apache/cloudberry/commit/fd7dcfb79a473fcc40635ef61f341531deb04e67) - Replace Pylint with Ruff for Python linting (Ed Espino) [#1079](https://github.com/apache/cloudberry/pull/1079)
* [`21d2b35d1d7`](https://github.com/apache/cloudberry/commit/21d2b35d1d72640659478b49c61f6d673660e627) - Fix compile warnings/errors (#1107) (Hao Wu) [#1107](https://github.com/apache/cloudberry/pull/1107)
* [`49a013521d0`](https://github.com/apache/cloudberry/commit/49a013521d0d3534cb68686dbe37e9760ecff434) - Update the relation pattern to be more precise (#1106) (Hao Wu) [#1106](https://github.com/apache/cloudberry/pull/1106)
* [`9223de872fe`](https://github.com/apache/cloudberry/commit/9223de872fed14b1471ddb36789f4e784f10740e) - Refactor Extend Protocol in libpq for Binary Data Handling (Zhang Mingli) [#1098](https://github.com/apache/cloudberry/pull/1098)
* [`ae4fac229bc`](https://github.com/apache/cloudberry/commit/ae4fac229bccde3eec3b59387b2d41363ebbc1cf) - Update PyGreSQL license and remove legacy files and exclusions (Ed Espino) [#1102](https://github.com/apache/cloudberry/pull/1102)
* [`d9aeebd5a28`](https://github.com/apache/cloudberry/commit/d9aeebd5a28ace6bfdb67960abc1bc218bcafdd4) - Upgrade PyYAML from 5.3.1 to 5.4.1 (Dianjin Wang) [#1080](https://github.com/apache/cloudberry/pull/1080)
* [`dbfb32e2612`](https://github.com/apache/cloudberry/commit/dbfb32e2612c9d71f677aaed832584929428c4cd) - Doc: update the brand to Cloudberry (Dianjin Wang) [#1094](https://github.com/apache/cloudberry/pull/1094)
* [`964756fdce1`](https://github.com/apache/cloudberry/commit/964756fdce155256f0a409be892634f73d57bd7b) - Disable autovacuum to make test stable (Hao Wu) [#1088](https://github.com/apache/cloudberry/pull/1088)
* [`c5f05d1276e`](https://github.com/apache/cloudberry/commit/c5f05d1276e3f1d6f0db65f93db61e24823f6fbb) - PAX: Fix RMGR desc function for pax (Hao Wu) [#1088](https://github.com/apache/cloudberry/pull/1088)
* [`8c543ce4b5a`](https://github.com/apache/cloudberry/commit/8c543ce4b5a6ba59cc885332550dd990b771aa84) - Fix incorrect usage of gp_versionstr in pg_upgrade version check (Jianghua Yang) [#1063](https://github.com/apache/cloudberry/pull/1063)
* [`41f32d966c6`](https://github.com/apache/cloudberry/commit/41f32d966c69e805d08d990fb294f2a1ea182454) - Cleanup the `deploy/*` dir (Dianjin Wang) [#1090](https://github.com/apache/cloudberry/pull/1090)
* [`b3ce3c0b2fc`](https://github.com/apache/cloudberry/commit/b3ce3c0b2fcc17a2a43b40cfd7d2cd60c113f702) - Add RAT license audit config and compliance metadata for release (Ed Espino) [#1066](https://github.com/apache/cloudberry/pull/1066)
* [`7e00589f2a4`](https://github.com/apache/cloudberry/commit/7e00589f2a4391b4b58bc869c96094494db8165b) - ORCA: Fix missing the aggstar in aggref (zhoujiaqi) [#1051](https://github.com/apache/cloudberry/pull/1051)
* [`f1d1b2b1285`](https://github.com/apache/cloudberry/commit/f1d1b2b1285f4b709f5734b1b25f0cc667eb8ab3) - Remove the unused hd-cli/* from Cloudberry source (Dianjin Wang) [#1073](https://github.com/apache/cloudberry/pull/1073)
* [`e492ba18b8a`](https://github.com/apache/cloudberry/commit/e492ba18b8a2dd74b7e913933e20618163981b49) - CI: Enable submodule checkout and remove manual submodule update (Ed Espino) [#1076](https://github.com/apache/cloudberry/pull/1076)
* [`5d0e2ef7306`](https://github.com/apache/cloudberry/commit/5d0e2ef7306c6a3e7fc5a06cc52aae9b842ba9c8) - Fix windows case information mismtach. (Dongxiao Song) [#1071](https://github.com/apache/cloudberry/pull/1071)
* [`47c70718b1a`](https://github.com/apache/cloudberry/commit/47c70718b1a7ab464627dba9c4c48f58ba04f056) - Sleep 2s after promotion to avoid connection failure. (Dongxiao Song) [#1071](https://github.com/apache/cloudberry/pull/1071)
* [`b1cf523f743`](https://github.com/apache/cloudberry/commit/b1cf523f7435fbfad61b4e0b7f231733f92c8e6a) - pg_dump: Lock all interesting tables in single statement. (Brent Doil) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`8487b63501c`](https://github.com/apache/cloudberry/commit/8487b63501c204327d22cd29e7b3f87b7ef01719) - pg_dump: Remove unused TypeCache struct (Brent Doil) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`fb6b34a8e1e`](https://github.com/apache/cloudberry/commit/fb6b34a8e1e5a6f757908fb1b31356eb551370a6) - pg_dump: Add missing relkind case (Peter Eisentraut) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`fa15576d4ce`](https://github.com/apache/cloudberry/commit/fa15576d4ce5ecfd47fe934e0383f546fbcc8c7a) - Fix minor memory leaks in pg_dump. (Tom Lane) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`0ee0531c30c`](https://github.com/apache/cloudberry/commit/0ee0531c30cc9ce34daaa2cca643cd9ba94c3af6) - In pg_dump, use simplehash.h to look up dumpable objects by OID. (Tom Lane) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`d9cc34ad4cb`](https://github.com/apache/cloudberry/commit/d9cc34ad4cbb728bdb02412207f949d5413b3188) - pg_dump: Remove unused flags and related code (Brent Doil) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`c462e38836c`](https://github.com/apache/cloudberry/commit/c462e38836ce6d7c68f7b0ad4386b11c8c7a32d8) - Remove unneccesary version checks from pg_dump (Vladimir Rachkin) [#1005](https://github.com/apache/cloudberry/pull/1005)
* [`8da79b3de66`](https://github.com/apache/cloudberry/commit/8da79b3de66a4e334bd0c55c02547a378cb727cd) - Show pax relation relfilenode in pg_waldump (#1048) (reshke) [#1048](https://github.com/apache/cloudberry/pull/1048)
* [`112667cee48`](https://github.com/apache/cloudberry/commit/112667cee48f7f7dc433b5fef3df80c5d504a85c) - Add SonarQube for Apache Cloudberry (Dianjin Wang) [#1004](https://github.com/apache/cloudberry/pull/1004)
* [`96a9b929465`](https://github.com/apache/cloudberry/commit/96a9b9294656b6fe11b2253f8620d8a44bb6ba45) - LICENSE: add licenses for bundled python packages (Dianjin Wang) [#1053](https://github.com/apache/cloudberry/pull/1053)
* [`1cbab9b3c4b`](https://github.com/apache/cloudberry/commit/1cbab9b3c4b64c9a75fdfcd44edcd9e79204739d) - CI: Enable PAX module in Coverity scan workflow (Dianjin Wang) [#1050](https://github.com/apache/cloudberry/pull/1050)
* [`3d8fae4d37e`](https://github.com/apache/cloudberry/commit/3d8fae4d37eda75f998a6eff21adad63125686ab) - Fix activeTaskCount increment for CRON_TASK_WAITING tasks (Jianghua Yang) [#1037](https://github.com/apache/cloudberry/pull/1037)
* [`26b9cda259d`](https://github.com/apache/cloudberry/commit/26b9cda259d07304796a6984c80414f4809a7b5c) - PAX: fix icw_test in github CI (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`06ca2f696b5`](https://github.com/apache/cloudberry/commit/06ca2f696b58e83796b4bf94e960ef91e2c708b2) - PAX: Change the default catalog to auxiliary table and enable CI (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`257eaf6e5b4`](https://github.com/apache/cloudberry/commit/257eaf6e5b4f5623a3c86ccb53106b7ff8aa871c) - PAX: update submodule (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`8325e1a9add`](https://github.com/apache/cloudberry/commit/8325e1a9addfe186411d1e39e705cbc4728abf60) - PAX: Remove vectorization related tests (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`d16fdfb520d`](https://github.com/apache/cloudberry/commit/d16fdfb520d4f56f4f124f96ee23a0804664ee87) - Adapt PAX isolation2 test to CBDB (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`76acbd55581`](https://github.com/apache/cloudberry/commit/76acbd5558198f6be93b2da3811e23b6daaf5083) - Adapt PAX regression test to CBDB (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`5478c0acbfe`](https://github.com/apache/cloudberry/commit/5478c0acbfea33f3e9d004e3bc6a55231424fa58) - FIX: Invalid USE_ASSERT_CHECKING in DeleteWithVisibilityMap (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`e646070edd0`](https://github.com/apache/cloudberry/commit/e646070edd01111180269962c79c43de14d694eb) - PAX: Adapt to 32-bit relnode (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`6e40e2bea66`](https://github.com/apache/cloudberry/commit/6e40e2bea6635b71ead45dcb2cb412c3a9595243) - Missing AM function swap_relation_files call (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`1b20765d65a`](https://github.com/apache/cloudberry/commit/1b20765d65a7d98ce298d296b784a212a3811518) - PAX: Adapt to the latest access method (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`32cd1f4e436`](https://github.com/apache/cloudberry/commit/32cd1f4e43657445b283cecebbe5894d11a07900) - PAX: Add doc/* and update README (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`670e3fc92be`](https://github.com/apache/cloudberry/commit/670e3fc92beb3039c726a61ce077563144b5315e) - PAX: fix failture case in isolation2 testcase (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`2283294bce0`](https://github.com/apache/cloudberry/commit/2283294bce0c57e86e7986a36b75776f7d343591) - PAX: Remove support for RemoteFileSystem (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`300801da093`](https://github.com/apache/cloudberry/commit/300801da093f68bac2735afe69e55a12587570b8) - PAX: change length array to offset array (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`84580db882c`](https://github.com/apache/cloudberry/commit/84580db882cdd74cabb32a6ad369f59d73f5bd47) - Add Apache license to pax files (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`bf5bc5266d9`](https://github.com/apache/cloudberry/commit/bf5bc5266d9da5d92215f981bc38ce6b8510fe7f) - PAX: Add manifest implementation for catalog (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`2a202acd4b3`](https://github.com/apache/cloudberry/commit/2a202acd4b313fb4fba2b7a19a644a3bd0f5dd68) - PAX: Fix test cases with or without vectorization (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`8a2e129bc4a`](https://github.com/apache/cloudberry/commit/8a2e129bc4a2969c7a9a4ad6c8f718c7020ca66a) - Fix the failure case in icw test. (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`b5b2db31d20`](https://github.com/apache/cloudberry/commit/b5b2db31d2070826583bdef09ce572c71501a7fd) - PAX: Remove internal partition support (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`b42e71dd807`](https://github.com/apache/cloudberry/commit/b42e71dd8074012e68b8a9fc363908cb032d4c8d) - Update vectoried scan interface (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`6c69216a251`](https://github.com/apache/cloudberry/commit/6c69216a251f58ec6290b2d597e10b824718c403) - PAX: remove the GUC VECTOR_MAX_BATCH_SIZE_GUC_NAME (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`ce3815ce769`](https://github.com/apache/cloudberry/commit/ce3815ce769a2dbba9bfa6322d086930a1b73c59) - build: pax supports incremental compilation. (GongXun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`58c000bb0b9`](https://github.com/apache/cloudberry/commit/58c000bb0b97b9f3969adf391c07d204b02bae49) - Pax: fix io error in pax table when transaction abort (GongXun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`0d23604674f`](https://github.com/apache/cloudberry/commit/0d23604674f54526630e91d0d916671613090b97) - PAXPY: Changed the CmakeLists (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`9d7d1e98e8e`](https://github.com/apache/cloudberry/commit/9d7d1e98e8e763562cdcfa9b954df18e4598f4a0) - PAX: Fix nullptr after toast compress failed (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`2577cda2647`](https://github.com/apache/cloudberry/commit/2577cda2647ce14cc21693a479075619d2db5623) - Adapt pax auxiliary table to manifest API (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`35b919135d1`](https://github.com/apache/cloudberry/commit/35b919135d116c602899a5f49e8278bb29442a54) - PAX: Add manifest api (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`692143a55de`](https://github.com/apache/cloudberry/commit/692143a55de59f2672b8b22749d4a247c7048283) - PAX: create a per-tablespace subdirectory when redo pax create directory log. (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`b6d4a24da24`](https://github.com/apache/cloudberry/commit/b6d4a24da24c249574a2bf29b29f3ce480d86bd9) - PAX: Fix isolation2 for test files (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`6f0070d87a7`](https://github.com/apache/cloudberry/commit/6f0070d87a76314e9a4eb24a2bb986463fcdf5e4) - PAX: Add isolation2 tests in ci (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`36130c652ef`](https://github.com/apache/cloudberry/commit/36130c652efec630d6075bc8835ac50ad00dd869) - PAX: Add wal log (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`4d871f0f9ca`](https://github.com/apache/cloudberry/commit/4d871f0f9cac30860ef89df5d70e54e3aee435d3) - PAX: remove the unaligned(bypass) logic. (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`166b4b8c041`](https://github.com/apache/cloudberry/commit/166b4b8c041d04cbc779ade3852fa99bbacad994) - Support custom smgr for pax (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`5b6a201042a`](https://github.com/apache/cloudberry/commit/5b6a201042ab8886e708646a74b1ad6edff2758d) - Regularly synchronize CBDB and update CI (GongXun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`be9cc40090b`](https://github.com/apache/cloudberry/commit/be9cc40090b890a5a36820c9c69bae3f9ca6616c) - PAX: use the atomic in filter statistics (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`09ec2a60238`](https://github.com/apache/cloudberry/commit/09ec2a602385544b78b4b0440f028d1644d2fd03) - Fix: memory leak in VEC path when read the PG Nuemric (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`5f1a828eaf3`](https://github.com/apache/cloudberry/commit/5f1a828eaf32f92d21b579e91d82c746295d33e4) - PAX: split unstable filter_tree test case (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`469452a16ed`](https://github.com/apache/cloudberry/commit/469452a16eddf277a9fd993f766f54ea3ea26753) - optimize: refactor OrcGroup::ReadTuple to improve performance (GongXun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`acc97d3d0f6`](https://github.com/apache/cloudberry/commit/acc97d3d0f6d7f962bf4f7339f39eed9186dc282) - Pax/eliminate offsets array when data writing (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`a53da704194`](https://github.com/apache/cloudberry/commit/a53da704194f5777be4542ca538e7b270f835d01) - Decouple the parallel-scan code and MicroPartitionMetadata (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`c81e8b27448`](https://github.com/apache/cloudberry/commit/c81e8b274481d14629fddba6f33566c8c051b9a0) - minor performance improve in data reading (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`16c4087c71f`](https://github.com/apache/cloudberry/commit/16c4087c71f83c6ce0f403f7561700e96fdd7bc1) - performance: refactor stats to improve pax writing performance (GongXun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`bce408d859b`](https://github.com/apache/cloudberry/commit/bce408d859b6b89a465ec4708618ef16fe4a8fa3) - PAX: remove the memalign in non-fixed column (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`6f7da15a795`](https://github.com/apache/cloudberry/commit/6f7da15a795e5b9293cbcc2710a0fbf322ef8a06) - PAX: Use unique pointer instead of shared pointer for some cases (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`880ff88c018`](https://github.com/apache/cloudberry/commit/880ff88c0187c52cae0c129179d1214370ec0674) - Fix: pax reloption without order will cause the statistics no effect. (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`721aa7eb623`](https://github.com/apache/cloudberry/commit/721aa7eb623b42ca7a49b4285ef4be4edc988f17) - PAX: do the encoding options cache in tablewriter (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`43506ac9180`](https://github.com/apache/cloudberry/commit/43506ac91804919dda898b9a96787cd1d399fcd0) - PAX: filter support arithmetic op exprs (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`34e44bb804c`](https://github.com/apache/cloudberry/commit/34e44bb804cbd81e07b127d5e32297edd36bfa5e) - PAX:Filter support CAST in PG path (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`1482864d09d`](https://github.com/apache/cloudberry/commit/1482864d09d317e9601c821267d503f72126fb1b) - PAX: new implements of PAX filter (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`04a5fc9889d`](https://github.com/apache/cloudberry/commit/04a5fc9889ddd31c8f6647e931fdf9411f4eb9c1) - Fix two toast issues that may cause crash (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`23fa2c6e6dc`](https://github.com/apache/cloudberry/commit/23fa2c6e6dc8424f1e6d576a47ddc97f26e356cc) - Performance/improve pax insert performance (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`f835d47a1fa`](https://github.com/apache/cloudberry/commit/f835d47a1fae587abce081ac1fbad1cea824127d) - PAX: don't detoast unread and short header datum (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`4730bf183d6`](https://github.com/apache/cloudberry/commit/4730bf183d6115a119592b68dacd3d97918fe42f) - Fix index scan using bitmap index on paxr #137 (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`1ce47e49041`](https://github.com/apache/cloudberry/commit/1ce47e49041b8a6827b52713fe753385c79da048) - PAX: Fix error that parallel scan ignores toast files (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`a43e647b8e6`](https://github.com/apache/cloudberry/commit/a43e647b8e6a542b8ef6b92038340793b7547003) - Fix: can't insert into pax auxiliary table after blockid more than 32768 (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`5d91bd643bd`](https://github.com/apache/cloudberry/commit/5d91bd643bd5f700405c3a4abaabbb67cd90b458) - Fix: PAX fast sequence will return the same seq in concurrent case (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`394603a1df3`](https://github.com/apache/cloudberry/commit/394603a1df3708b4249727615e5427d4ceab493e) - PAX: support python3 api (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`c4cfe6ea948`](https://github.com/apache/cloudberry/commit/c4cfe6ea948dfc370e8e22a46c597eb36b1c31fe) - PAX: re-enable install libpaxformat and header (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`2369deceae7`](https://github.com/apache/cloudberry/commit/2369deceae7292248eaa617c1953bac2a4782763) - Regularly synchronize CBDB and update CI (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`8e748af0d76`](https://github.com/apache/cloudberry/commit/8e748af0d76360a5b65c5b367577231753c78e2b) - Fix crash when insert large column value and pax_enable_toast is off (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`4c2ed58f15e`](https://github.com/apache/cloudberry/commit/4c2ed58f15e35ab89cfbfc57425f3590730b5d13) - Remove the function assign_enable_vectorization (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`178ce9f12ff`](https://github.com/apache/cloudberry/commit/178ce9f12ff836b541ed98af80aaeec04ae57c9f) - Fix parallel scan for pax tables (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`5dbcdbebe5c`](https://github.com/apache/cloudberry/commit/5dbcdbebe5cd6fbe81ccb2a56b06a4f445878e8f) - Fix the icw test failure case of pax under orca optimizer (GongXun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`93890eb97b3`](https://github.com/apache/cloudberry/commit/93890eb97b35131aab20043706e22ed4c4cbf0f9) - Fix answer files when the collation changes to en_US.utf8 (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`dd57eff8933`](https://github.com/apache/cloudberry/commit/dd57eff8933e4421bfd52353000633a3ce20a905) - PAX: move the CBDB_CHECK into MicroPartitionStats::FromValue (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`01fc7a9b53c`](https://github.com/apache/cloudberry/commit/01fc7a9b53c9372bdbd4d42c28df70d5bf0c04e2) - PAX: Support IN expression filter for vector scan (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`4f8e4f68ee5`](https://github.com/apache/cloudberry/commit/4f8e4f68ee5a4b75df1a37c1d028111e64c9f583) - PAX: disable numeric in bloom filter (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`ffb00403f0a`](https://github.com/apache/cloudberry/commit/ffb00403f0ab6a408b83a18872c4c1b7af093f17) - PAX: Fix compile issue that may install bad pax.so (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`57b5cea2993`](https://github.com/apache/cloudberry/commit/57b5cea2993274a699ca6893961ee954779d3be8) - PAX: Remove the gtest mock `Palloc/Palloc0/Pfree` (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`8764842bfb7`](https://github.com/apache/cloudberry/commit/8764842bfb78b19a8c35aa7c8f81c5cac8d47eba) - Fix UFileClose failed, call UFileGetLastError to get error msg will coredump (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`981efa20a11`](https://github.com/apache/cloudberry/commit/981efa20a114584512de9899ec2906d29fd903cc) - PAX: Add icw test in greenplum_schedule for pax table (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`aa650155c72`](https://github.com/apache/cloudberry/commit/aa650155c72037b4c79d7c01c2d235f47b11045b) - Regularly synchronize CBDB and update CI (Zijie) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`35f267f1c41`](https://github.com/apache/cloudberry/commit/35f267f1c41c5e19af7d22cb9316b974f0acd1d8) - PAX: support more types for filtering in parallel scan (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`8dc6588e8e8`](https://github.com/apache/cloudberry/commit/8dc6588e8e898ca74defc3777e242f1278f9cd67) - PAX: Support lexical cluster (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`c0d02848a29`](https://github.com/apache/cloudberry/commit/c0d02848a29866987e5ab18b5b8ab63b9c982e76) - Regularly synchronize CBDB and update CI (wangxiaoran) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`3db4834a09a`](https://github.com/apache/cloudberry/commit/3db4834a09aac22d48831a47b6d8e678fead91b1) - PAX: support almost min/max oper (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`dec59493ed3`](https://github.com/apache/cloudberry/commit/dec59493ed3c363fbf2062e72a9a3cc589ad066f) - PAX: Support parallel scan pax table by threads for vectorization (yangkaidi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`48e14d7ade0`](https://github.com/apache/cloudberry/commit/48e14d7ade075d9a96b9ebeb4dad919df9a20fed) - PAX: Move the logic of checking whether stmt is supported in dfs tablespace to pax (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`a48f5f647ee`](https://github.com/apache/cloudberry/commit/a48f5f647eea8f0bae1a5bc642a1abbb556ca52e) - PAX: Support bloom filter in the statistics (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`11ff06c8c36`](https://github.com/apache/cloudberry/commit/11ff06c8c36142a80df96dbf45ede5b1a95abe28) - PAX: remove the opfamily and build scan will not use the BRINAM (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`bf7745d39ba`](https://github.com/apache/cloudberry/commit/bf7745d39ba50313dcfb6ff9cdf76f721a25475d) - PAX: Refactor memory management to allow thread-safe scan (wuhao) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`b5f13402d15`](https://github.com/apache/cloudberry/commit/b5f13402d15fdd83a82eba62bc81d81810e30495) - bugfix: tuples loss issue in bitmap index scan (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`57420be1429`](https://github.com/apache/cloudberry/commit/57420be14292dee6a675934c362ce7fd8f1fa6b5) - PAX: Support parallel-scan (gongxun) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`733f3cc8e72`](https://github.com/apache/cloudberry/commit/733f3cc8e7292d2327ea0324e9b5e05494f858c7) - Regularly synchronize CBDB and update CI (Hao Wu) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`5b3d9a26585`](https://github.com/apache/cloudberry/commit/5b3d9a26585b57ec0ffe8d23242a8ce140d1b157) - PAX: support record batch return with dictionary (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`1ff348eff76`](https://github.com/apache/cloudberry/commit/1ff348eff763e0134f219912b3e6aeb437788d8f) - PAX: Support dict encoding (zhoujiaqi) [#1044](https://github.com/apache/cloudberry/pull/1044)
* [`90a96f5db32`](https://github.com/apache/cloudberry/commit/90a96f5db32fd1ddfa5450c3d3398e13c6767abd) - PAX: The type of ptblockname is changed to int type (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`d95aa2b0e91`](https://github.com/apache/cloudberry/commit/d95aa2b0e911526145eaf04ddddfa4f40ec48fae) - Fix: pfree may free NULL datum (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`8564339f93a`](https://github.com/apache/cloudberry/commit/8564339f93aa77e64192571e9dc58d09c9fa297e) - PAX: support varchar min/max operator (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`7523cf05dfc`](https://github.com/apache/cloudberry/commit/7523cf05dfcde7e06d992f230c6c7ca2da402ccc) - PAX: reset row filter memory context to avoid OOM (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`9b843df4b86`](https://github.com/apache/cloudberry/commit/9b843df4b861fe6c1c5b0ba5889d6a946c7edfd6) - PAX: Support zorder curve (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a1c84c8e339`](https://github.com/apache/cloudberry/commit/a1c84c8e339f65839303cce1423e9516a6a94d82) - pax: suport index cluster (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`51937f5a891`](https://github.com/apache/cloudberry/commit/51937f5a891e9f542c1286d893e947aa72013804) - PAX: Disable the unsupport indexes (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`5c16f81c1f4`](https://github.com/apache/cloudberry/commit/5c16f81c1f450e421e66482960a29bfe887c14e2) - PAX: enable kind of UDF to dump the PAX file (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`eb5ea42b22f`](https://github.com/apache/cloudberry/commit/eb5ea42b22fa82e2cde8cff950426189ea2fb433) - PAX:  AM can't insert into table which use object storage (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`c320755fb94`](https://github.com/apache/cloudberry/commit/c320755fb94f079bc7cbde78407723b57fb3bef8) - PAX: pax_dump support dump toast datum (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`733943ee586`](https://github.com/apache/cloudberry/commit/733943ee5863c55e29354566fecc1c88b35156e3) - PAX: support sum/count pb info combine (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`de0c3cd8bbb`](https://github.com/apache/cloudberry/commit/de0c3cd8bbbad0f457b81b6e005ba3cb327a75b6) - Regularly synchronize CBDB and update CI (Zijie) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`d996c2ad1d4`](https://github.com/apache/cloudberry/commit/d996c2ad1d4a87f94b0b12655f7a8cc4cb2eefee) - PAX: support update stats when delete/update happend (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`5a6eb2d9ead`](https://github.com/apache/cloudberry/commit/5a6eb2d9ead3d492509758deac626c14b9fe91a4) - PAX: re-enable the sql tests which defined in pax-tests target (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`ad3e6878178`](https://github.com/apache/cloudberry/commit/ad3e6878178990bae834348a8ff1c1b45c1ef006) - Ignore extension misc_sanity case primary key check. (Zhang Mingli) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`0b1a75b82c6`](https://github.com/apache/cloudberry/commit/0b1a75b82c6bcca2eb41d39507a4f871cacd5ec9) - PAX: Support SUM and COUNT in statistics (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a7d369d9236`](https://github.com/apache/cloudberry/commit/a7d369d92368e12fedadfa4f08b373f324dc621c) - PAX: add existexttoast to summary callback (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`243f420ed35`](https://github.com/apache/cloudberry/commit/243f420ed350e689b0b9d63d614b4b9bb3ba6d2a) - PAX: enable porc_vec format ICW (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`996e17002ec`](https://github.com/apache/cloudberry/commit/996e17002ec3f78876e9219d35f31623a0bcb91c) - PAX: add more exception message (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`32fbc33c915`](https://github.com/apache/cloudberry/commit/32fbc33c915f49e1681ba1454f751498ef2bafe4) - PAX: support porc_vec format no filling datum header when typlen is not -1 (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a50763d055f`](https://github.com/apache/cloudberry/commit/a50763d055f6ee54f199a9f63a2bc77cfacbe344) - PAX: icw support vectorization regress test cases (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4e84f4f7bd1`](https://github.com/apache/cloudberry/commit/4e84f4f7bd156ae7043301a17edad9d9b2e70f22) - Fix PAX scanning with visimap for vectorization (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`701d0df01d6`](https://github.com/apache/cloudberry/commit/701d0df01d6b797cdcffe1f635b375b520971cfd) - PAX: plasma is no longer supported (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`ef85d33e70e`](https://github.com/apache/cloudberry/commit/ef85d33e70e43928583dbb9e5c97fe48adc6fd16) - PAX: refactor vec adapter and split the format into different paths (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`5867dc3b866`](https://github.com/apache/cloudberry/commit/5867dc3b8662474e2b2182778a6fdd15569dd291) - feature: support dfs_tablespace in pax storage (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`69aae821443`](https://github.com/apache/cloudberry/commit/69aae82144343f74a019c544920b5063568ac3b1) - Fix: length 0 and all null bpchar in record batch (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`7fc94f376c1`](https://github.com/apache/cloudberry/commit/7fc94f376c10198fb49a34c9536eeda18cf3d62f) - Op: PAX Precompute null counts array in GetTuple (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`577c86219cb`](https://github.com/apache/cloudberry/commit/577c86219cb056c48f18e882cadc242e9ce37468) - Regularly synchronize CBDB and update CI (Dongxiao Song) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`1e1dff0ff68`](https://github.com/apache/cloudberry/commit/1e1dff0ff68c83c68e97adf64f21deb79d95475d) - PAX: Add SCAN_SUPPORT_COLUMN_ORIENTED_SCAN in scan flags (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a761dfe50f4`](https://github.com/apache/cloudberry/commit/a761dfe50f435f1fed35ced5699b1e44863d0a12) - Fix: compile error when CBDB_BUILD_TYPE is release (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`bc53a0e0924`](https://github.com/apache/cloudberry/commit/bc53a0e0924bcd0a243b2ce57c794258cbd4f8f8) - PAX: optimize min-max memory state (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`92067046ec1`](https://github.com/apache/cloudberry/commit/92067046ec199871d8fc31a2c52649df99239ca6) - Fix nullptr in missing column (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a22d974df84`](https://github.com/apache/cloudberry/commit/a22d974df8458cde49a558d2f5f7fda41dce1d9d) - bugfix: pax support default values when vectorization is enable (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`6abc1f17048`](https://github.com/apache/cloudberry/commit/6abc1f170481c4868874eb60eb85e6e14f9f0d4a) - bugfix: fix compilation errors in pax (GongXun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4f629e0bf12`](https://github.com/apache/cloudberry/commit/4f629e0bf12c035a765e74b54aa04f61714d206f) - Pax: feature support store toast datum (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`499dc26efd3`](https://github.com/apache/cloudberry/commit/499dc26efd3fa6e206484dedc9d58068f4eea2f1) - Fix: pax return wrong value of bool type when vectorization is enabled (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`b21eb23cb9a`](https://github.com/apache/cloudberry/commit/b21eb23cb9a66f181d1832655fb75532ede15cf0) - PAX: bitpacked support porc_vec (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`785b8e1fc9d`](https://github.com/apache/cloudberry/commit/785b8e1fc9dc8dbfb3c337a10cc2c20189d29039) - PAX: bpchar support porc_vec (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`92fa86c27f1`](https://github.com/apache/cloudberry/commit/92fa86c27f1d92f054f4fb2dc884bb6d93491bd8) - PAX: Refactor the writer/reader options (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`245fb79e5e3`](https://github.com/apache/cloudberry/commit/245fb79e5e325793df6da9acdfd1a28218129a1d) - Fix: the max value of pax_max_tuples_per_file is wrong (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`261a29f2c96`](https://github.com/apache/cloudberry/commit/261a29f2c96cc0350644499d173f939123af442f) - Pax: TupleDesc in TableTupleSlot is no longer used (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`c65d6573e0c`](https://github.com/apache/cloudberry/commit/c65d6573e0c6d4776a4b11912c7162200fe6f8ee) - Fix: use BITS_TO_BYTES to calculte the length of bitpacked buffer. (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`7a3d04f73dd`](https://github.com/apache/cloudberry/commit/7a3d04f73dd91d9a1bd055919a78bf7dbce27beb) - PAX: Fix visibility map to build ctid for vectorization (wuhao) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`c9d88e737b2`](https://github.com/apache/cloudberry/commit/c9d88e737b2a674f84cf4ae7a7871ebc756af64c) - Pax: add new interface to combine two of MicroPartitionStatisticsInfo (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`bab49fda464`](https://github.com/apache/cloudberry/commit/bab49fda46470e142fd9f338f0d335babba864d0) - Regularly synchronize CBDB and update CI (zhangwenchao) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`c9a863b0a28`](https://github.com/apache/cloudberry/commit/c9a863b0a28c14e4567338997bf2d91668023bb5) - Bugfix: pax fix coredump with vectorization enable (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`2e2c727ca0c`](https://github.com/apache/cloudberry/commit/2e2c727ca0cffd21a753f537e5833599c30207b0) - Fix: The cache_group cache hit condition is incorrect. (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`f5ff4eaea5c`](https://github.com/apache/cloudberry/commit/f5ff4eaea5c4a46cf02f4c7f4713ab5aa03283a8) - PAX: Fix compile warnings and enable -Wextra and -Werror (lizhaohan) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`24f7ca23194`](https://github.com/apache/cloudberry/commit/24f7ca231942adf0316ed58492955a7fe032c55e) - PAX: install pax_dump into rpm (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`027e2997863`](https://github.com/apache/cloudberry/commit/027e299786354e6d0fc71e63a69bbaf81b7fc87e) - Fix: link paxformat.so error (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`968a42dabf7`](https://github.com/apache/cloudberry/commit/968a42dabf7430423072b00df5d11da3338bd695) - PAX: Improve the performance of analyze (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a8f7a6e5840`](https://github.com/apache/cloudberry/commit/a8f7a6e5840c96b43d74c396cb69513bf5496aba) - Feature: pax length stream support encoding options (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`9a042f50a61`](https://github.com/apache/cloudberry/commit/9a042f50a6105f9bdae77fced744fcfab1c24957) - Pax: storage format porc_vec support big group (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`51dc8fe3c43`](https://github.com/apache/cloudberry/commit/51dc8fe3c4345fce7d3b344c4c769e15ae5a04b7) - Feature: PAX support visibility map for delete/update (GongXun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`9c3f92eec25`](https://github.com/apache/cloudberry/commit/9c3f92eec25c2685c42026b7c4d9bf7dc71fa0d6) - PAX: Update dependency management (Hao Wu) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`178d1638242`](https://github.com/apache/cloudberry/commit/178d16382422c3ea44eed317bb2f9d52f55eb54a) - Pax: merge reloption numeric_vec_storage to reloption storage_format (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4406272b0a4`](https://github.com/apache/cloudberry/commit/4406272b0a4b4fd2fc12f9aeefb21099f6e7d2c7) - PAX: Add reloption to set minmax columns (wuhao) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`2067855a090`](https://github.com/apache/cloudberry/commit/2067855a090c57a06174faf77e3ecda51885c40f) - bugfix: incorrect data stream origin length of bpchar type without compression (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`df6db7965b0`](https://github.com/apache/cloudberry/commit/df6db7965b03851b10f0da4131d120780ae607ba) - PAX: Add bitpacked column for arrow boolean type (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`776892ec0b6`](https://github.com/apache/cloudberry/commit/776892ec0b67ed174d6a709ba704b3844b4e51e2) - PAX: add single CI to run icw and icw-orca with access method PAX (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`9c2098c82e6`](https://github.com/apache/cloudberry/commit/9c2098c82e600fab56c2ee44792621ec3fb8e033) - Fix: parallel_schedule ORCA test cases (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`c8af47e4002`](https://github.com/apache/cloudberry/commit/c8af47e4002d6e0e57c3d51bc4c63fd2c2b677ac) - Fix: fd handle owner ans some of test cases (GongXun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`31af89cc950`](https://github.com/apache/cloudberry/commit/31af89cc9500178a4af975df60361cf431264411) - Fix: parallel_schedule pg optimizer test cases (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`ae5240315d8`](https://github.com/apache/cloudberry/commit/ae5240315d8529f2636351f867cc4c5f9aee84c2) - pax: port regress tests to pax (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`dfa3b627be5`](https://github.com/apache/cloudberry/commit/dfa3b627be5eeb1aaf33a047c40b79bca5748453) - PAX: Rename orc to porc (wuhao) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`eb2c0771e4b`](https://github.com/apache/cloudberry/commit/eb2c0771e4b68e6c63ba1cee7903714305dc66ce) - feature: skip read the column which is all null (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`3d3a1293807`](https://github.com/apache/cloudberry/commit/3d3a1293807c4edec43948b5dae2806602bf00e8) - Pax: RelationCopyData need do pending delete (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`f2f11052d14`](https://github.com/apache/cloudberry/commit/f2f11052d148441e907dcb7bb99ea598bf71cd97) - bugfix: pax filter does not filter NaN values correctly (GongXun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`bb32d8d6fbf`](https://github.com/apache/cloudberry/commit/bb32d8d6fbfbcdeba400c9230c7e7471af668682) - PAX support bpchar type vectorization build (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`77cb52b11ec`](https://github.com/apache/cloudberry/commit/77cb52b11ec722a2a3bc179cab42f806f68d72e2) - Fix: pax update table with dropped column return not-null tuple (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`42314c12700`](https://github.com/apache/cloudberry/commit/42314c1270032232515b1f5fc2f3fc48de061f30) - Fix: new add column may cause array bounds violation (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`d8223d6f1f0`](https://github.com/apache/cloudberry/commit/d8223d6f1f08179afcc98f2b6c90fc33788e19cb) - Fix: the projection in orc group not right (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`01e9b897d44`](https://github.com/apache/cloudberry/commit/01e9b897d443583ec7ca593d266ade206ce99956) - Fix: PAX format need build CTID (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4eaa62586cf`](https://github.com/apache/cloudberry/commit/4eaa62586cf4e8728bd3578612666a6b8b64b0fa) - PAX: doesn't support non-btree index (Hao Wu) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`84e3216ac3b`](https://github.com/apache/cloudberry/commit/84e3216ac3b11bd9bb132c0a0c669ca756aee0e0) - PAX: Preload pax extension and sync some GUCs (Hao Wu) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`da5515e54e0`](https://github.com/apache/cloudberry/commit/da5515e54e0aeb04dd66dc5525989a8a2d73424a) - Use scan_flags to indicate whether the table AM support vectorization (leo) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`cb665ed6871`](https://github.com/apache/cloudberry/commit/cb665ed68713dcf6e0ebcee5face63e0c92af274) - PAX: paxformat supports vectorized scan operators (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`db5ebc94f56`](https://github.com/apache/cloudberry/commit/db5ebc94f56b2537411ed977fbca7f97783c8a35) - PAX:optimize queries with large wide tables (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`2a4b33d9b9d`](https://github.com/apache/cloudberry/commit/2a4b33d9b9dbe63e27f7f4d6e059410388440f2a) - Fix: PAX numeric vec format memory leak (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`08b94357364`](https://github.com/apache/cloudberry/commit/08b943573642ae4e076ccf3637e0336c4453f945) - Fix: pax custom object support some of event_trigger callback (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`93db87cdbba`](https://github.com/apache/cloudberry/commit/93db87cdbba578eebbc663423d983405ae8a83a5) - Bugfix: fix incorrect micro partition file skip logic (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`d139ee5a7b2`](https://github.com/apache/cloudberry/commit/d139ee5a7b2e5347ce4612f31be9dadb53e9c57b) - Pax change the default file split strategy to guc (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`fba34c28710`](https://github.com/apache/cloudberry/commit/fba34c2871060e4aba90b934294739c69e287e1d) - Fix: PAX will cache the wrong text locale (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`5b59834016f`](https://github.com/apache/cloudberry/commit/5b59834016f24562da55c587cb733656e443cf4b) - PAX: Fix null test for all columns (Hao Wu) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`a3a6852c9c3`](https://github.com/apache/cloudberry/commit/a3a6852c9c3cc1afb8e44cb2c5781e941975764a) - Enable pg statistics in pax (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`f5050e26eac`](https://github.com/apache/cloudberry/commit/f5050e26eac9b2c0783234a149f343d0dd12fa8c) - Fix: PAX split by file size does not calculate the written sizes (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`01e13909893`](https://github.com/apache/cloudberry/commit/01e13909893d5cd5a67d7b81e93c5e05e6a13386) - Fix: pax cluster index will make aux table invisible (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4806abb424a`](https://github.com/apache/cloudberry/commit/4806abb424a85c14a8b4c1093a86463f6c49dddb) - bugfix: check resource owner in FdHandleAbortCallback (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`f6e907a3fab`](https://github.com/apache/cloudberry/commit/f6e907a3fabafd0011649e543779538f32e02054) - PAX: check the value of gp_interconnect_queue_depth and warning if it's less than 64 (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`43ecd5a9333`](https://github.com/apache/cloudberry/commit/43ecd5a9333ed3ee7ffbe4e919a927c5a2557651) - Fix: fix pax-storage make install will compile again (liushengsong) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`1e9e624a9da`](https://github.com/apache/cloudberry/commit/1e9e624a9dab7380fa37058d35dcf9eccdb74d7d) - Fix: the index in pax aux table should be same as aux table (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4b145e3c918`](https://github.com/apache/cloudberry/commit/4b145e3c91864072bd04460bd74e063651fbe0f0) - Fix: pax fast sequence will lost when swap table happen (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`e95fe1c17ed`](https://github.com/apache/cloudberry/commit/e95fe1c17edfe8b5bfb998017ac8af362c8d56b7) - bugfix: Pax storage format fixes incorrect mapping of pg timestamp/timestamptz types (gongxun) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`cc0e4b231aa`](https://github.com/apache/cloudberry/commit/cc0e4b231aa28cfa45b70027605b66d7a64da448) - Fix: pax got nullptr in table writer when exception happen (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`ef5eb08d4ea`](https://github.com/apache/cloudberry/commit/ef5eb08d4ea5d995f88ec741c382dd910b1d400d) - Feature: pax store the vec-format decimal128 (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`4032ed23201`](https://github.com/apache/cloudberry/commit/4032ed232013e866c4493a78b3f1da8d75263af5) - Fix: pax broken gtest and add it into CI (jiaqizho) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`72539b41644`](https://github.com/apache/cloudberry/commit/72539b416444fd93b85f7d6e60fa8748f519303c) - Pax: add MicroPartitionWriter merging logic back to optimize performance (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`707cb0ffa7e`](https://github.com/apache/cloudberry/commit/707cb0ffa7e81f12aa2a7d0296eed45da056ec8b) - Add ExecClearTuple before read or fetch index tuple (zhoujiaqi) [#1043](https://github.com/apache/cloudberry/pull/1043)
* [`f967400dcd2`](https://github.com/apache/cloudberry/commit/f967400dcd277b989f2b2655c162757c53b2c182) - Fix: pax operator without set null field also should not assert unimplemented sk_strategy (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`eb33022c4cc`](https://github.com/apache/cloudberry/commit/eb33022c4ccb2b07a5b26621bf43c0044e67af53) - performance: length_stream changes from int64 type to int32 type (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`a08be4d5354`](https://github.com/apache/cloudberry/commit/a08be4d535474378244d492fe4d96394a745afe7) - Fix: pax group footer will not reocrd compress level (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`33a0beada41`](https://github.com/apache/cloudberry/commit/33a0beada41248792efdc4f12826f1326f211293) - Fix: pax object access hook assert false (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`1fd368c53d6`](https://github.com/apache/cloudberry/commit/1fd368c53d6520f4ca6efa94f24aee32956d6090) - Op: Make the functions in DataBuffer inline (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e13ff9d0dfd`](https://github.com/apache/cloudberry/commit/e13ff9d0dfde54a42910b87350e41b14e5f84e9c) - Fix: alter PAX table will got cast problem (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`d8ff501790b`](https://github.com/apache/cloudberry/commit/d8ff501790b570e0a80625e613f2f9ef20fa065a) - PAX: Fix guc issue that needs to sync pax_scan_reuse_buffer_size (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`712eb89c8a8`](https://github.com/apache/cloudberry/commit/712eb89c8a8fb53c3e94d5b0e1d25228b469c73a) - Fix: toast will make pax operators not work (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`9f35c843ac5`](https://github.com/apache/cloudberry/commit/9f35c843ac52b4fe6f5a71ac0b92bd239383c0a2) - PAX: Add a configure option to build pax optionally. (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`a65488a3964`](https://github.com/apache/cloudberry/commit/a65488a3964de7392716f3b5ced9e25f64a19f17) - Pax: Add regression tests (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`149dfc18b7f`](https://github.com/apache/cloudberry/commit/149dfc18b7fc3ed8ec4b5075245fe9b61123d16d) - PAX: Fix guc name to not contain dot (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e61f64dbe68`](https://github.com/apache/cloudberry/commit/e61f64dbe680b7d7954c7546fbacbcb1763765c1) - Fix: delete pax_itemptr.cc in build libpaxformat.so (liushengsong) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`db42e7a987d`](https://github.com/apache/cloudberry/commit/db42e7a987d4b1012cdb44dafd816877c4b419ec) - Pax: Avoid stack overflow when reading footer (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`fa5dce83073`](https://github.com/apache/cloudberry/commit/fa5dce830738f79a0d08ff9008b53dca3e70b1e5) - PAX: Limit the maximum number of concurrent jobs to 8 (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`386ccfb0bbb`](https://github.com/apache/cloudberry/commit/386ccfb0bbb507b3ca4068da9ae45f27c6e225a4) - Fix struct layout of PaxIndexScanDesc (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`75d66b55e6c`](https://github.com/apache/cloudberry/commit/75d66b55e6c365d31caa5b083bfef410ed14635c) - PAX: Use macro SO_TYPE_VECTOR instead of hard-coded value (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`f4d2f9de559`](https://github.com/apache/cloudberry/commit/f4d2f9de5593ef145f7b5dcd4ee39b2e9ba75143) - PAX: Add new guc pax.enable_filter (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`96bc41e9ca4`](https://github.com/apache/cloudberry/commit/96bc41e9ca481e205b30d40b4e7944eb7e0df989) - Enable local index by default and remove the else code (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`29d227432a3`](https://github.com/apache/cloudberry/commit/29d227432a35d3d78ebc915387c997a131c55a5b) - Fix: install the libpax.so, libpaxformat.so, and headers into `${prefix}` (liushengsong) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`9ebdd217221`](https://github.com/apache/cloudberry/commit/9ebdd2172219002ffa192ed2f972a69f46e79437) - PAX: Rename the namespace of orc proto objects (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e2cc5d6cc70`](https://github.com/apache/cloudberry/commit/e2cc5d6cc7063a1a1997880a21f075d1f4b13c68) - PAX: Reduct get relation path function call times (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`db893523857`](https://github.com/apache/cloudberry/commit/db893523857ad8c4a02140c73542f7950e7a7e35) - PAX: both build pax.so and paxformat.so (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e262d1e897c`](https://github.com/apache/cloudberry/commit/e262d1e897ce5570ca6e164fe50dde8da6b15d15) - Pax: Remove the struct CTupleSlot (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e6b1c2d5aa2`](https://github.com/apache/cloudberry/commit/e6b1c2d5aa26152eac39349801759cc5af043c87) - Pax build type follow lighting pipeline (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`337a044bc24`](https://github.com/apache/cloudberry/commit/337a044bc2474f4a02a83b95b28707ab44c7a678) - Feature: use stripe stats instead of repeated mix/max update operations (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`642cfb0118d`](https://github.com/apache/cloudberry/commit/642cfb0118d485b0221a1cfb7ca80eccffed1c9f) - Pax/remove zstd submodule (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`ddfbc940317`](https://github.com/apache/cloudberry/commit/ddfbc940317624133c727cbdce942438e862d649) - PAX: Add PAX_NEW/PAX_DELETE to replace global new/delete operator (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`6192ca316ec`](https://github.com/apache/cloudberry/commit/6192ca316ec5230394ed1d03fe81353e26b4ee91) - Use dynamic link library instead of static compilation (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`6cab69f1026`](https://github.com/apache/cloudberry/commit/6cab69f1026b183971ad97b6edddfb8995c55d27) - Feature: implementing part of PG min/max operator in PAX (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e4e05b560a1`](https://github.com/apache/cloudberry/commit/e4e05b560a1d5ed7d4f1352950d864c6ef38c5fc) - Feature: pax FD resource owner supports concurrent reading and writing (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`6261905d348`](https://github.com/apache/cloudberry/commit/6261905d3481acc5ab3e1b01f6957f0a26787ae7) - bugfix: fix the error when hashdata cloud work with paxformat (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`4f526ee3c71`](https://github.com/apache/cloudberry/commit/4f526ee3c714b8582f61a712a88dcb427f4c3d1c) - PAX: Add google benchmark (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`c5bcd5f5f73`](https://github.com/apache/cloudberry/commit/c5bcd5f5f73983dbd78fceece1b171b42628e3cc) - Feature: Introduce pax_dump to dump single pax file (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`9da56e7404f`](https://github.com/apache/cloudberry/commit/9da56e7404fbd94a407d6196b6ef593541f3ec1b) - PAX: Compile pax with vectorization only if the kernel builds vectorization (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`5c375f40b42`](https://github.com/apache/cloudberry/commit/5c375f40b42440a33af1d5db690c0a14224fe0f6) - Pax re-enable vectorization build (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`824fca9277c`](https://github.com/apache/cloudberry/commit/824fca9277cc48b49f0fb431a3faa13a1112ac11) - PAX: Build pax_storage as an internal extension by default (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`126d10f891d`](https://github.com/apache/cloudberry/commit/126d10f891d2df075394268afac27d76acc3d694) - Dynamic reuse_buffer size (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`fc5fe5d96e5`](https://github.com/apache/cloudberry/commit/fc5fe5d96e5ba68193fe1005a93ee87cf404c401) - PAX: Always create index for auxiliary relation (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`7e17d821b5e`](https://github.com/apache/cloudberry/commit/7e17d821b5e4d1bcbf58bbd4bffbbf851461dd65) - Op: cleanup some of pax tests, replace with the same function (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`2e649edf08c`](https://github.com/apache/cloudberry/commit/2e649edf08ca8717b1453947188ab23bce992f33) - Move PAX repo (Max Yang) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`f04b69157de`](https://github.com/apache/cloudberry/commit/f04b69157ded1aa625ae83ee18ee5d39d84cf115) - Update the signature of TransformColumnEncodingClauses (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`ab1039d9524`](https://github.com/apache/cloudberry/commit/ab1039d952436b5aaf46ed0e5e4c2dcd1e6f5b2d) - Move pg_pax_tables from kernel to pax (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`bdffc6a8754`](https://github.com/apache/cloudberry/commit/bdffc6a8754f83f834c8370dbb366e3daf678cd0) - Fix compile error in release mode (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`81c016ce91a`](https://github.com/apache/cloudberry/commit/81c016ce91aad3e82084160e6bd2fa5acd7e6e11) - Fix: RUNPATH in the so file (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`b6041ddb5c5`](https://github.com/apache/cloudberry/commit/b6041ddb5c505c3951ab822e13110ed4734b0938) - Fix: Invalid read in MergeGroup (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`9ebc110e87d`](https://github.com/apache/cloudberry/commit/9ebc110e87d88914946c15c58974a8a94ab61e34) - PAX: Fix compile error (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`09ebf85d35c`](https://github.com/apache/cloudberry/commit/09ebf85d35c6770110133948f085221bd44085f9) - Fix: missing attrno in row filter reader (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`13baee07379`](https://github.com/apache/cloudberry/commit/13baee07379b88c110dbe575e1941dd510e12f05) - PAX: Add regress test (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`3ae4e906123`](https://github.com/apache/cloudberry/commit/3ae4e90612334464286f2c5d9efa8d4db1921ba0) - PAX: Implement index_unique_check (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`53a4e571001`](https://github.com/apache/cloudberry/commit/53a4e571001834a22d372e4e5159e544fb8f55e5) - PAX: Use the correctness flags when open file (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e4e2a8a0113`](https://github.com/apache/cloudberry/commit/e4e2a8a0113a7671dc97fa3b81d1206306fd8ca6) - PAX: Allow empty columns in PAX table (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`b09692b62e9`](https://github.com/apache/cloudberry/commit/b09692b62e95ba18a2582250cb6f8124fe0ce76c) - Feature: catch pg error when long jump happend (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`0754407d764`](https://github.com/apache/cloudberry/commit/0754407d7645e598c67421f1bf6a19259352d668) - Fix: group stats is not right after merge (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`22ff75fba0d`](https://github.com/apache/cloudberry/commit/22ff75fba0d0d5ed32296d43cfdb672aef19b420) - Fix: VEC format no need do align in non-fixed column (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`a3da6d53eb5`](https://github.com/apache/cloudberry/commit/a3da6d53eb59c193799a6d42c5b2077136e64c9d) - Feature: support resowner to manage fd (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`69d26b36d41`](https://github.com/apache/cloudberry/commit/69d26b36d41ea5fd4b6d8adc0b48eba3f06b1c6d) - PAX: Add support for index build and scan (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`7434c397327`](https://github.com/apache/cloudberry/commit/7434c397327de5aaa9bf8de855200fc189426ef8) - Generate file name by fast-sequence to support index (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`d46895be661`](https://github.com/apache/cloudberry/commit/d46895be661042992c38ee79dc88a937a5cafe8d) - Feature: support GetTuple interface to direct get tuple (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`ec443e053b9`](https://github.com/apache/cloudberry/commit/ec443e053b983e6da53bdd71fb4bf9f90e82d6da) - Feature: TableParitionWriter support parition reloption (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`8a1c80eead4`](https://github.com/apache/cloudberry/commit/8a1c80eead43cb32a236ccdeb4bbd6562391868e) - Fix: use new item ptr to build vec ctid (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`08f84d6cdc2`](https://github.com/apache/cloudberry/commit/08f84d6cdc240f367b0821e0662d80746bab682c) - Fix: group offset not right (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`6414f1df184`](https://github.com/apache/cloudberry/commit/6414f1df18421239c5408579c8b18dfb6e038cb3) - Refactor ItemPointerData to use index for offline (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`31008498f25`](https://github.com/apache/cloudberry/commit/31008498f25ee48cd047d6ad1543fe061b3388ad) - Feature: filter support AND oper which not been flatten (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`795fd30e3cc`](https://github.com/apache/cloudberry/commit/795fd30e3cc2421b0915738edf32fa88b73411c4) - PAX: Add base partition support functions (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`ee37653126a`](https://github.com/apache/cloudberry/commit/ee37653126ae0f253e1362774268ef24281436b6) - Feature: storage type orc_vec support pg executor (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`04d3ea2fbe7`](https://github.com/apache/cloudberry/commit/04d3ea2fbe7aea767ad589972bbf63f66e62c218) - Save and validate opfamily instead of oids of compare functions (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`66d766f8956`](https://github.com/apache/cloudberry/commit/66d766f895635c84f88d938abd3be39055094b4b) - PAX: Fix CI submodule can't pull (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`d9f3787eb6e`](https://github.com/apache/cloudberry/commit/d9f3787eb6e0e1400d98ef284e2014792e6a1819) - Op: Refactor PaxEncodingColumn and PaxNonFixedEncodingColumn (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`36182d5b8e4`](https://github.com/apache/cloudberry/commit/36182d5b8e45d5f1f1055d8354b758bb7e9c5dfd) - PAX: Fix invalid size alloc (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`09fd35a06af`](https://github.com/apache/cloudberry/commit/09fd35a06af6b3ecfa097e81fa06a7f158d746a3) - Support group filter and row-level filter (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`a4c3797daec`](https://github.com/apache/cloudberry/commit/a4c3797daec579681abcd1b8b811dd509666063c) - Store transformed PartBoundSpec in pg_pax_tables (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`7f5548d0698`](https://github.com/apache/cloudberry/commit/7f5548d069851e368b86ef5100890fb7b85293de) - Feature: add reloptions to support for partition (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`c3a277ecfd8`](https://github.com/apache/cloudberry/commit/c3a277ecfd8c3c31db99c414bff22433ac4881e6) - Fix: build with vec will got undefined symbol (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`6a0f079cc8d`](https://github.com/apache/cloudberry/commit/6a0f079cc8d168c47ac1e26aceddddc3d3564505) - Introduce: cpp-stub to mock global and private method (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`2e20c6b8fb2`](https://github.com/apache/cloudberry/commit/2e20c6b8fb241a8c0ca1e1a175b2286aa16e1963) - Update Table AM function for scan_begin_extractcolumns (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`26305594d2f`](https://github.com/apache/cloudberry/commit/26305594d2f84287e62f81030a380d5c7abe4144) - Fix: Vec reader support group and new bitmap (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`27886d00986`](https://github.com/apache/cloudberry/commit/27886d0098617b16c541888444fd2a2dd92b7875) - Fix: the datum read from disk should follow typealign (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`63078672b89`](https://github.com/apache/cloudberry/commit/63078672b89f7d18f69017788cab26a4a04273d4) - Bugfix: Export the header file that storage_am relies when compiling (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`f8f8892bfcf`](https://github.com/apache/cloudberry/commit/f8f8892bfcf2b4f1625d0f4fd9e4932b91cd8478) - Fix the type oid that left type and right type are not the same (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`252c1b211cc`](https://github.com/apache/cloudberry/commit/252c1b211cc026925415ffae934762f651276b0b) - Adapt pax to arrow change "Change abi interface to strcut from pointer ..." (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e9a4a283482`](https://github.com/apache/cloudberry/commit/e9a4a28348212510f3c28d82c105ffcfc4522954) - Fix: VEC reader support read with ctid (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`dbb202096c6`](https://github.com/apache/cloudberry/commit/dbb202096c6306be53c170ef36550e4f237b93b2) - Feature: Add PAX fastsequence system table (Tony Ying) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`7564653c608`](https://github.com/apache/cloudberry/commit/7564653c6087734d37f3fa398a33227ad7c67bb1) - Feature: mirco-partition support multi groups (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`73b1915a95f`](https://github.com/apache/cloudberry/commit/73b1915a95f1925f6ef162232fd09effdbd6de29) - Feature: orc support tail read (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`048cc5e6ade`](https://github.com/apache/cloudberry/commit/048cc5e6ade572512a3e931f79174155cd28167a) - Add GUC pax.enable_debug and dump debug info (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`2b9642fdfd9`](https://github.com/apache/cloudberry/commit/2b9642fdfd907d6470c224f9d30aa2a1ee9749b9) - PAX: Reimplement bitmap using bits (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`ad923e59545`](https://github.com/apache/cloudberry/commit/ad923e595458b649bcefa63e8bce2bc8b32a99f3) - Fix: Partial Agg may cause unpin twice (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`64e60d2bfa2`](https://github.com/apache/cloudberry/commit/64e60d2bfa2acaa66f1319505bee413fca6ac72c) - Feature: support cache pax columns result (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`5cbdada502b`](https://github.com/apache/cloudberry/commit/5cbdada502bf44303928ac5fd553a42140f5d90d) - Feature: Introduce pax cache (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`834b4e222de`](https://github.com/apache/cloudberry/commit/834b4e222de6ce2b80335b741782df9314cf35c0) - Fix: RLE encoding out-of-bound problem (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`b8820678456`](https://github.com/apache/cloudberry/commit/b8820678456a0e7fc975c8c29e07b0ef332fe98a) - Fix analyze crash and incorrect pg_class.reltuples (wuhao) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`d582eb8555d`](https://github.com/apache/cloudberry/commit/d582eb8555d3cf758baa7760434ea9d9e1121c31) - Relax typid check for empty data file (Hao Wu) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`711892d3fa6`](https://github.com/apache/cloudberry/commit/711892d3fa68541e46026b7691f1c9a3d05fd61d) - Fix: operator may not match opfamily (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`e2ce2f96e3f`](https://github.com/apache/cloudberry/commit/e2ce2f96e3f1881c6b759b2b28c3a330de752d2b) - Feature:pax table support encoding options (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`6c1b1903716`](https://github.com/apache/cloudberry/commit/6c1b1903716f9e95a5ad8bb82d7084ce013db3dc) - Feature: pax am support pass encoding clauses options (zhoujiaqi) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`fd14f6316a7`](https://github.com/apache/cloudberry/commit/fd14f6316a7929c3457aeef081f67969e1dd965c) - Bugfix : Make Pax file directory structure consisitency for set new tablespace case. (Tony Ying) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`73558b3a8eb`](https://github.com/apache/cloudberry/commit/73558b3a8eb94309444c878c3fcaa52fd8c6fc74) - Fix compile errror when BUILD_PAX_FORMAT is ON (gongxun) [#1042](https://github.com/apache/cloudberry/pull/1042)
* [`22aeaed4e5b`](https://github.com/apache/cloudberry/commit/22aeaed4e5b18ac0e44c2f57715f9983bae24f19) - Op: Add a new vectorization MicroPartitionReader to replace ReadVecTuple (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`76069425912`](https://github.com/apache/cloudberry/commit/7606942591271bf9cde06b636716e608a2ce9783) - Op: remove OrcIteratorReader (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`53a6008853e`](https://github.com/apache/cloudberry/commit/53a6008853e133ad7401332df0a209d872bd8153) - Fix: Build got some error with -DBUILD_GTEST=OFF (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`66f953911bd`](https://github.com/apache/cloudberry/commit/66f953911bdbeaaa5a4cfe19126f273d02d9d11a) - Op: Remove previous check before delete (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ea3f1812818`](https://github.com/apache/cloudberry/commit/ea3f18128187e4f8dd30a8461b75223fb5b043fa) - Fix: some am functions have no catch common cexception (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`5ee668aac53`](https://github.com/apache/cloudberry/commit/5ee668aac534389f60337b43d85d65e455c62921) - Feature: PAX support VEC executor (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`c14d2f9ffe1`](https://github.com/apache/cloudberry/commit/c14d2f9ffe1620b36b2e48577a577bb2c1d1a859) - Feature: interface adjustment to support vec implements (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`c807ac590d7`](https://github.com/apache/cloudberry/commit/c807ac590d7384f9cfb6cffee402509309a3292a) - Fix: fs test will got some permissions errors (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`514c066b87e`](https://github.com/apache/cloudberry/commit/514c066b87ef5f9c19ef51cbdab81fcba2e32ed5) - Feature: Add support for micro-partition-level filter (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`b0971ed5eff`](https://github.com/apache/cloudberry/commit/b0971ed5eff9287ce93c508cd026e62433517d04) - CI: change back to cbdb feature-pax branch (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`8f2fc61766f`](https://github.com/apache/cloudberry/commit/8f2fc61766ffe1584fb97e5821d8cc798a44f56a) - Feature: Implement API CopyForCluster (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`8216e58f4a9`](https://github.com/apache/cloudberry/commit/8216e58f4a9c152277f550a7f2f2f1c7c5fa27ae) - Feature: Add pax catalog statistics support (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`bba82efe293`](https://github.com/apache/cloudberry/commit/bba82efe2933458f81d39235c415ab61d8840c84) - Simplify the iterator interface and cleanup some unused code (wuhao) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`bb2211f5224`](https://github.com/apache/cloudberry/commit/bb2211f5224f151d93b2f21436c3113fac4894cd) - Rewrite callback implementation of analyze & cleanup code (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ca6e26abe6e`](https://github.com/apache/cloudberry/commit/ca6e26abe6e59c917572268466a589e451d77dba) - Feature: orc support encoding column (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`4bc16ddf9a1`](https://github.com/apache/cloudberry/commit/4bc16ddf9a119ec9ce4e10872fb3e22c034e877b) - Bugfix: remove unnecessary files when compiling libpaxformat.so (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`3b5ec229cdc`](https://github.com/apache/cloudberry/commit/3b5ec229cdce2514889530943c8df1e3e4747888) - Feature: pax column support encoding/decoding (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ffdd150e662`](https://github.com/apache/cloudberry/commit/ffdd150e662100864e5e7b2fc495d38d7d33f7f2) - Enhancement: reconstruct the directory structure of pax extension (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`4ef6ef33379`](https://github.com/apache/cloudberry/commit/4ef6ef333796e78d630c3e8deac720e91e0ec8ec) - Enhancement: Remove unnecessary interface from filesystem class (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ed7e6b683cc`](https://github.com/apache/cloudberry/commit/ed7e6b683ccb8b7d5a579e461d3d468364cc59f3) - Optimize: New implements column projection in orc (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`5931197c73a`](https://github.com/apache/cloudberry/commit/5931197c73ab3b31e249fb6182054046a1a9113b) - Bugfix: Fix ReadTuple in case scan analyze without projection info (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`f2d47c63a73`](https://github.com/apache/cloudberry/commit/f2d47c63a7349bc55f63034c74096396b7bd8edc) - Enhancement: pax extension uses the 1X_STABLE_CP_FEATURE_PAX branch of cbdb for testing (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`839cdcd79a0`](https://github.com/apache/cloudberry/commit/839cdcd79a0ae773cd7876e1fa8a9d82de4acd9a) - CMake: allow release build if -DENBALE_DEBUG=off (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ba5a865544f`](https://github.com/apache/cloudberry/commit/ba5a865544f0ea6a5b08027e11daf4a5242149c9) - Feature: add PAX projection filter functionality (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`76e949ba2c7`](https://github.com/apache/cloudberry/commit/76e949ba2c71abdfe72cdcaad7b54d12f2b9679c) - Feature: RLE decoding support template (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`45f933bb7ff`](https://github.com/apache/cloudberry/commit/45f933bb7ff5eaa29e963a5a869eb632de97bcf8) - Fix: kExTypeFileOperationError exception missing error message (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`1452b8e92c3`](https://github.com/apache/cloudberry/commit/1452b8e92c30651bcb27914c1339c1cb7afcaefa) - Feature: Introduce encoding && compress interface in pax column (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`72dbc018a44`](https://github.com/apache/cloudberry/commit/72dbc018a44dc17c904e735cc3b7b5e3a91a47fb) - Enhancement: IO functions throw exception instead of returning error code (wuhao) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`adb54b1fd47`](https://github.com/apache/cloudberry/commit/adb54b1fd47c3f663a56fed145985d05b43789c8) - Feature/pax format adapted to storage am (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`21ddfb92562`](https://github.com/apache/cloudberry/commit/21ddfb9256208e6cff0ab4d420fecf41a98c4a0b) - Fix CI will failed in dev branch (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`33f441774d2`](https://github.com/apache/cloudberry/commit/33f441774d29fd4409946614fd0a4461e37c2be5) - Implement an empty PaxAccessMethod::RelationVacuum to able to run VACUUM. (wuhao) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`476b9b7fee0`](https://github.com/apache/cloudberry/commit/476b9b7fee0e4831061a5617458c61d7d4846e20) - Always use the database's default tablespace for the auxiliary relation of PAX table (wuhao) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`d3eaeec437e`](https://github.com/apache/cloudberry/commit/d3eaeec437e8a0d038a22d332b92bf4e073b83b9) - Optimize: ignore paxc_* file in CI clang-tidy check (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`cf08b9f18a9`](https://github.com/apache/cloudberry/commit/cf08b9f18a99c5b742145d12b01c77e52d51a97f) - Feature: Implement pax access method ScanRescan API (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`d4f990993a4`](https://github.com/apache/cloudberry/commit/d4f990993a46b0dadb2b9972638f1a4df9629efe) - Feature:introduce pax memory context (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ea15841b762`](https://github.com/apache/cloudberry/commit/ea15841b762a0b66239e0b5f590980d98bfbd5a9) - Fix SwapRelationFiles that should hold relation lock (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`2dc938842e6`](https://github.com/apache/cloudberry/commit/2dc938842e61076b9305d6bf0f41a74d566f8ccb) - Add a callback for swap_relation_files for PAX (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`fd613016822`](https://github.com/apache/cloudberry/commit/fd613016822c6c34967d42325121924ba7ecc488) - bugfix: fix the incorrect usage of temporary parameter, avoid memory be released (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`8304efea73f`](https://github.com/apache/cloudberry/commit/8304efea73f053cc275e408997becbc8ff6464a1) - Install pax as a second-party extension (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`2bfa8ca79e1`](https://github.com/apache/cloudberry/commit/2bfa8ca79e1e79dcb2dc7e942cdd681725d0fdd7) - Optimize: remove non-continuous write (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`413996b60d7`](https://github.com/apache/cloudberry/commit/413996b60d759c596f0e39d8f3550f9f701216c7) - bugfix: Fixed the error in clang-tidy which the modified file list was read incorrectly (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`33cf7c861be`](https://github.com/apache/cloudberry/commit/33cf7c861be1ff20cc358b89601d6fde0d34a348) - Fix: insert data into the external table will crashes (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`97b2aabd32c`](https://github.com/apache/cloudberry/commit/97b2aabd32c4584e7468c2e9d71effd301e2f492) - Bugfix: Fix drop column update issue (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`271947076f1`](https://github.com/apache/cloudberry/commit/271947076f1eec5e2cd0c462c4488055851c01d3) - Add clang-tidy.result into CI artifacts for debugging usage (tony) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`552fcddb0de`](https://github.com/apache/cloudberry/commit/552fcddb0de49a8059c29f8073d9268edb5545ae) - Feature: support Mirco-paritiition non-transactional delete (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`5127731ba46`](https://github.com/apache/cloudberry/commit/5127731ba464f9abb25cb99cd11bfc81daafc62a) - Fix: byte alignment is necessary when writing data for non-fixed columns (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`5fd3a56ef34`](https://github.com/apache/cloudberry/commit/5fd3a56ef34674a9398108ea5e93ff1a80393aad) - clang-tidy needs to skip files removed by git rm (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`224951f8a0d`](https://github.com/apache/cloudberry/commit/224951f8a0dcf5d4e001bc58c6e58c95fb7398f4) - Feature: Support `add column` ddl in PAX (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`7a0a200a2d2`](https://github.com/apache/cloudberry/commit/7a0a200a2d2d21c32bf393b7f366605153ce3dbc) - Optimize: enable clang-tidy in CI (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`69ae6d4af0e`](https://github.com/apache/cloudberry/commit/69ae6d4af0e79f0f850826d09b5323ceeaa5428b) - Optimize: defined basic code style & Introduce to cpp-tidy (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`6b93200ba11`](https://github.com/apache/cloudberry/commit/6b93200ba116c7af1985a0080badc4ba937a1ffd) - Query with SPI will call the ExecutorEnd multiple times in recursive style (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`2c3fdc32e47`](https://github.com/apache/cloudberry/commit/2c3fdc32e476d5cb83b4fee9e76a969530b7678c) - Enhancement: Replace the binary path of protobuf (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`df3393464a3`](https://github.com/apache/cloudberry/commit/df3393464a3a69464aa06ad35b80b357461dc2dc) - Support create empty table which do not contains any columns (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`0011ea261a7`](https://github.com/apache/cloudberry/commit/0011ea261a76c225004885080ffcaf0386b57c18) - Fix analyze table failed when pax table has null and non-null column values in single block file (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`26caabc6acf`](https://github.com/apache/cloudberry/commit/26caabc6acf032359596650dabbe45bd2a378ec2) - Feature: pax support null field (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`36f8853210c`](https://github.com/apache/cloudberry/commit/36f8853210c5dbe44c2ac0a7ef7905dd9f8efff9) - Feature: support delete/update in pax storage (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`60eeab57730`](https://github.com/apache/cloudberry/commit/60eeab57730d02d4ce32abfe9e997018f4416d81) - Fix pax can not deal non-virtual-tuple (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ab09d3ef9ae`](https://github.com/apache/cloudberry/commit/ab09d3ef9ae26040a40331be3a007fee7c041c7f) - Feature: support more pg-types in pax storage (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ae792c5d9c7`](https://github.com/apache/cloudberry/commit/ae792c5d9c7b2f79b4927c95db0eb56dc300ec84) - Feature: introduce gmock replace simple mock (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`58d78d245e0`](https://github.com/apache/cloudberry/commit/58d78d245e07698047065162840dce17805e709c) - Fix CException won't be catched in cpp (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`003e23f9346`](https://github.com/apache/cloudberry/commit/003e23f9346ee039d7d4cda53b00d337d4e1db04) - Feature: MicroPartitionReader support reused data buffer in scan (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`67e763796a6`](https://github.com/apache/cloudberry/commit/67e763796a6f80d08edceea2d5a511619767dc24) - Fix `SeekTuple` logic error (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`b413bfaf8bf`](https://github.com/apache/cloudberry/commit/b413bfaf8bf743e86aa6a149e948a5479acff317) - Feature: table writer support split strategy (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`3cea7b0ed84`](https://github.com/apache/cloudberry/commit/3cea7b0ed841334b97acef3a699fa519fa505490) - Fix CI without branch (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`a233318b20e`](https://github.com/apache/cloudberry/commit/a233318b20e81af5516d89cd81ab74763cc8178e) - PAX: Run CI job automatically on push (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`e5991f76f22`](https://github.com/apache/cloudberry/commit/e5991f76f22af53b63640e04aa302324c12f1714) - PAX: Better non fixed column (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`2c22b325492`](https://github.com/apache/cloudberry/commit/2c22b3254920d5cd8ae0eb6ebd66cfb8b0def235) - PAX: Better orc seek function (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`cd415e73f48`](https://github.com/apache/cloudberry/commit/cd415e73f480109a72fed24a42512058d3328fa2) - Fix memory problem caused by DatumFromCString (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ce3d36b298d`](https://github.com/apache/cloudberry/commit/ce3d36b298d239d4b1b2cb03d543490bb726cca6) - Bugfix: Create mircro-partition table directory for trancate table case (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`05a1ff8d0e1`](https://github.com/apache/cloudberry/commit/05a1ff8d0e17fb3f46403018717ad2c6e24bd690) - Feature: add PAX CI build & UT pipeline (LINHU YING) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`3142fe6748f`](https://github.com/apache/cloudberry/commit/3142fe6748f4bb121ba998241ff4a16a3434fb60) - PAX: Tuple will in-place updated, so should not keep the memory ptr (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`7494aa5774e`](https://github.com/apache/cloudberry/commit/7494aa5774ed86b598a0985c7c2bc5bc4db6585a) - Bugfix: select empty table raise panic (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`dc42eef6931`](https://github.com/apache/cloudberry/commit/dc42eef6931f4465f7dae77b81cd3acd5648c257) - Bugfix: remove useless gp_debug_linger timewait for debug usage in GTEST (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`f9c45a4e4a9`](https://github.com/apache/cloudberry/commit/f9c45a4e4a9e6b7c63f39a2a14f07b4a611c703e) - Feature: PAX support set new tablespace operation (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`75122c8a525`](https://github.com/apache/cloudberry/commit/75122c8a5252273fcf03a804f7200e81103afead) - Generate SQL to use fixed OID (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`e417ad7aa4f`](https://github.com/apache/cloudberry/commit/e417ad7aa4fc14caddf97f4e1cafd3ac152b8aab) - PAX: Add exception catch for some C++ callback (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`cbd58b3ef28`](https://github.com/apache/cloudberry/commit/cbd58b3ef28bab4a03fc1cd2127e17c55752b5b6) - Refactor scanner: merge scanner and PaxScanDesc into PaxScanDesc (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`8348e4d3b2a`](https://github.com/apache/cloudberry/commit/8348e4d3b2ac2d3b19c2dd003fa0abfa2aacf7d3) - Fix cmake 3.11 will got error cause POSITION_INDEPENDENT_CODE relation (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`1f430635ea5`](https://github.com/apache/cloudberry/commit/1f430635ea5998fc332dee70266d18bcd24fb684) - Feature: port ORC into PAX (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`60967ff684d`](https://github.com/apache/cloudberry/commit/60967ff684d25cba8d4fc01e84a28cce05541818) - Feature: Pax support bulk insert operation (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`811f51c3ef6`](https://github.com/apache/cloudberry/commit/811f51c3ef632ae69ff3042f73b8d20027688eff) - PAX: split namespace to pax and paxc (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`1f5dd327378`](https://github.com/apache/cloudberry/commit/1f5dd327378f849f2408ce86b12e6a8459baffcd) - PAX: Add reloptions support (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`0a14476cbad`](https://github.com/apache/cloudberry/commit/0a14476cbad47645500720f58e899285e643974e) - Feature: implemention anlayze and samping AM interface (chenhongjie) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`936aad94fab`](https://github.com/apache/cloudberry/commit/936aad94fab05bd11ae795da074caeb0292c7d20) - PAX: format headers and their order (wuhao) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`8a3aff6a13b`](https://github.com/apache/cloudberry/commit/8a3aff6a13b49dc76b103426d7573701e61af8e0) - Feature: support table rescan in pax storage (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`e3fef9815d2`](https://github.com/apache/cloudberry/commit/e3fef9815d251f20555a4a6019c50eccdb8ac322) - Feature: Implement RelationSize and EstimateRelSize (chenhongjie) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`3e7afc4fb90`](https://github.com/apache/cloudberry/commit/3e7afc4fb9046c643f2f97c55cfd8d6abf299c9c) - Split table access methods into 2 classes, as static class functions (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`aded17f2c82`](https://github.com/apache/cloudberry/commit/aded17f2c828f256385edee9d2c420861ddd0701) - Update dml_init and dml_fini hooks (Hao Wu) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`e8d756e2184`](https://github.com/apache/cloudberry/commit/e8d756e2184cca06802a8de0862f0cd0a654ad1c) - Read include path from pg_config instead of hardcode. (Max Yang) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`a8cd86314f8`](https://github.com/apache/cloudberry/commit/a8cd86314f8eb5c621ca4d80f49cbba21324a55b) - Feature: catalog add the ptblocksize attribute (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`d427d8aea96`](https://github.com/apache/cloudberry/commit/d427d8aea96a2f65afeb19499959105858d57b93) - PAX: support truncate operation (Tony Ying) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`a4b50c4f84d`](https://github.com/apache/cloudberry/commit/a4b50c4f84d44d615c9cf8a086a98cc38028b1be) - bugfix: block_id is empty in catalog table when insert tuple (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`8f70d6b8de6`](https://github.com/apache/cloudberry/commit/8f70d6b8de6ee9ba8563a0d1929d2d14290adcb0) - Feature: linking libpostgres.so and add some unit testing (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`ebfdb54e2bd`](https://github.com/apache/cloudberry/commit/ebfdb54e2bdb7f39ffb66fd44c5c6a1440bd9f0e) - Feature: support table scan in pax storage (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`9d37c10bd3f`](https://github.com/apache/cloudberry/commit/9d37c10bd3f22b23259734f29a1575b95f684860) - Feature: pax storage support tuple insert (gongxun) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`d7a31c5ebde`](https://github.com/apache/cloudberry/commit/d7a31c5ebde3b8d8c56964487ae33a4dc9ac6a7c) - PAX: Implement local file system operations (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`34273e7da62`](https://github.com/apache/cloudberry/commit/34273e7da62c543ca54c677a403dbb5cd2095523) - PAX: Integrate Google Test (gtest) and Google Mock (gmock) (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`c466d976738`](https://github.com/apache/cloudberry/commit/c466d976738456e59b96d7bc1d0048233794deb3) - Feature: Implement storage layer abstraction with writer/reader interfaces (zhoujiaqi) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`de2cbd9dcce`](https://github.com/apache/cloudberry/commit/de2cbd9dccee0dcd9457336a3080a7cd5941d79d) - PAX: Add initial project structure and core components (jiaqizho) [#1041](https://github.com/apache/cloudberry/pull/1041)
* [`f8a46dc6978`](https://github.com/apache/cloudberry/commit/f8a46dc69782fe6f98ba61b45727327383487e74) - Optimize lock for select-for-update and similar queries (HuSen8891) [#1047](https://github.com/apache/cloudberry/pull/1047)
* [`a1573a244b6`](https://github.com/apache/cloudberry/commit/a1573a244b65f2c02c7aae7e869f73be9dabbe2d) - Update indexscan.c: s/XMIN_COMITTTED/XMIN_COMMITTED (#1040) (reshke) [#1040](https://github.com/apache/cloudberry/pull/1040)
* [`49d49b87eee`](https://github.com/apache/cloudberry/commit/49d49b87eee30fbd9bc777e2717be6bf658d89dc) - SINGLENODE and EntryDB materialized view status maintenance optimization. (Zhang Mingli) [#990](https://github.com/apache/cloudberry/pull/990)
* [`d0b568a2ccd`](https://github.com/apache/cloudberry/commit/d0b568a2ccdd6fc33e63174ee21c5ced23275f68) - Optimize Materialized View Status Maintenance for Partitioned Tables (Zhang Mingli) [#990](https://github.com/apache/cloudberry/pull/990)
* [`198f77585ca`](https://github.com/apache/cloudberry/commit/198f77585ca595ba265cbc42ad76e463818dfdad) - Enable GitHub Discussions for Cloudberry (#1039) (Dianjin Wang) [#1039](https://github.com/apache/cloudberry/pull/1039)
* [`82a35a03f5f`](https://github.com/apache/cloudberry/commit/82a35a03f5f485d1c15f712b77823c6505608e49) - Cleanup: remove old brand name in files (#1030) (Dianjin Wang) [#1030](https://github.com/apache/cloudberry/pull/1030)
* [`7a0775c45c7`](https://github.com/apache/cloudberry/commit/7a0775c45c7ab54c19091d416deab815b733b044) - Fix branch protection config: correct nesting and rename check (Ed Espino) [#1038](https://github.com/apache/cloudberry/pull/1038)
* [`85f206558b2`](https://github.com/apache/cloudberry/commit/85f206558b26716053ef73b9dd12b2a4d189a512) - Enable GitHub Wiki for Cloudberry via .asf.yaml (Dianjin Wang) [#1034](https://github.com/apache/cloudberry/pull/1034)
* [`3cd8f57acda`](https://github.com/apache/cloudberry/commit/3cd8f57acdaf16f1d969674d06362981ae7855fd) - Do not disable the locking optimization for single node (HuSen8891) [#1033](https://github.com/apache/cloudberry/pull/1033)
* [`77863a64c43`](https://github.com/apache/cloudberry/commit/77863a64c43117f64f9fdd90176f707ee6417255) - Optimize MV invalidation overhead using reference counting. (Zhang Mingli) [#1029](https://github.com/apache/cloudberry/pull/1029)
* [`612ef79d4c8`](https://github.com/apache/cloudberry/commit/612ef79d4c8a65e481dfcf05f5218268430002e9) - Check whether attr_encodings is null when call AddRelationAttributeEncodings (#1001) (Xun Gong) [#1001](https://github.com/apache/cloudberry/pull/1001)
* [`f10e4934d93`](https://github.com/apache/cloudberry/commit/f10e4934d932fecd595f242c333b1c5c37695601) - Adjust the loading order of preload (#1027) (liuxiaoyu) [#1027](https://github.com/apache/cloudberry/pull/1027)
* [`f17138e452c`](https://github.com/apache/cloudberry/commit/f17138e452ce98437ced950d3bc3da695b50ff44) - Fix potential overflow in binary search mid calculation (Jianghua Yang) [#1028](https://github.com/apache/cloudberry/pull/1028)
* [`05c40152088`](https://github.com/apache/cloudberry/commit/05c401520888819509880bdb1770bcd55c58b65a) - [ORCA] Remove the IntoClause related logic in the CTAS (#978) (jiaqizho) [#978](https://github.com/apache/cloudberry/pull/978)
* [`b72886a60eb`](https://github.com/apache/cloudberry/commit/b72886a60eb5b0248f5b42885a2c88ba19c71767) - Add quick path to exit GpDestroyParallelDSMEntry. (HuSen8891) [#1020](https://github.com/apache/cloudberry/pull/1020)
* [`8c62a2615b7`](https://github.com/apache/cloudberry/commit/8c62a2615b7f4869e55681225ce3dfbdd7c36aac) - [ORCA] Simply some logic of code (zhoujiaqi) [#1013](https://github.com/apache/cloudberry/pull/1013)
* [`bc519a43aa3`](https://github.com/apache/cloudberry/commit/bc519a43aa318d2f53754112957aa2d64fc50cb4) - CI: upload Cloudberry debuginfo RPM build artifacts (Jianghua Yang) [#1016](https://github.com/apache/cloudberry/pull/1016)
* [`549f0abc4ef`](https://github.com/apache/cloudberry/commit/549f0abc4ef98feb8a1431b7478c6b0ca8b249f9) - Temp ignore refresh_compare test (Jianghua Yang) [#1015](https://github.com/apache/cloudberry/pull/1015)
* [`8742e27f122`](https://github.com/apache/cloudberry/commit/8742e27f122444c9d349a29ff49a2a1c45a06d32) - Improve appendonly_getnextslot to optimize tuple retrieval (#1025) (Jianghua.yjh) [#1025](https://github.com/apache/cloudberry/pull/1025)
* [`7847a1534eb`](https://github.com/apache/cloudberry/commit/7847a1534eb266ff62c5493bcc672f017152f2f3) - Add optional support for CLOCK_MONOTONIC_COARSE when explain analyze. (Jianghua Yang) [#1011](https://github.com/apache/cloudberry/pull/1011)
* [`8c287295fdd`](https://github.com/apache/cloudberry/commit/8c287295fdd61af00f188ef61c3e33451a778670) - InstrStopNode align with gp7 (Jianghua Yang) [#1011](https://github.com/apache/cloudberry/pull/1011)
* [`398c52defb4`](https://github.com/apache/cloudberry/commit/****************************************) - Add CommandId to XLOG if serverless mode is enabled (HuSen8891) [#1021](https://github.com/apache/cloudberry/pull/1021)
* [`676521e598b`](https://github.com/apache/cloudberry/commit/676521e598bb2605530b8229828c1a5d9a29b0a3) - Fix: Cleanup tables which has self define table access method. (Jianghua Yang) [#1026](https://github.com/apache/cloudberry/pull/1026)
* [`2824db7373e`](https://github.com/apache/cloudberry/commit/2824db7373e669668e19abfba4f35c732780acd4) - revert 'extend relfilenode from 32 bit(Oid) to 64 bit(RelFileNodeId)' (leo) [#1023](https://github.com/apache/cloudberry/pull/1023)
* [`ca06b2896dc`](https://github.com/apache/cloudberry/commit/ca06b2896dc8bfc52e9b5aaffaea3de42ebef95e) - Push the runtime filter from HashJoin down to SeqScan. (zhangyue) [#724](https://github.com/apache/cloudberry/pull/724)
* [`e1c99e4d77b`](https://github.com/apache/cloudberry/commit/e1c99e4d77be44d5400530fd32ce1ace992b2fb1) - Revert "Include distributed xid in transaction commit WAL in all cases" (leo) [#1018](https://github.com/apache/cloudberry/pull/1018)
* [`e0fc2fe8172`](https://github.com/apache/cloudberry/commit/e0fc2fe817212bfd403bfe8066e9aa116ff0da95) - Fix always rebuild gang when cdbdisp_dispatchCommandInternal. (zhangwenchao) [#995](https://github.com/apache/cloudberry/pull/995)
* [`f668d857bc7`](https://github.com/apache/cloudberry/commit/f668d857bc7f895f437c1f4b6cd392c41d61ce38) - Revert "Prepare GUC option string only once during gang creation" (leo) [#1017](https://github.com/apache/cloudberry/pull/1017)
* [`8910a5b1fcd`](https://github.com/apache/cloudberry/commit/8910a5b1fcdf7bd4f826adaa8c4266933d1b347c) - hardcode the attributes of GpSegmentId in GetContentIdsFromPlanForSingleRelation() (#15659) (Hongxu Ma) [#1019](https://github.com/apache/cloudberry/pull/1019)
* [`e6773293d37`](https://github.com/apache/cloudberry/commit/e6773293d37acf1c860c08419daec3c6bdfc4601) - Remove the HTBL in motion (zhoujiaqi) [#1012](https://github.com/apache/cloudberry/pull/1012)
* [`a73818b5fd7`](https://github.com/apache/cloudberry/commit/a73818b5fd7d531894da7c36556a19ec618037bb) - Add .DS_Store to .gitignore (Dianjin Wang) [#1006](https://github.com/apache/cloudberry/pull/1006)
* [`7da6ee14c73`](https://github.com/apache/cloudberry/commit/7da6ee14c731bdd7088d3bc6e01ff2121e497ad0) - Optimize AOCS scan performance by introducing specialized no-qual path (Jianghua Yang) [#1010](https://github.com/apache/cloudberry/pull/1010)
* [`d1af36d122c`](https://github.com/apache/cloudberry/commit/d1af36d122ccd2892f5f794bfb0d995b8a2f693a) - Remove upgrade_tuple and related code. (Brent Doil) [#1010](https://github.com/apache/cloudberry/pull/1010)
* [`94af1a2974c`](https://github.com/apache/cloudberry/commit/94af1a2974c2dcd6c9041e17641ce1ddf204d9f2) - Doc: update the NOTICE and LICENSE files (Dianjin Wang) [#812](https://github.com/apache/cloudberry/pull/812)
* [`a56d4f936fc`](https://github.com/apache/cloudberry/commit/a56d4f936fcee89b43ea1dffe3e87ce369682e05) - Reuse the epoll object to fix the dispatcher performance downgrade (#14800) (Hongxu Ma) [#997](https://github.com/apache/cloudberry/pull/997)
* [`61cdc19d0f3`](https://github.com/apache/cloudberry/commit/61cdc19d0f38e672aac8dc3fa1d9d8dd7ff1975d) - Fix dispatch test in parallel mode (Jianghua Yang) [#997](https://github.com/apache/cloudberry/pull/997)
* [`2946ac18f16`](https://github.com/apache/cloudberry/commit/2946ac18f16f268ead86aaf8053eab30b6171718) - CI: Refactor Coverity scan workflow (Dianjin Wang) [#1000](https://github.com/apache/cloudberry/pull/1000)
* [`7961dd7b3c9`](https://github.com/apache/cloudberry/commit/7961dd7b3c9fbbbbdccbb8aab3aa6e990c5a6cc4) - Add Coverity Scan Badge (Dianjin Wang) [#993](https://github.com/apache/cloudberry/pull/993)
* [`f18593370b2`](https://github.com/apache/cloudberry/commit/f18593370b2b991823080916b46a4b7cb173769f) - CI: Disable parallel builds for Coverity scan (Ilya Shipitsin) [#998](https://github.com/apache/cloudberry/pull/998)
* [`8f1926799de`](https://github.com/apache/cloudberry/commit/8f1926799de3e33c074d86b3916f2cc21eba5d59) - CI: enable daily coverity scan (Ilya Shipitsin) [#849](https://github.com/apache/cloudberry/pull/849)
* [`f020573d359`](https://github.com/apache/cloudberry/commit/f020573d3598b440526f2fdbeac46b974ba8bd17) - Fix wrong join_rel size estimates for anti join. (#934) (Tender Wang) [#934](https://github.com/apache/cloudberry/pull/934)
* [`0c3c0051e22`](https://github.com/apache/cloudberry/commit/0c3c0051e228429e6a6a82a5c49f498936be27a3) - remove deprecated warning log, ao/aocs has support DynamicIndexOnlyScan (GongXun) [#994](https://github.com/apache/cloudberry/pull/994)
* [`58049301468`](https://github.com/apache/cloudberry/commit/580493014687545e3d50d779461e96289bf4c510) - Fix gpcheckcat that checks foreign key reference for appendonly tables (Hao Wu) [#988](https://github.com/apache/cloudberry/pull/988)
* [`c67e370d62b`](https://github.com/apache/cloudberry/commit/c67e370d62bf2c6f5cdeb26ee0c42457c0d56e88) - Fix FIPS mode checks and initialize segfile_count (Jianghua Yang) [#986](https://github.com/apache/cloudberry/pull/986)
* [`644b4e30738`](https://github.com/apache/cloudberry/commit/644b4e307380954e986f048d0a7910a3063e524e) - Fix volatile qualifier discard and ensure safe access to PGPROC in lock.c (Jianghua Yang) [#963](https://github.com/apache/cloudberry/pull/963)
* [`878e6fd1127`](https://github.com/apache/cloudberry/commit/878e6fd1127dd35640a0b91a0dca9d29744308ca) - [AQUMV] Extend AQUMV to support materialized views on partitioned tables. (Zhang Mingli) [#965](https://github.com/apache/cloudberry/pull/965)
* [`a96fe0553c0`](https://github.com/apache/cloudberry/commit/a96fe0553c0f250feb16e6e1db787dcc41baa4f5) - Resolve cherry-picks (reshke) [#985](https://github.com/apache/cloudberry/pull/985)
* [`8e490bc213d`](https://github.com/apache/cloudberry/commit/8e490bc213d0230d6bd32234b2b09789c7da27db) - Update check_for_appendonly_materialized_view_with_relfrozenxid (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`409d9269567`](https://github.com/apache/cloudberry/commit/409d926956770437f6183760c8529170fa4b1a87) - Report incompatible roles in pg_upgrade checking (Daniel Gustafsson) [#985](https://github.com/apache/cloudberry/pull/985)
* [`84e8a03e14d`](https://github.com/apache/cloudberry/commit/84e8a03e14dac1b7a2c2b8d3bc0f1d51c86cd25f) - pg_upgrade: modify `--output-dir` to not create certain sub-directories (Marbin Tan) [#985](https://github.com/apache/cloudberry/pull/985)
* [`af87dadf522`](https://github.com/apache/cloudberry/commit/af87dadf52200241691733ffca9e87126a193f1a) - Remove a fixme of pg_upgrade makefile (Adam Lee) [#985](https://github.com/apache/cloudberry/pull/985)
* [`106062292d8`](https://github.com/apache/cloudberry/commit/106062292d8fee49cb68c8460d89aeb5e08662b8) - pg_upgrade: Add check for disallowed OPERATOR (Marbin Tan) [#985](https://github.com/apache/cloudberry/pull/985)
* [`37ea5553526`](https://github.com/apache/cloudberry/commit/37ea55535269957f1871da0f564e1a1896a86303) - pg_upgrade: check for views using removed types (Kevin Yeap) [#985](https://github.com/apache/cloudberry/pull/985)
* [`45804f88431`](https://github.com/apache/cloudberry/commit/45804f88431f568c5b023efc150c1a3b8d60a862) - pg_upgrade: version guard checks that use 6X support functions (Kevin Yeap) [#985](https://github.com/apache/cloudberry/pull/985)
* [`bfd04d5bf0a`](https://github.com/apache/cloudberry/commit/bfd04d5bf0a05aefdd4e1dc3265da6138478f989) - pg_upgrade: check for views using removed functions (Kevin Yeap) [#985](https://github.com/apache/cloudberry/pull/985)
* [`f8e419e9573`](https://github.com/apache/cloudberry/commit/f8e419e9573177c30eacdcc1f86b96381e917955) - pg_upgrade: check for views using removed operators (Kevin Yeap) [#985](https://github.com/apache/cloudberry/pull/985)
* [`9bdea2a41c9`](https://github.com/apache/cloudberry/commit/9bdea2a41c9762a9947f29064b9e602589548f61) - Fix check_multi_column_list_partition_keys (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`829077d9b1c`](https://github.com/apache/cloudberry/commit/829077d9b1c1aea7208265303b9d03cc24a68ccb) - pg_upgrade: Add flag for relocateable output (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`b779ad268c6`](https://github.com/apache/cloudberry/commit/b779ad268c650c495eae4c69b2c1fa70cd715ecd) - pg_upgrade: further tweaking of make_outputdirs(). (Tom Lane) [#985](https://github.com/apache/cloudberry/pull/985)
* [`9317bee8286`](https://github.com/apache/cloudberry/commit/9317bee82860de88eead956f86938a18dcebb524) - Ensure check output files land in the same location (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`90c12321531`](https://github.com/apache/cloudberry/commit/90c12321531b958bf9bdc92d95d4e13b3290bd51) - Restructure pg_upgrade output directories for better idempotence (Michael Paquier) [#985](https://github.com/apache/cloudberry/pull/985)
* [`62075a59432`](https://github.com/apache/cloudberry/commit/62075a59432001ab3e92fd9e29900b65c454f09b) - pg_upgrade Exclude `$libdir/plpython2` during library check (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`45ea69acc1a`](https://github.com/apache/cloudberry/commit/45ea69acc1ab905b8c52977ff0e14d79c45e9a19) - Remove dead pg_upgrade code (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`baa15aa81b2`](https://github.com/apache/cloudberry/commit/baa15aa81b2dfc1f3f273ee0964bf6f6b0af5ce0) - pg_upgrade: Add check for functions dependent on plpython2 (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`147829456e3`](https://github.com/apache/cloudberry/commit/147829456e3062c63a06b9c07ab2e9a22b7b664a) - Check for multi-column LIST partition keys (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`fc26347b8af`](https://github.com/apache/cloudberry/commit/fc26347b8af07cc7c7fde598ac43cab78df51f1a) - Drop __gpupgrade_tmp if exists. (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`a1a76e576cd`](https://github.com/apache/cloudberry/commit/a1a76e576cd64b86f41e55f31713735eff513aec) - pg_upgrade: Remove gphdfs checks. (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`1f586b78f88`](https://github.com/apache/cloudberry/commit/1f586b78f88c5d96dfe2d4fa3f176f0807228a6e) - pg_upgrade: Use gp_fatal_log for check output (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`d9eed043621`](https://github.com/apache/cloudberry/commit/d9eed04362109523d46db5adabca28bc01e04967) - pg_upgrade: Remove unnecessary line type check (Brent Doil) [#985](https://github.com/apache/cloudberry/pull/985)
* [`b64d461e37c`](https://github.com/apache/cloudberry/commit/b64d461e37cc7e8f1dd7beffa95b490b54d2c5ab) - Epic: Rebrand Names and URLs for ASF Compliance (#731) (Dianjin Wang) [#731](https://github.com/apache/cloudberry/pull/731)
* [`a9cc2517224`](https://github.com/apache/cloudberry/commit/a9cc25172248382669a670662799176798d9f259) - Remove concourse and hd-ci related files (Dianjin Wang) [#920](https://github.com/apache/cloudberry/pull/920)
* [`fa73033d255`](https://github.com/apache/cloudberry/commit/fa73033d2553968681e5ad41187a5a082e6a0f4e) - Update Makefile: fix typo. s/appendonly/aocs/ (reshke) [#981](https://github.com/apache/cloudberry/pull/981)
* [`755d3ba3ecc`](https://github.com/apache/cloudberry/commit/755d3ba3ecc7399da461b041276ab7a9f6460236) - Adapt appendPsqlMetaConnect() to the new fmtId() encoding expectations. (Tom Lane) [#980](https://github.com/apache/cloudberry/pull/980)
* [`762425d2e3a`](https://github.com/apache/cloudberry/commit/762425d2e3a2b8543e7c36701d529dfae03e6503) - Fix type in test_escape test (Andres Freund) [#980](https://github.com/apache/cloudberry/pull/980)
* [`98a58aabd96`](https://github.com/apache/cloudberry/commit/98a58aabd962710507be74940f331c3a5880d24c) - Add test of various escape functions (Andres Freund) [#980](https://github.com/apache/cloudberry/pull/980)
* [`d04a61da060`](https://github.com/apache/cloudberry/commit/d04a61da06025cbc4fd0cc396523cf6b17b3449c) - Fix handling of invalidly encoded data in escaping functions (Andres Freund) [#980](https://github.com/apache/cloudberry/pull/980)
* [`884e3088295`](https://github.com/apache/cloudberry/commit/884e3088295c2032962454271e4ae5c3fbb1b748) - Specify the encoding of input to fmtId() (Andres Freund) [#980](https://github.com/apache/cloudberry/pull/980)
* [`57e45eaa25a`](https://github.com/apache/cloudberry/commit/57e45eaa25a354527e778b6d244ee653553cdd7c) - Add pg_encoding_set_invalid() (Andres Freund) [#980](https://github.com/apache/cloudberry/pull/980)
* [`0af03caefc6`](https://github.com/apache/cloudberry/commit/0af03caefc603c157ceec7e2ead115fa70cc8f5b) - [ORCA] Support PAX AM in ORCA (zhoujiaqi) [#979](https://github.com/apache/cloudberry/pull/979)
* [`32dfc53bc24`](https://github.com/apache/cloudberry/commit/32dfc53bc247114019ba3e0f595a833970cc5388) - Fix crash that syslog receives messages from threads (Hao Wu) [#977](https://github.com/apache/cloudberry/pull/977)
* [`6a428a1b153`](https://github.com/apache/cloudberry/commit/6a428a1b153c4baf4c13c9f1070bd49639217d3a) - Address cherry-pick issues (reshke) [#897](https://github.com/apache/cloudberry/pull/897)
* [`eb98b4d982c`](https://github.com/apache/cloudberry/commit/eb98b4d982cab784f4c765b6538cb1a5136d7d5b) - aoco: Scan progress reporting for CREATE INDEX (Soumyadeep Chakraborty) [#897](https://github.com/apache/cloudberry/pull/897)
* [`9fbaaae416b`](https://github.com/apache/cloudberry/commit/9fbaaae416bddb5c78721744f17bac8a2725a059) - Cherry-pick fix: remove gp_appendonly_enable_unique_index GUC (reshke) [#897](https://github.com/apache/cloudberry/pull/897)
* [`972000b8234`](https://github.com/apache/cloudberry/commit/972000b8234f5e6f3c4fbce61893dd457ea58a7b) - ao: Scan progress reporting for CREATE INDEX (Soumyadeep Chakraborty) [#897](https://github.com/apache/cloudberry/pull/897)
* [`3916639e62e`](https://github.com/apache/cloudberry/commit/3916639e62e3102e13da008fe9a316c6dd8f21fa) - Allow SET AM from heap to AO with unique indexes (Soumyadeep Chakraborty) [#897](https://github.com/apache/cloudberry/pull/897)
* [`84070273ad6`](https://github.com/apache/cloudberry/commit/84070273ad655afd932c6ec48b157c0ab1afe5c7) - ao/co: Add smoke test for partial unique indexes (Soumyadeep Chakraborty) [#897](https://github.com/apache/cloudberry/pull/897)
* [`ba84b10941e`](https://github.com/apache/cloudberry/commit/ba84b10941ebbd38e0da9e3113b9f93d49838f11) - ao/co: Smoke test - unique index in repeatable read (Soumyadeep Chakraborty) [#897](https://github.com/apache/cloudberry/pull/897)
* [`2c58f50b811`](https://github.com/apache/cloudberry/commit/2c58f50b811e1a76f6d86b091d64760c77a7973a) - [ORCA] Fix ORCA unit-tests (zhoujiaqi) [#976](https://github.com/apache/cloudberry/pull/976)
* [`ec131880484`](https://github.com/apache/cloudberry/commit/ec131880484bda6f6e1ffa0a4dc3fa6d9c87cca3) - pgcrypto: allow enable FIPS in FIPS not enabled OS (Sasasu) [#975](https://github.com/apache/cloudberry/pull/975)
* [`3ab1d02cff6`](https://github.com/apache/cloudberry/commit/3ab1d02cff61737fc2b45d0047335d924a4addab) - Add testcase for FIPS mode operations in pgcrypto (Sasasu) [#975](https://github.com/apache/cloudberry/pull/975)
* [`74d4c351b1b`](https://github.com/apache/cloudberry/commit/74d4c351b1b7dc1da7d7d9be9bf058811f8d6d2b) - Support FIPS mode operation in pgcrypto (Sasasu) [#975](https://github.com/apache/cloudberry/pull/975)
* [`829f3ab5f74`](https://github.com/apache/cloudberry/commit/829f3ab5f74c52a2e6bdc24e1fc16c04cbccc718) - Pass relation Oid to smgr_AORelOpenSegFile. (#956) (reshke) [#956](https://github.com/apache/cloudberry/pull/956)
* [`c3540a8d690`](https://github.com/apache/cloudberry/commit/c3540a8d690e02bd1bd82a5cf4d95c6f146b07e8) - Fix pgcrypto to support OpenSSL >= 3.0.0 (Jianghua Yang) [#973](https://github.com/apache/cloudberry/pull/973)
* [`517794184fa`](https://github.com/apache/cloudberry/commit/517794184fa282b0a80abbc4ba9f09ae9668c9b2) - Fix flaky test in insert_root_partition_truncate_deadlock (wenru yan) [#971](https://github.com/apache/cloudberry/pull/971)
* [`454814d0e8f`](https://github.com/apache/cloudberry/commit/454814d0e8f3c4cff552083ee0edeaa20e336794) - Lock leaf partitions for Insert Statement when GDD disabled. (Zhenghua Lyu) [#971](https://github.com/apache/cloudberry/pull/971)
* [`fe5baeb18ae`](https://github.com/apache/cloudberry/commit/fe5baeb18ae80441b91f792868a8168f21a3a35f) - Fix comment for a pg_upgrade check (Brent Doil) [#964](https://github.com/apache/cloudberry/pull/964)
* [`b59b0d567e3`](https://github.com/apache/cloudberry/commit/b59b0d567e35ed41b1f8614e4e7ad16406f8eae3) - Unify todos and fixmes in pg_upgrade code. (Brent Doil) [#964](https://github.com/apache/cloudberry/pull/964)
* [`2e454cf1536`](https://github.com/apache/cloudberry/commit/2e454cf15361f29f2a146fb380f993849ddaf6ce) - Create common infrastructure for cross-version upgrade testing. (Tom Lane) [#964](https://github.com/apache/cloudberry/pull/964)
* [`6e1ddd0e1f5`](https://github.com/apache/cloudberry/commit/6e1ddd0e1f5987ffa03fcdb4f63aa200a5fbf113) - pg_upgrade:  adjust error paragraph width to be consistent (Bruce Momjian) [#964](https://github.com/apache/cloudberry/pull/964)
* [`0d2d752b09d`](https://github.com/apache/cloudberry/commit/0d2d752b09d6c65b728d575cea4a108fc8279f74) - pg_upgrade: improve instructions for fixing incompatible isn use (Bruce Momjian) [#964](https://github.com/apache/cloudberry/pull/964)
* [`c9eb7933fcf`](https://github.com/apache/cloudberry/commit/c9eb7933fcf693bd1108ffc6798f9e140ed037d1) - pg_upgrade: Clean up some redundant code (Peter Eisentraut) [#964](https://github.com/apache/cloudberry/pull/964)
* [`169199de8e5`](https://github.com/apache/cloudberry/commit/169199de8e54c1f8b08c0b8467a2d66eb308f8ec) - pg_upgrade:  clarify the database names in error files (Bruce Momjian) [#964](https://github.com/apache/cloudberry/pull/964)
* [`5082a5c10f0`](https://github.com/apache/cloudberry/commit/5082a5c10f07ea17918c34ea29143a0c95948656) - Fix handling of empty ranges and NULLs in BRIN (Tomas Vondra) [#970](https://github.com/apache/cloudberry/pull/970)
* [`90ce2e646f1`](https://github.com/apache/cloudberry/commit/90ce2e646f10d6c2938a7f205b98c4f83b9b661a) - Fix handling of NULLs when merging BRIN summaries (Tomas Vondra) [#970](https://github.com/apache/cloudberry/pull/970)
* [`2964b431304`](https://github.com/apache/cloudberry/commit/2964b431304b684f56299773c5430bd974190c7f) - brin_revmap: Fix maybe-uninitialized warning (Soumyadeep Chakraborty) [#970](https://github.com/apache/cloudberry/pull/970)
* [`2fcd0f6d951`](https://github.com/apache/cloudberry/commit/2fcd0f6d951c0d9769c310c8ba2096419de81cb0) - brin ao/co: Fix desummarization (utility mode) (Soumyadeep Chakraborty) [#970](https://github.com/apache/cloudberry/pull/970)
* [`59ceab73671`](https://github.com/apache/cloudberry/commit/59ceab736715eff9ca59c707433931ec7e6151dd) - brin ao/co: Enable specific range summarization (Soumyadeep Chakraborty) [#970](https://github.com/apache/cloudberry/pull/970)
* [`1de910935c2`](https://github.com/apache/cloudberry/commit/1de910935c2ed53009c68c89a0cc2e7c26918d05) - Add help information for gpfdist (Jianghua Yang) [#972](https://github.com/apache/cloudberry/pull/972)
* [`bf7f7f1ddbf`](https://github.com/apache/cloudberry/commit/bf7f7f1ddbf1161a26f5974e66623d81e305f6aa) - Fix icw tests from "table aliases in ORCA, Support Query Parameters, join order hints" (zhoujiaqi) [#959](https://github.com/apache/cloudberry/pull/959)
* [`568e0c340ff`](https://github.com/apache/cloudberry/commit/568e0c340ff0916b10fc781fc7a08353ba889a1b) - [ORCA] Support left/right outer join order hints (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`ed0d9001917`](https://github.com/apache/cloudberry/commit/ed0d9001917ef9588f3d6cec0c418eec0600e5a4) - [ORCA] Define gpos::set using std with custom allocator (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`406a2b3fe9a`](https://github.com/apache/cloudberry/commit/406a2b3fe9ae205b25192d27094535fd0b9867bc) - [Orca] Fallback to planner if a function in 'from' clause uses 'WITH ORDINALITY' (#17477) (bhari) [#959](https://github.com/apache/cloudberry/pull/959)
* [`7257984e405`](https://github.com/apache/cloudberry/commit/7257984e4059b916245cf61284dd05f64cb42467) - Fix orca preprocess step for query with Select-Project-NaryJoin pattern (#17423) (bhari) [#959](https://github.com/apache/cloudberry/pull/959)
* [`bbdf6cb1061`](https://github.com/apache/cloudberry/commit/bbdf6cb1061cb5379bc16261510fa19d21623717) - Add support for Planhints logging (#17398) (Sanath Kumar Vobilisetty) [#959](https://github.com/apache/cloudberry/pull/959)
* [`446b1c1ac7b`](https://github.com/apache/cloudberry/commit/446b1c1ac7b6032bd35d1ecaaba1bf3786ad9acb) - Update pg_hint_plan to recoginze join type JOIN_LASJ_NOTIN (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`d8b8188dfb1`](https://github.com/apache/cloudberry/commit/d8b8188dfb1e80997dfb8e380a1ff00453a787cb) - [ORCA] Support join type hints (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`f0d5d2833c1`](https://github.com/apache/cloudberry/commit/f0d5d2833c1f6f7f1dac49e28e2ea9327ba139fd) - Inline CTEs in Orca that contain outer references (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`7ef960a39e6`](https://github.com/apache/cloudberry/commit/7ef960a39e6bbfd432dcb859d161ad2ed6086f97) - Use full precision for statistics values in Orca for mdps (#17386) (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`eeaada0f901`](https://github.com/apache/cloudberry/commit/eeaada0f901c744863995e383c84a183241eccce) - Consider skew of null values when costing redistributes in Orca (#17311) (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`5c0c40eb3ab`](https://github.com/apache/cloudberry/commit/5c0c40eb3ab218cbe0a48b38fbd9190071ab205c) - Support full hash join (Jingyu Wang) [#959](https://github.com/apache/cloudberry/pull/959)
* [`60af2016612`](https://github.com/apache/cloudberry/commit/60af201661238ea302807d7bd4063685c5061562) - Fix memory leak in merge join implementation (Jingyu Wang) [#959](https://github.com/apache/cloudberry/pull/959)
* [`bf184e2f304`](https://github.com/apache/cloudberry/commit/bf184e2f304d07dcf5bb138a9c9549b8681b0bf4) - Require plan hint field in optimizer config (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`5386b9cc7ce`](https://github.com/apache/cloudberry/commit/5386b9cc7ce0288567b33d3904c6556aa767d2f5) - Enable optimizer config test case (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`e24c8c6ae96`](https://github.com/apache/cloudberry/commit/e24c8c6ae96a036729c0702473a5b984ee6cb0f3) - Orca: reinstate the retrieval of system column statistics (#17250) (Pan Wang) [#959](https://github.com/apache/cloudberry/pull/959)
* [`3aeaad282e2`](https://github.com/apache/cloudberry/commit/3aeaad282e2b374922bdd9ff61631ed2a6367adf) - [ORCA] Implement plan hints for join order (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`dbfe77e8e21`](https://github.com/apache/cloudberry/commit/dbfe77e8e21dbc726d39d6764565401270922f15) - [ORCA] Add memory pool custom allocator (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`a9066c0a650`](https://github.com/apache/cloudberry/commit/a9066c0a650f23e58beabd901b849dc665efeea2) - Fix issue with plan hints being unable to derive table descriptors. (#17264) (Sanath Kumar Vobilisetty) [#959](https://github.com/apache/cloudberry/pull/959)
* [`635efd67931`](https://github.com/apache/cloudberry/commit/635efd67931b64ae8eccca8b560b1282cdd0570e) - Modify Orca unit test asserts to run in retail build as well and fix unused variable warnings (#17307) (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`bec00be1849`](https://github.com/apache/cloudberry/commit/bec00be184987447fc816bc5956a931c8bbe4a56) - Add support for table aliases in ORCA (Denis) [#959](https://github.com/apache/cloudberry/pull/959)
* [`da091d14fab`](https://github.com/apache/cloudberry/commit/da091d14fab0d0924a5f1e2138e23cc19158a27f) - Support Query Parameters in Orca (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`b8dacf25d11`](https://github.com/apache/cloudberry/commit/b8dacf25d118fa21bfa9c25f2f8f4ba7b72f71e5) - [ORCA] Remove default parameter argument (David Kimura) [#959](https://github.com/apache/cloudberry/pull/959)
* [`b8bacf16cba`](https://github.com/apache/cloudberry/commit/b8bacf16cba0d996422af584e262881fa6d98b6f) - Add ORCA GUC's for dynamic index/bitmap scan (Jingyu Wang) [#959](https://github.com/apache/cloudberry/pull/959)
* [`7e31f8404ce`](https://github.com/apache/cloudberry/commit/7e31f8404ce2a0059f5518be3498888a6dfc774f) - Optimize DELETEs in Orca to only project necessary columns (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`fc0f9e47f76`](https://github.com/apache/cloudberry/commit/fc0f9e47f76c84b2a1e8f705d373b1e1f4135ef4) - Handle Invalid Return Value For Opfamily Mdid (#17165) (NISHANT SHARMA) [#959](https://github.com/apache/cloudberry/pull/959)
* [`883811c45ac`](https://github.com/apache/cloudberry/commit/883811c45acc437876e4ea25daeeea03628efd11) - Remove unused Partition Constraint code from Orca (#17136) (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`a99e12037c6`](https://github.com/apache/cloudberry/commit/a99e12037c6fc3a9ad9c5b85be98810a63cb6e26) - Prevent overwriting of workers in ORCA (#17097) (NISHANT SHARMA) [#959](https://github.com/apache/cloudberry/pull/959)
* [`d19c6039f59`](https://github.com/apache/cloudberry/commit/d19c6039f59ebd0070eb0e34e2f21b28c4ca5ea4) - Re-support DISTINCT-qualified Window Aggregate in Orca (#17113) (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`d8d5ea7f389`](https://github.com/apache/cloudberry/commit/d8d5ea7f38955c76d3fa188df744de697d52e724) - Fix ORCA returning wrong column type modifier info (#16810) (bhari) [#959](https://github.com/apache/cloudberry/pull/959)
* [`9d66a1de4be`](https://github.com/apache/cloudberry/commit/9d66a1de4be83ac242f3f4e7348f020b612ae726) - Mark additional Orca gucs to be shown in guc list (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`4d25a7b666b`](https://github.com/apache/cloudberry/commit/4d25a7b666b258e7e44f742b6bebca236f09896c) - Run preprocessing methods in ExplainDXL (#15334) (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`4becde5c721`](https://github.com/apache/cloudberry/commit/4becde5c721b172b4c57bdc653a1a28c578f114e) - Orca FIXME: enable previously disabled test (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`b4103ef9ad2`](https://github.com/apache/cloudberry/commit/b4103ef9ad2bbd36a31837c000b011458e461e38) - Address updatable cursor fixme in Orca (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`1dcc67bbf7e`](https://github.com/apache/cloudberry/commit/1dcc67bbf7e617b0ea8e938854e222376e8fc690) - Enable postgres_fdw test for Orca (Chris Hajas) [#959](https://github.com/apache/cloudberry/pull/959)
* [`5dc8fc2c966`](https://github.com/apache/cloudberry/commit/5dc8fc2c9661bf76b66cb522b99067053643dc07) - Change optimizer_array_expansion_threshold default value to 20 (#14032) (gpopt) [#959](https://github.com/apache/cloudberry/pull/959)
* [`0b2eb333a8c`](https://github.com/apache/cloudberry/commit/0b2eb333a8cb1b02824fee8e06ea93cdf24bdea0) - Revert "Fix crashes of lateral join (#16958)" (Jianghua Yang) [#966](https://github.com/apache/cloudberry/pull/966)
* [`c924030517a`](https://github.com/apache/cloudberry/commit/c924030517ad51dc6c3ffea9f6eb6e9b5115fb82) - Fix cherry-pick issue (reshke) [#912](https://github.com/apache/cloudberry/pull/912)
* [`efa49da09b5`](https://github.com/apache/cloudberry/commit/efa49da09b56ea2c392c7bc591135480828c0645) - brin ao/co: Bool to track tuples in build state (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`184585f6d76`](https://github.com/apache/cloudberry/commit/184585f6d761f6d3d3daa6707306512602ae576e) - brin tests: Rename blocks to nblocks (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`6d67c856c2d`](https://github.com/apache/cloudberry/commit/6d67c856c2d6b28048dfc5dd05ca72d795b1c498) - brin: Rename isAo to isAO for consistency (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`e748a379359`](https://github.com/apache/cloudberry/commit/e748a379359bb1c018039db2c82482cf6e602bd3) - brin ao/co: Minor adjustments to pageinspect (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`c57ec8017a8`](https://github.com/apache/cloudberry/commit/c57ec8017a8da610c0388daf07f7d1892c16ef6c) - brin ao/co: Assert range in/ex-clusion for scans (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`992ca8bdce3`](https://github.com/apache/cloudberry/commit/992ca8bdce35281bc8e8afe03c7d61f92211018f) - brin ao/co: Add coverage for aborted rows (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`cfba63ccf23`](https://github.com/apache/cloudberry/commit/cfba63ccf23a26e9ca153c5aeae17e5c34ee3f21) - brin ao/co: Ensure final range summarization: build (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`9d89abaa10b`](https://github.com/apache/cloudberry/commit/9d89abaa10b8d066a6983a35ee1c58c0d60d7dc4) - brin ao/co fixes: final partial range summarization (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`edf48ec0777`](https://github.com/apache/cloudberry/commit/edf48ec07777b0f7efa3f6cc89f73893a7fd87b9) - brin ao/co: Fix range start calculations (Soumyadeep Chakraborty) [#912](https://github.com/apache/cloudberry/pull/912)
* [`5cdbab19afe`](https://github.com/apache/cloudberry/commit/5cdbab19afe37078e972c037fd765a62788e5945) - Resolve cherry-pick (reshke) [#957](https://github.com/apache/cloudberry/pull/957)
* [`4f6aa23c1aa`](https://github.com/apache/cloudberry/commit/4f6aa23c1aaad05f11d8107b1462958e1382647f) - Resolve GPDB_12_MERGE_FIXME create index on AO/CO allows read-only transactions (Haolin Wang) [#957](https://github.com/apache/cloudberry/pull/957)
* [`78861a03f9e`](https://github.com/apache/cloudberry/commit/78861a03f9e05cf28ce38fa1e02beef27ce28333) - gpexpand : Fix multi port issue when clusters are not in balanced state  (#13822) (Rakesh Sharma) [#957](https://github.com/apache/cloudberry/pull/957)
* [`81d4a8087a0`](https://github.com/apache/cloudberry/commit/81d4a8087a00a6d5ccc563cb335f3febe16c07e8) - Move fillfactor option back to intRelOpts_gp (Huansong Fu) [#957](https://github.com/apache/cloudberry/pull/957)
* [`7c37140e152`](https://github.com/apache/cloudberry/commit/7c37140e1528469365a429771d6121f9c0cb17cb) - BRIN: improve documentation on summarization (Alvaro Herrera) [#957](https://github.com/apache/cloudberry/pull/957)
* [`b23ca191387`](https://github.com/apache/cloudberry/commit/b23ca191387ff3a139f37507df3199a12b7226d5) - Fix column conflict issue in REFRESH MATERIALIZED VIEW CONCURRENTLY. (wenru yan) [#962](https://github.com/apache/cloudberry/pull/962)
* [`3a9305c4c0b`](https://github.com/apache/cloudberry/commit/3a9305c4c0bb0fc10a458db4359dff063d414885) - Disallow dbconnlimit checking on segments (Soumyadeep Chakraborty) [#962](https://github.com/apache/cloudberry/pull/962)
* [`29a36f869c9`](https://github.com/apache/cloudberry/commit/29a36f869c913cc68b09e8663fa03abd45961fee) - Fix duplicate index records of view pg_stat_all_indexes (eedy) [#962](https://github.com/apache/cloudberry/pull/962)
* [`85e41671b08`](https://github.com/apache/cloudberry/commit/85e41671b08f58f8a4888bbfa5a42b43d44a24a9) - [7X] Suppress compiler warning. (#15572) (Xing Guo) [#962](https://github.com/apache/cloudberry/pull/962)
* [`917766ed86d`](https://github.com/apache/cloudberry/commit/917766ed86d952907ecdea617445c59763c11918) - fast-analyze: rename cur_seg_row to segrowsprocessed (Haolin Wang) [#962](https://github.com/apache/cloudberry/pull/962)
* [`0b115e89273`](https://github.com/apache/cloudberry/commit/0b115e892738654f89181d45e180164c1222167d) - fast-analyze: rename bufferDone to needNextBuffer (Haolin Wang) [#962](https://github.com/apache/cloudberry/pull/962)
* [`a7482bd3836`](https://github.com/apache/cloudberry/commit/a7482bd3836da0bb7260f6f776e777d465a045a2) - fast-analyze: add row based sampler (Haolin Wang) [#962](https://github.com/apache/cloudberry/pull/962)
* [`25c6dbb5a4b`](https://github.com/apache/cloudberry/commit/25c6dbb5a4b270306864711f2beab330cc3ed26e) - Use ERROR for dispatcher liveness checks (Soumyadeep Chakraborty) [#962](https://github.com/apache/cloudberry/pull/962)
* [`00da8318420`](https://github.com/apache/cloudberry/commit/00da831842077cc170f12293a1aadc4c49ff00dc) - Fix gp_hyperloglog cstring type hadnling (#953) (reshke) [#953](https://github.com/apache/cloudberry/pull/953)
* [`c9511cc06c0`](https://github.com/apache/cloudberry/commit/c9511cc06c0ee2849eb97bcaec2850f95c2f6c45) - Fix compile on os with lower version of libcurl. (#16023) (zhaorui) [#960](https://github.com/apache/cloudberry/pull/960)
* [`0f96542cc39`](https://github.com/apache/cloudberry/commit/0f96542cc39025167522a0a250df282c5d467a0c) - Fix crashes of lateral join (#16958) (xuejing zhao) [#960](https://github.com/apache/cloudberry/pull/960)
* [`2528cbf0315`](https://github.com/apache/cloudberry/commit/2528cbf03154ed05d5fa6fa2b26a5779444a5d23) - Fix ValueError exception if there is an empty lock directory (Nihal Jain) [#960](https://github.com/apache/cloudberry/pull/960)
* [`68188447e37`](https://github.com/apache/cloudberry/commit/68188447e375ef06985aaba806fae8b55a437013) - Ignore database binswap_connect in binary swap test (Huansong Fu) [#960](https://github.com/apache/cloudberry/pull/960)
* [`3a7247dae94`](https://github.com/apache/cloudberry/commit/3a7247dae94e518a0d98cc17ca02df06e04d2df0) - Analyzing leaf partitions of multi-level partition table causes resampling of intermediate partition. (#16218) (Chandan Kunal) [#960](https://github.com/apache/cloudberry/pull/960)
* [`9b81b3b14dc`](https://github.com/apache/cloudberry/commit/9b81b3b14dc81c5f1be4b46ca47422d687046101) - autostats: Use SKIP_LOCKED for ANALYZE (Soumyadeep Chakraborty) [#960](https://github.com/apache/cloudberry/pull/960)
* [`f24f5169d57`](https://github.com/apache/cloudberry/commit/f24f5169d57755b95b0db704195a7d6d52ddb07d) - Suppress unused function warn when configure with --disable-orca (#951) (reshke) [#951](https://github.com/apache/cloudberry/pull/951)
* [`ab54a4dfa22`](https://github.com/apache/cloudberry/commit/ab54a4dfa2287fde1c2c6b10af9ceda0163bef6b) - Resolve cherry-pick issue with src/bin/pg_upgrade/check.c (reshke) [#955](https://github.com/apache/cloudberry/pull/955)
* [`e18aa83c048`](https://github.com/apache/cloudberry/commit/e18aa83c048b05d96277b2ce5995eee6e664bf4e) - Bring back matview with relfrozedxid check. (reshke) [#955](https://github.com/apache/cloudberry/pull/955)
* [`e223bdf3887`](https://github.com/apache/cloudberry/commit/e223bdf388745e88c81de1a5e2efcbd2bf8d7896) - Expand version string check in pg_upgrade (Brent Doil) [#955](https://github.com/apache/cloudberry/pull/955)
* [`e4cd698f44f`](https://github.com/apache/cloudberry/commit/e4cd698f44f34caa95dc8bc334371089441f9c84) - pg_upgrade: Add missing newline to message (Peter Eisentraut) [#955](https://github.com/apache/cloudberry/pull/955)
* [`35e876de698`](https://github.com/apache/cloudberry/commit/35e876de698a1d0944b4abaac2c8dff3cbca8992) - pg_upgrade: check for types removed in pg12 (Alvaro Herrera) [#955](https://github.com/apache/cloudberry/pull/955)
* [`78a82672436`](https://github.com/apache/cloudberry/commit/78a82672436d81c106246197ca997cf76f2e5eb9) - Fix some incorrectness in upgrade_adapt.sql on query for WITH OIDS (Michael Paquier) [#955](https://github.com/apache/cloudberry/pull/955)
* [`bba0455901f`](https://github.com/apache/cloudberry/commit/bba0455901f8306438adbb0e15c78b17000ed615) - pg_upgrade: Fix some minor code issues (Peter Eisentraut) [#955](https://github.com/apache/cloudberry/pull/955)
* [`9d90cf851fc`](https://github.com/apache/cloudberry/commit/9d90cf851fc2d8176167283934180a3cf0b3611f) - List offending databases in pg_upgrade datallowconn check (Daniel Gustafsson) [#955](https://github.com/apache/cloudberry/pull/955)
* [`c1efdb48274`](https://github.com/apache/cloudberry/commit/c1efdb48274eeb61faa7ea1d461c95885c039d97) - Fix double declaration for check_ok() in pg_upgrade.h (Peter Eisentraut) [#955](https://github.com/apache/cloudberry/pull/955)
* [`3fdbaae1179`](https://github.com/apache/cloudberry/commit/3fdbaae11791b3b26a048c9789fb169ee9049413) - pg_upgrade: Don't print progress status when output is not a tty. (Andres Freund) [#955](https://github.com/apache/cloudberry/pull/955)
* [`df3bf47ebdd`](https://github.com/apache/cloudberry/commit/df3bf47ebddf07ce5c6274c9018154f2f1f73a4c) - Fix thinko with subdirectories generated by pg_upgrade for internal files (Michael Paquier) [#955](https://github.com/apache/cloudberry/pull/955)
* [`cd5640ea2b3`](https://github.com/apache/cloudberry/commit/cd5640ea2b387ca8ac377d5392858d45bad9b9e8) - pg_upgrade: Move all the files generated internally to a subdirectory (Michael Paquier) [#955](https://github.com/apache/cloudberry/pull/955)
* [`cbfa6c96771`](https://github.com/apache/cloudberry/commit/cbfa6c9677199e758b52d9d783b1eafff6596c4b) - Remove pg_upgrade support for upgrading from pre-9.2 servers. (Tom Lane) [#955](https://github.com/apache/cloudberry/pull/955)
* [`dc59d3c4f7d`](https://github.com/apache/cloudberry/commit/dc59d3c4f7d661331cf4620bf8766dfccbad0474) - pg_upgrade: rewrite data type check from sql to plpgsql to run on GPDB6 (Kevin Yeap) [#955](https://github.com/apache/cloudberry/pull/955)
* [`6a4a5b20b13`](https://github.com/apache/cloudberry/commit/6a4a5b20b130e5cb83b54e2f2a1f246549c19689) - Update license headers for ASF rules (Dianjin Wang) [#935](https://github.com/apache/cloudberry/pull/935)
* [`603639e5c1b`](https://github.com/apache/cloudberry/commit/603639e5c1b932cf27b814e9308181f2e8971b80) - Doc: update Cloudberry info in configuration files (Dianjin Wang) [#924](https://github.com/apache/cloudberry/pull/924)
* [`6e82c1bdd94`](https://github.com/apache/cloudberry/commit/6e82c1bdd948370550e7421ba574f0163b31cd67) - Fix singlenode resource_queue test. (Jianghua Yang) [#949](https://github.com/apache/cloudberry/pull/949)
* [`ded07d02cdf`](https://github.com/apache/cloudberry/commit/ded07d02cdfc10e1f23f1a93c98eb1d5d25444f2) - Minor logging improvements to reslock release (Soumyadeep Chakraborty) [#949](https://github.com/apache/cloudberry/pull/949)
* [`027ea21cce2`](https://github.com/apache/cloudberry/commit/027ea21cce2e2f411293e3c4c02b6f6917ed622f) - Log queueid and portalid in resource queue logs (Soumyadeep Chakraborty) [#949](https://github.com/apache/cloudberry/pull/949)
* [`52a7b4407df`](https://github.com/apache/cloudberry/commit/52a7b4407dfbcf0dee7bd50fc0ee2953d3d208d1) - Add more verbose logging to ResCheckSelfDeadlock() (Soumyadeep Chakraborty) [#949](https://github.com/apache/cloudberry/pull/949)
* [`e83f7869399`](https://github.com/apache/cloudberry/commit/e83f7869399213a6df2fb00eff27bd0b1645a591) - Log queue/portal if ResLockRelease returns false (Soumyadeep Chakraborty) [#949](https://github.com/apache/cloudberry/pull/949)
* [`723dfb54d84`](https://github.com/apache/cloudberry/commit/723dfb54d8490f7a59d02d9d8efa684c4da5ba8a) - Fix race between termination and resqueue wakeup (Soumyadeep Chakraborty) [#949](https://github.com/apache/cloudberry/pull/949)
* [`d2f1ba727d4`](https://github.com/apache/cloudberry/commit/d2f1ba727d4cd8da41fed5ad0718bf3f331a4e18) - Fix a statement leak involving self-deadlocks (Soumyadeep Chakraborty) [#949](https://github.com/apache/cloudberry/pull/949)
* [`cd451b9400d`](https://github.com/apache/cloudberry/commit/cd451b9400d51316643b3cd226be7b6410eaffa6) - Remove unused TEXT files from top directory (Dianjin Wang) [#939](https://github.com/apache/cloudberry/pull/939)
* [`7568f376876`](https://github.com/apache/cloudberry/commit/7568f3768760ab0c3fdfab01f1a3f2b2c1c6751c) - Fix build && icw tests (zhoujiaqi) [#944](https://github.com/apache/cloudberry/pull/944)
* [`55a8f905b1b`](https://github.com/apache/cloudberry/commit/55a8f905b1b73777bb5655b6282faccfd2a38226) - Enable plan hints regress test (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`58dcaf000c4`](https://github.com/apache/cloudberry/commit/58dcaf000c47644df266e812c17c17a68cce213a) - [ORCA] Fix ident to const predicate push down optimization (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`78fa230700d`](https://github.com/apache/cloudberry/commit/78fa230700d7cbb74672bbf7abab92fb764e42f4) - Properly set function column count for table value functions in Orca (Chris Hajas) [#944](https://github.com/apache/cloudberry/pull/944)
* [`49b1c562079`](https://github.com/apache/cloudberry/commit/49b1c5620796f7ea18cc95df6921ec4b8a7adb76) - [ORCA] Fix SIGSEGV using subquery exists on materialized view (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`22bfff2b63d`](https://github.com/apache/cloudberry/commit/22bfff2b63d89b5e2b881159e1bc9927411d47ff) - [ORCA] Implement plan hints for row hints (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`b05263bde4a`](https://github.com/apache/cloudberry/commit/b05263bde4ab3167920abc81236cf4983e2e3ea1) - [ORCA] Update all join operators to derive set of table descriptors (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`590d18180cd`](https://github.com/apache/cloudberry/commit/590d18180cdc8ca35bb592a1d70a76f8787f6304) - Fix typo of statistics under gporca. (Zhang Mingli) [#944](https://github.com/apache/cloudberry/pull/944)
* [`50ad35a453f`](https://github.com/apache/cloudberry/commit/50ad35a453f648fac5cf8e22277c81cd7d7af9cb) - Fix for finding child output columns when parent is union while join pruning (#16960) (Dev Swaroop Chattopadhyay) [#944](https://github.com/apache/cloudberry/pull/944)
* [`80a26c835b2`](https://github.com/apache/cloudberry/commit/80a26c835b22ff5e1708de57c1a545f40dc0b7d8) - Enable ORCA to generate plans with row level security enabled (#16869) (Dev Swaroop Chattopadhyay) [#944](https://github.com/apache/cloudberry/pull/944)
* [`d09a32972fa`](https://github.com/apache/cloudberry/commit/d09a32972fa94afe4b9134614c6a9743a7a2f02f) - Fix memorys leak caught via ICW w/memory check (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`cc4e8f3c2d4`](https://github.com/apache/cloudberry/commit/cc4e8f3c2d45b7d38be7773a4529258469402788) - Fix query hang / fallback if involving CTE of replicated (Jingyu Wang) [#944](https://github.com/apache/cloudberry/pull/944)
* [`4130f66f363`](https://github.com/apache/cloudberry/commit/4130f66f363bf59ae3f46664dbe59bcf6f71fb89) - Revert "Queries on Distributed Replicated tables hangs when using optimizer" (Jingyu Wang) [#944](https://github.com/apache/cloudberry/pull/944)
* [`576811061a9`](https://github.com/apache/cloudberry/commit/576811061a97deef9f537f3669d2dc3e74f48d0d) - Update CDynamicPtrArray::Sort/IsSorted to require CompareFn (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`a28770e6f88`](https://github.com/apache/cloudberry/commit/a28770e6f88697a26a35f79a61a88cc98d5b8fb1) - Update CDynamicPtrArray::Equals to use operator== (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`87428023bf2`](https://github.com/apache/cloudberry/commit/87428023bf2cce40a6685e8a92e367ba80e46021) - Fill argtypes of eageragg (#16979) (chaotian) [#944](https://github.com/apache/cloudberry/pull/944)
* [`e148dde531e`](https://github.com/apache/cloudberry/commit/e148dde531e95f214adfc5537c0fa2e71b1dcc95) - Derive statistics only for used columns in Orca's Union All operator (#16811) (Pan Wang) [#944](https://github.com/apache/cloudberry/pull/944)
* [`f9c64761ce5`](https://github.com/apache/cloudberry/commit/f9c64761ce530d9d25274f6766393f6eeb289afe) - [ORCA] Implement plan hints for scan types (#16731) (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`364c3abf109`](https://github.com/apache/cloudberry/commit/364c3abf109937e8b016a5af264554f80fab2e93) - [ORCA] Use standard library for compile time assert (#16938) (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`ede206b4639`](https://github.com/apache/cloudberry/commit/ede206b4639003846e0b673db4c870b8df45c580) - [ORCA] Fix missing pdshashedEquiv in IndexOnlyScan (#16898) (fishtree1161) [#944](https://github.com/apache/cloudberry/pull/944)
* [`10abbc1ae2a`](https://github.com/apache/cloudberry/commit/10abbc1ae2a980666ce187ba866e225a9c5d3201) - Subquery Scan code cleanup (#16891) (Dev Swaroop Chattopadhyay) [#944](https://github.com/apache/cloudberry/pull/944)
* [`4c43fcaf0db`](https://github.com/apache/cloudberry/commit/4c43fcaf0dbba6b720d9d796a67eac0164f55675) - Remove redundant hash keys of motion (#16883) (chaotian) [#944](https://github.com/apache/cloudberry/pull/944)
* [`dc32e926977`](https://github.com/apache/cloudberry/commit/dc32e926977bfccb4bb2d775492767cc075d5125) - [ORCA] Add GUC to disable right outer join (ROJ) (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`9d5bf9aec77`](https://github.com/apache/cloudberry/commit/9d5bf9aec772fcbc8bab0185ad70b04e3eb2e0e4) - Port reltuples to Relation (Jingyu Wang) [#944](https://github.com/apache/cloudberry/pull/944)
* [`4f68dcf01a0`](https://github.com/apache/cloudberry/commit/4f68dcf01a03be24477e5c2ab0023de3c8660161) - Derive dynamic table scan cardinality from leaf parts (Jingyu Wang) [#944](https://github.com/apache/cloudberry/pull/944)
* [`3e8bbf1e1b9`](https://github.com/apache/cloudberry/commit/3e8bbf1e1b930acc9c77c38eb68b85f318acd23c) - Enable time-related cross-type pred stats calculation (Jingyu Wang) [#944](https://github.com/apache/cloudberry/pull/944)
* [`2c4229a67ce`](https://github.com/apache/cloudberry/commit/2c4229a67ce8c235226f1bbe8d0e1efb88eb6ead) - [cleanup] Remove unused code (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`cb4ccfd1a5d`](https://github.com/apache/cloudberry/commit/cb4ccfd1a5db1cd9d691d328f6feffcf7ed904ed) - [ORCA] Avoid extra motion from multiple self LOJ/ROJ (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`940bf22d5e5`](https://github.com/apache/cloudberry/commit/940bf22d5e5a1e594052da932f7af84d2e62781b) - [ORCA] Update DeriveTableDescriptor to return set of descriptors (David Kimura) [#944](https://github.com/apache/cloudberry/pull/944)
* [`38d80887923`](https://github.com/apache/cloudberry/commit/38d8088792319ce214daccd50a17e9d435c53dc8) - Remove unused index strategy and subtype information from Orca (#16868) (Chris Hajas) [#944](https://github.com/apache/cloudberry/pull/944)
* [`17e6c8b122c`](https://github.com/apache/cloudberry/commit/17e6c8b122c0e694f2946c704b9c58806cc852c0) - Fix performance regression caused by upstream materialized view changes (Adam Lee) [#946](https://github.com/apache/cloudberry/pull/946)
* [`d42418a3457`](https://github.com/apache/cloudberry/commit/d42418a3457e7e968d4358faa5949536cc3c2410) - Run REFRESH MATERIALIZED VIEW CONCURRENTLY in right security context (Heikki Linnakangas) [#946](https://github.com/apache/cloudberry/pull/946)
* [`c69821feef4`](https://github.com/apache/cloudberry/commit/c69821feef4a38c9f50884b42ed2b50b85679022) - Fix result is wrong when outer query has order by after LATERAL subquery (#17228) (xuejing zhao) [#946](https://github.com/apache/cloudberry/pull/946)
* [`0bb637ef488`](https://github.com/apache/cloudberry/commit/0bb637ef488a1f342f03a14c39d4658ece72479c) - Add update path for gp_toolkit to version "1.6" (Adam Lee) [#946](https://github.com/apache/cloudberry/pull/946)
* [`0716b614136`](https://github.com/apache/cloudberry/commit/0716b614136541159f4a480aefd27c79e8001e61) - Rename the resource group parameter 'memory_limit' to 'memory_quota' (#17378) (Zhenglong Li) [#946](https://github.com/apache/cloudberry/pull/946)
* [`a83c0f841bf`](https://github.com/apache/cloudberry/commit/a83c0f841bf1e7f0008c1952fc7f1b42357a6679) - CreateStmt: Introduce notion of origin (Soumyadeep Chakraborty) [#946](https://github.com/apache/cloudberry/pull/946)
* [`6c03bf0c544`](https://github.com/apache/cloudberry/commit/6c03bf0c544be83e86ad00adbfe8f5627bf16d49) - Fix pg_hint_plan test cases (zhoujiaqi) [#940](https://github.com/apache/cloudberry/pull/940)
* [`d64a8702e87`](https://github.com/apache/cloudberry/commit/d64a8702e87f0a544d5dd3f266f659ce4792fd30) - Refactor extension pg_hint_plan (zhoujiaqi) [#940](https://github.com/apache/cloudberry/pull/940)
* [`bd01547a0b0`](https://github.com/apache/cloudberry/commit/bd01547a0b0e3728264575f46aa8027e735716c1) - Adapater pg_hint_plan extension to CBDB (zhoujiaqi) [#940](https://github.com/apache/cloudberry/pull/940)
* [`63487baf86f`](https://github.com/apache/cloudberry/commit/63487baf86f22b4848d8403978ee89a7c7120a9c) - Add LD option --build-id only at RPM-build time (fengzh) [#940](https://github.com/apache/cloudberry/pull/940)
* [`19a4b7e108b`](https://github.com/apache/cloudberry/commit/19a4b7e108b9dd9bcc8f62d1a7ff233554155a04) - Fix pg_hint_plan build error with --disable-orca (David Kimura) [#940](https://github.com/apache/cloudberry/pull/940)
* [`9eb208e0518`](https://github.com/apache/cloudberry/commit/9eb208e0518eec436dc97a99b3aa43b77ecfc446) - Create pg_plan_hint parser hook (David Kimura) [#940](https://github.com/apache/cloudberry/pull/940)
* [`84fe7b469e3`](https://github.com/apache/cloudberry/commit/84fe7b469e3a1e56ddbb5be0660f5a0aa01152fc) - Fix and build pg_hint_plan (David Kimura) [#940](https://github.com/apache/cloudberry/pull/940)
* [`2900bc638c1`](https://github.com/apache/cloudberry/commit/2900bc638c1d14b77e4a8cddf0502b4d684fe066) - Merge pg_hint_plan source tree into GPDB (David Kimura) [#940](https://github.com/apache/cloudberry/pull/940)
* [`73fe8a4edd6`](https://github.com/apache/cloudberry/commit/73fe8a4edd666c80f6f4d5cac9eca9ce00b6ca88) - Rename Build step to Build RPM (#942) (Leonid) [#942](https://github.com/apache/cloudberry/pull/942)
* [`6d237381a7e`](https://github.com/apache/cloudberry/commit/6d237381a7e6bfd9d4086c9bb856c1377cfad13b) - Fix gp_toolkit_ao_funcs answer file. (Jianghua Yang) [#943](https://github.com/apache/cloudberry/pull/943)
* [`56c79999f59`](https://github.com/apache/cloudberry/commit/56c79999f59785b37e9eeccf0fa4a7b637a96df3) - Fix: remove redundant mock requirements. (Jianghua Yang) [#943](https://github.com/apache/cloudberry/pull/943)
* [`8b1af16fb35`](https://github.com/apache/cloudberry/commit/8b1af16fb35b95dd3f88e698e65f2d3b78427ff1) - use shutil.which to get cmd path in Popen (#16172) (RMT) [#943](https://github.com/apache/cloudberry/pull/943)
* [`115d8090bbb`](https://github.com/apache/cloudberry/commit/115d8090bbbdb0073e853a86c9a250121ffc576a) - Enable more tests and cleanup comments in isolation_schedule (Huansong Fu) [#943](https://github.com/apache/cloudberry/pull/943)
* [`562d9f857be`](https://github.com/apache/cloudberry/commit/562d9f857beccab0459c16794dbf517064d06b78) - Allow utility mode on coordinator to not upgrade lock for SELECT locking clause (Huansong Fu) [#943](https://github.com/apache/cloudberry/pull/943)
* [`29a22866b32`](https://github.com/apache/cloudberry/commit/29a22866b32261d8ccf82ffa3f64c2757c5baba1) - Fix gpcheckcat error against pg_description (#16130) (Shirisha SN) [#943](https://github.com/apache/cloudberry/pull/943)
* [`eedaa6b8def`](https://github.com/apache/cloudberry/commit/eedaa6b8defda4886b8da9c5d825a9075b87e832) - Load pageinspect implicitly for regress/isolation2 (Soumyadeep Chakraborty) [#943](https://github.com/apache/cloudberry/pull/943)
* [`0ae2660814a`](https://github.com/apache/cloudberry/commit/0ae2660814aeeaf3a945aebdb4a80a8b8f7982de) - Add two macros RelationStorageIsAoRows (RelationStorageIsAoCols|Huansong Fu) [#943](https://github.com/apache/cloudberry/pull/943)
* [`75928a683a1`](https://github.com/apache/cloudberry/commit/75928a683a164e5b3f156b1af290a9addf10fd75) - Fix singlenode partition test. (Jianghua Yang) [#936](https://github.com/apache/cloudberry/pull/936)
* [`460c66361fb`](https://github.com/apache/cloudberry/commit/460c66361fb83c0ba77f0cf6a7313c26622e8113) - Fix ANALYZE bug in expand_vacuum_rels (Brent Doil) [#936](https://github.com/apache/cloudberry/pull/936)
* [`73c2fbd7d04`](https://github.com/apache/cloudberry/commit/73c2fbd7d04cfe266783470caa00a3ca9264abad) - Remove psutil usage from regress tests (#15711) (Chen Mulong) [#936](https://github.com/apache/cloudberry/pull/936)
* [`74b7051d234`](https://github.com/apache/cloudberry/commit/74b7051d234fc625cbb4434228b65d9d6ef7db0c) - Behave test cases for checking the use of TRUSTED_SHELL in gpinitsystem (#15382) (Sruthi C P) [#936](https://github.com/apache/cloudberry/pull/936)
* [`a04da887fe8`](https://github.com/apache/cloudberry/commit/a04da887fe8d6919a2a2671fcfd7000bdda0a57e) - bugfix: Set the search_path correctly when CREATE EXTENSION WITH SCHEMA. (#13703) (QingMa) [#936](https://github.com/apache/cloudberry/pull/936)
* [`abce4a7298a`](https://github.com/apache/cloudberry/commit/abce4a7298a552f7bd04c18e38ae326ea3d84d94) - Test fix failure by adding timeeout and fixing test steps. (#14589) (Piyush Chandwadkar) [#936](https://github.com/apache/cloudberry/pull/936)
* [`e19c85c144b`](https://github.com/apache/cloudberry/commit/e19c85c144b1244b34a4fa20406559934f4db37e) - Add error/warning when exchanging/attach an external table (Huansong Fu) [#936](https://github.com/apache/cloudberry/pull/936)
* [`71abf7a0abc`](https://github.com/apache/cloudberry/commit/71abf7a0abc12384fb6be66d4d19e8d5005a432e) - Fix concurrent update core of partition table in Dynamic scan (#15253) (chaotian) [#936](https://github.com/apache/cloudberry/pull/936)
* [`63a4e168a6d`](https://github.com/apache/cloudberry/commit/63a4e168a6d58a7e0197ec480921253048a2dd00) - Fix build and icw tests "CLogical[Dynamic]IndexOnlyGet, Hash subplans, Update Index Scan Costing..." (zhoujiaqi) [#926](https://github.com/apache/cloudberry/pull/926)
* [`2a666c8cd50`](https://github.com/apache/cloudberry/commit/2a666c8cd507a5b63829270d89b9a14ac88a176f) - [ORCA] Remove the AO version check in indexscan check (zhoujiaqi) [#926](https://github.com/apache/cloudberry/pull/926)
* [`44b65975de0`](https://github.com/apache/cloudberry/commit/44b65975de0751bc7949d27bc74c4bd2451aa72a) - FIX: num of pages always be invalid when relation is root partition table (zhoujiaqi) [#926](https://github.com/apache/cloudberry/pull/926)
* [`06215e3713b`](https://github.com/apache/cloudberry/commit/06215e3713b86521f66ba2470ca4584092b1beb0) - Improve DPv2 algorithm to include distribution spec information with partition selectors (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`4f909484e28`](https://github.com/apache/cloudberry/commit/4f909484e283d31d3330914f643ef37f1600a387) - Enumerate bushy trees when considering partition selectors (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`66a147d4062`](https://github.com/apache/cloudberry/commit/66a147d40623c39d652acc9edad4c77d4cc6d3a8) - Align broadcast penalty in Orca's DPv2 to guc value (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`b3fdca2567e`](https://github.com/apache/cloudberry/commit/b3fdca2567e193c7185782a46951a03ca7ac5432) - Fix DbgStr when printing DP structs in Orca (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`2a7b1c0776c`](https://github.com/apache/cloudberry/commit/2a7b1c0776c83398cd85370a83510e0a07f86d28) - [ORCA] Fix boolean testing when the child is not an ident but an expression (#16825) (fishtree1161) [#926](https://github.com/apache/cloudberry/pull/926)
* [`8d4d067aa92`](https://github.com/apache/cloudberry/commit/8d4d067aa923c2e1b1944b8e60d28621f109575b) - Stop Addition Of 'gp_segment_id,ctid' As keys For Replicated Tables (#16767) (NISHANT SHARMA) [#926](https://github.com/apache/cloudberry/pull/926)
* [`5389e3d7886`](https://github.com/apache/cloudberry/commit/5389e3d7886af68cdae3ea17824ef394055fd915) - Fix query fallback when subqury present within LEAST()/GREATEST() (#16777) (Hari krishna) [#926](https://github.com/apache/cloudberry/pull/926)
* [`f2b0b5c18ce`](https://github.com/apache/cloudberry/commit/f2b0b5c18cee89b29617a4787871ca25b7f4164e) - Fix LeftJoinPruning pruns essential left join (#16690) (Pan Wang) [#926](https://github.com/apache/cloudberry/pull/926)
* [`6efed0e0cef`](https://github.com/apache/cloudberry/commit/6efed0e0cef47cec102801ba8e10eeade4374699) - [ORCA] Add CLogical[Dynamic]IndexOnlyGet operators (#16671) (David Kimura) [#926](https://github.com/apache/cloudberry/pull/926)
* [`e18aec25896`](https://github.com/apache/cloudberry/commit/e18aec258961bbc52487561ead50dabbe92291a4) - Remove 'hack' from comments from commit 6e2c664e (#16706) (David Kimura) [#926](https://github.com/apache/cloudberry/pull/926)
* [`b0e3251fdbd`](https://github.com/apache/cloudberry/commit/b0e3251fdbd29bb78f23c0b0a4f2fedc665f5e98) - Improve cardinality estimation for projection of ndv-preserving columns (#16664) (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`6d6d3cbfd06`](https://github.com/apache/cloudberry/commit/6d6d3cbfd06cf2a276454d2eef5f718463726056) - Fix uninitialized-use warning in CTranslatorDXLToPlStmt.cpp (Soumyadeep Chakraborty) [#926](https://github.com/apache/cloudberry/pull/926)
* [`6b19c44262b`](https://github.com/apache/cloudberry/commit/6b19c44262bc9f498646c30d7efb061c180744e3) - [ORCA] Relax client/server CTYPE encoding requirement (#16619) (David Kimura) [#926](https://github.com/apache/cloudberry/pull/926)
* [`82106c2ad0c`](https://github.com/apache/cloudberry/commit/82106c2ad0ce830ee20ad1ae2eff5bff21fe6bc7) - Add more functions as NDV preserving to improve join estimates in Orca (#16660) (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`c4ab9b38723`](https://github.com/apache/cloudberry/commit/c4ab9b387234450b40eed83721aa5c4a6ff0c69d) - [ORCA] Allow alias name to represent table name (#16659) (David Kimura) [#926](https://github.com/apache/cloudberry/pull/926)
* [`316553cbe4f`](https://github.com/apache/cloudberry/commit/316553cbe4f6c7be62675c25e0366e3771734dbc) - Fix CColRefSet DbgPrint (#16652) (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`0dafcea5da0`](https://github.com/apache/cloudberry/commit/0dafcea5da000fcd4f63c469816fa46dffb6f0e5) - Add support for min/max aggregates optimization (#16480) (Sanath Kumar Vobilisetty) [#926](https://github.com/apache/cloudberry/pull/926)
* [`1606f347a2e`](https://github.com/apache/cloudberry/commit/1606f347a2e1454d5178198b7ac3379f8998afb6) - Fix ORCA producing incorrect plan when handling SEMI join with RANDOM distributed table (greenplum-db#16611) (#16615) (Kevin.wyh) [#926](https://github.com/apache/cloudberry/pull/926)
* [`4bbbe4e49e6`](https://github.com/apache/cloudberry/commit/4bbbe4e49e6ad24ddf8b177cc756cd0c86c27072) - Derive additional distribution spec from union all (Jingyu Wang) [#926](https://github.com/apache/cloudberry/pull/926)
* [`359df4c0ad0`](https://github.com/apache/cloudberry/commit/359df4c0ad0a482933ab86cbb7776d06cd864936) - Hash subplans in Orca plans when possible (#16479) (Chris Hajas) [#926](https://github.com/apache/cloudberry/pull/926)
* [`613d670137a`](https://github.com/apache/cloudberry/commit/613d670137af730bc9eea1182fe3be7d242e824c) - [ORCA] Fix unused nested CTE pruning  (#16501) (David Kimura) [#926](https://github.com/apache/cloudberry/pull/926)
* [`74aac059a3f`](https://github.com/apache/cloudberry/commit/74aac059a3facd13f19f1abc70e32832d75c4f40) - ao/co: Consider all blocks as all-visible in ORCA (Soumyadeep Chakraborty) [#926](https://github.com/apache/cloudberry/pull/926)
* [`53cc83eecf6`](https://github.com/apache/cloudberry/commit/53cc83eecf667706de0594a2786c47fb8ea73407) - Compute statistics for a column of table (#16487) (NISHANT SHARMA) [#926](https://github.com/apache/cloudberry/pull/926)
* [`98da87b5295`](https://github.com/apache/cloudberry/commit/98da87b5295987c1d17c3a14fb398ddc64b36290) - Add GUC optimizer_enable_orderedagg to enable/disable ordered aggregates (#16491) (David Kimura) [#926](https://github.com/apache/cloudberry/pull/926)
* [`c10ea53b699`](https://github.com/apache/cloudberry/commit/c10ea53b69913e39f5b976e7239febb6e25d0795) - Fix ORCA invalid processing of nested SubLinks under aggregates. (Alexander Kondakov) [#926](https://github.com/apache/cloudberry/pull/926)
* [`31f6eea7af4`](https://github.com/apache/cloudberry/commit/31f6eea7af48d80ee934e7c4d9c2296ed3dbed90) - Fix ORCA invalid processing of nested SubLinks referenced in GROUP BY clause. (Alexander Kondakov) [#926](https://github.com/apache/cloudberry/pull/926)
* [`9f2fe295780`](https://github.com/apache/cloudberry/commit/9f2fe29578058910742eeb42f097d76fd35e0b0e) - Fix ORCA invalid processing of nested SubLinks with GROUP BY attributes (Alexander Kondakov) [#926](https://github.com/apache/cloudberry/pull/926)
* [`45311a3c454`](https://github.com/apache/cloudberry/commit/45311a3c454356a9f3556ba33c5b23f7c3e4e0d7) - Update Index Scan Costing for index columns not present in predicate and vice versa (#16156) (NISHANT SHARMA) [#926](https://github.com/apache/cloudberry/pull/926)
* [`98dd9109d95`](https://github.com/apache/cloudberry/commit/98dd9109d9537818e617043f1c5b726875f2822a) - Fix cherry-pick. (Zhang Mingli) [#931](https://github.com/apache/cloudberry/pull/931)
* [`40b1175ff21`](https://github.com/apache/cloudberry/commit/40b1175ff2199bd8b1a673b676acf7507282ac53) - Fix the locus of foreign upper path (#15821) (zhaorui) [#931](https://github.com/apache/cloudberry/pull/931)
* [`189bd60cbae`](https://github.com/apache/cloudberry/commit/189bd60cbae0f25d666a88fda2c5e8b6eea8fa18) - Doc: update the project name to Apache Cloudberry (Dianjin Wang) [#925](https://github.com/apache/cloudberry/pull/925)
* [`3d4b736fb7b`](https://github.com/apache/cloudberry/commit/3d4b736fb7bbfa8dae2de9e3c143f316f1799b3a) - Fix possible wrong mocking path in mocker.py (dh-cloud) [#923](https://github.com/apache/cloudberry/pull/923)
* [`6c53ab4155f`](https://github.com/apache/cloudberry/commit/6c53ab4155f96ec36861bf9373442feb2af30e87) - Add missing keywords to PartitionIdentKeyword (Huansong Fu) [#923](https://github.com/apache/cloudberry/pull/923)
* [`92daee2ea68`](https://github.com/apache/cloudberry/commit/92daee2ea688a5b9d8c96a16474f1ac73a3de7e3) - fix typo: s/ANALZE/ANALYZE (Haolin Wang) [#923](https://github.com/apache/cloudberry/pull/923)
* [`324f0470d57`](https://github.com/apache/cloudberry/commit/324f0470d57a4e1c50816af622f1ad82708d0f18) - Fix for supporting deferrable keyword on primary/unique keys (#15816) (Hari krishna) [#923](https://github.com/apache/cloudberry/pull/923)
* [`1eaad20b7e3`](https://github.com/apache/cloudberry/commit/1eaad20b7e3320996c4e8f8bae78e889a552b228) - Fix case pg_rewind_fail_missing_xlog (wenru yan) [#923](https://github.com/apache/cloudberry/pull/923)
* [`39098f031a9`](https://github.com/apache/cloudberry/commit/39098f031a999ca72ae79883e76fec5fa1926e9a) - bug fix: unable to understand 'IS NOT DISTINCT' (7X) (#15183) (Yongtao Huang) [#923](https://github.com/apache/cloudberry/pull/923)
* [`7d0cf0e5da1`](https://github.com/apache/cloudberry/commit/7d0cf0e5da1fce3d96ba3eb89b5edbddc565586b) - Fix an issue that we are not checking file .204800 in ao_foreach_extent_file (Huansong Fu) [#923](https://github.com/apache/cloudberry/pull/923)
* [`f65e82bcb84`](https://github.com/apache/cloudberry/commit/f65e82bcb84076aa9c5a225d9856ccb86c86bd30) - Fix icw tests "index only scan, ao support index only scan, rename fallback message..." (zhoujiaqi) [#916](https://github.com/apache/cloudberry/pull/916)
* [`fec83a88b07`](https://github.com/apache/cloudberry/commit/fec83a88b07a910c63ef2e525ebd51d279ef3342) - Fix build && core generated by cherry-pick "index only scan, ao support index only scan..." (zhoujiaqi) [#916](https://github.com/apache/cloudberry/pull/916)
* [`7e59649b402`](https://github.com/apache/cloudberry/commit/7e59649b4027f495f2b99cbc5854aef54765f1bc) - [ORCA] Fix bug checking index_can_return() (#16575) (David Kimura) [#916](https://github.com/apache/cloudberry/pull/916)
* [`9d8d8c29f5d`](https://github.com/apache/cloudberry/commit/9d8d8c29f5d9a008b4be0752faeb1574155bd109) - Fix "cache lookup failed for foreign table" for multi-level partition table with foreign tables in Orca (#16376) (Kevin.wyh) [#916](https://github.com/apache/cloudberry/pull/916)
* [`a722fee7cf7`](https://github.com/apache/cloudberry/commit/a722fee7cf7a12c783672baa89bb3d06650fdb62) - Remove unused PartIndexId DXL token from Orca (#16418) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`9374844ee40`](https://github.com/apache/cloudberry/commit/9374844ee40c5250d21f8d0da40df7ef8193816a) - Orca memory pool refactoring (#16392) (Georgy Shelkovy) [#916](https://github.com/apache/cloudberry/pull/916)
* [`68ad5774368`](https://github.com/apache/cloudberry/commit/68ad57743689d93d59a1e3736ceb0cccdb2b91be) - Optimize serialization of IMDId objects in Orca to be lazy (#16339) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`282743a7f1d`](https://github.com/apache/cloudberry/commit/282743a7f1dcfd947e7474e5d911230df71f12bf) - Optimize string creation in Orca (#16332) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`53ca202d72c`](https://github.com/apache/cloudberry/commit/53ca202d72ce361c5b7d1f076ac0dbe13ada518a) - Introducing Non-Replicated Distribution Spec (Jingyu Wang) [#916](https://github.com/apache/cloudberry/pull/916)
* [`1c07547b3f0`](https://github.com/apache/cloudberry/commit/1c07547b3f06928717169cb9b8d8b233a9f80669) - Remove check restricting some array casts by orca (#16379) (bhari) [#916](https://github.com/apache/cloudberry/pull/916)
* [`7d0ea5944ec`](https://github.com/apache/cloudberry/commit/7d0ea5944ecb60ddf13080e7bf9784b610bd5ef9) - [ORCA] Add fallback on relations with 'hnsw' index type (#16384) (David Kimura) [#916](https://github.com/apache/cloudberry/pull/916)
* [`584db612182`](https://github.com/apache/cloudberry/commit/584db612182c6bf078e529c1523acb7e4d22ab8a) - Fix clang-formatting in CPhysicalJoin.cpp and MDP file (#16377) (Chandan Kunal) [#916](https://github.com/apache/cloudberry/pull/916)
* [`03eca3430f2`](https://github.com/apache/cloudberry/commit/03eca3430f26a7b5b53fbb9eb3f918137d32f9c7) - Correcting derived distribution spec for CPhysicalJoin. (#16342) (Chandan Kunal) [#916](https://github.com/apache/cloudberry/pull/916)
* [`08e0b7489ca`](https://github.com/apache/cloudberry/commit/08e0b7489ca4083b7f232ab16a9e972ff9ff0b30) - Support FIELDSELECT node from ORCA (#16265) (Hari krishna) [#916](https://github.com/apache/cloudberry/pull/916)
* [`cecc82d5675`](https://github.com/apache/cloudberry/commit/cecc82d5675a81e2864d231f6e96d5c46dcd5d6c) - Fixing column width of partitioned tables (#16282) (Dev Swaroop Chattopadhyay) [#916](https://github.com/apache/cloudberry/pull/916)
* [`37224a3d171`](https://github.com/apache/cloudberry/commit/37224a3d171d479e67b9264e65fca64476557e4c) - [ORCA] Allow index only scan on more index types (#16260) (David Kimura) [#916](https://github.com/apache/cloudberry/pull/916)
* [`80fa455a18a`](https://github.com/apache/cloudberry/commit/80fa455a18a8bb77e032e580750637ab0700f4e6) - Fix redundant sort being enforced on group aggregate (#16276) (bhari) [#916](https://github.com/apache/cloudberry/pull/916)
* [`f3ada177c24`](https://github.com/apache/cloudberry/commit/f3ada177c24b91546333b0cfbd2a37cd5b5db624) - Remove default column information from Orca (#16273) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`25197b4e1a4`](https://github.com/apache/cloudberry/commit/25197b4e1a4499a8503ed5662e6eefaced839b8f) - Refactor string length checks in Orca (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`39dd11c2ed4`](https://github.com/apache/cloudberry/commit/39dd11c2ed45f39a9449eb946924d8d808631928) - Added support for Backward indexscan in ORCA (#16087) (Sanath Kumar Vobilisetty) [#916](https://github.com/apache/cloudberry/pull/916)
* [`f56a70d47ac`](https://github.com/apache/cloudberry/commit/f56a70d47ac1a31da7fb4f581515fffd5ac1d991) - Remove unused partitioning code in Orca (#16274) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`bb472f913c8`](https://github.com/apache/cloudberry/commit/bb472f913c86ee46c92c700176de889efe71d080) - Update ICW expected files (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`32f515dd111`](https://github.com/apache/cloudberry/commit/32f515dd1113922a73cc05c08a6a697b53f80a71) - Change fallback message from GPORCA to Postgres-based planner (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`85ed090b8f6`](https://github.com/apache/cloudberry/commit/85ed090b8f69bab97718461bd76b69455015f894) - Rename GPORCA and planner optimizer in logging and explain output (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`d2f3cf97a29`](https://github.com/apache/cloudberry/commit/d2f3cf97a299591d644b85d3781ff830edff65ab) - Remove ExmiPlStmt2DXLConversion error label in Orca (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`5d86a49b131`](https://github.com/apache/cloudberry/commit/5d86a49b131b808263c604f41b3b5de0e32fd4ed) - Remove unused error messages in Orca (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`3c3e5e51c01`](https://github.com/apache/cloudberry/commit/3c3e5e51c01392389fc5693b2898f37b31939e1b) - [ORCA] Enable index-only scan on AO tables (#16162) (David Kimura) [#916](https://github.com/apache/cloudberry/pull/916)
* [`67a1bee4dcf`](https://github.com/apache/cloudberry/commit/67a1bee4dcf405c336f5a1cd05aa4702f73e4886) - Fix Orca crash due to improper colref mapping with CTEs (#16212) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`7a2b1360d47`](https://github.com/apache/cloudberry/commit/7a2b1360d4775e341763a3e9d79412381b7cb2f2) - Update ORCA ans files for index costing change (Soumyadeep Chakraborty) [#916](https://github.com/apache/cloudberry/pull/916)
* [`2b72516adb6`](https://github.com/apache/cloudberry/commit/2b72516adb6a6a2fe1ae0419e712e8a987f39f7c) - Fall back to planner for queries on relations with pgvector index (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`d2b4b03762f`](https://github.com/apache/cloudberry/commit/d2b4b03762fa19078611b2f918a7067e9356db72) - ORCA initialization refactoring (Georgy Shelkovy) [#916](https://github.com/apache/cloudberry/pull/916)
* [`cfa4f8073a7`](https://github.com/apache/cloudberry/commit/cfa4f8073a78438dc1c4261768cb544eff2f22a2) - Change Query Parameter fallback message in Orca to notice type (#16197) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`a8ac5b3d74d`](https://github.com/apache/cloudberry/commit/a8ac5b3d74d6758bd3008a4478a08e5a4ae538ce) - Do not convert IN query to Exists for set returning function (#16049) (bhari) [#916](https://github.com/apache/cloudberry/pull/916)
* [`45c177bcc38`](https://github.com/apache/cloudberry/commit/45c177bcc3836128e7fb09067ae4042155591b78) - Enable ORCA to generate IndexScan plans with ScalarArrayOp quals (Ekta Khanna) [#916](https://github.com/apache/cloudberry/pull/916)
* [`45267fcfc6f`](https://github.com/apache/cloudberry/commit/45267fcfc6fbda815d31706b0b0e146e1295a3ab) - Enhance Orca's cardinality estimation for local aggregate (#15992) (Hari krishna) [#916](https://github.com/apache/cloudberry/pull/916)
* [`7ba8e156755`](https://github.com/apache/cloudberry/commit/7ba8e156755740e374be61cc8387308650b5f9c7) - [ORCA] Implement dynamic index only scan (#15974) (David Kimura) [#916](https://github.com/apache/cloudberry/pull/916)
* [`4ea126dd0ea`](https://github.com/apache/cloudberry/commit/4ea126dd0eab625d4c638f768b71e12c623b84c7) - Disable some Orca histogram asserts (#16052) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`33b36471d27`](https://github.com/apache/cloudberry/commit/33b36471d27c703dc02c7a0e1817e7374730043f) - Correct the child order when creating grouping sets for the rollup (#16096) (Hari krishna) [#916](https://github.com/apache/cloudberry/pull/916)
* [`c8344d1a384`](https://github.com/apache/cloudberry/commit/c8344d1a384316ca1e6349325f671bdd0ac25982) - Declare ACL mode as a ULONG (Soumyadeep Chakraborty) [#916](https://github.com/apache/cloudberry/pull/916)
* [`ff9f7609d0f`](https://github.com/apache/cloudberry/commit/ff9f7609d0fb9d4691e4445ac29a77d0f8e63488) - fix bug of RelabelType in group by clause (#15958) (chaotian) [#916](https://github.com/apache/cloudberry/pull/916)
* [`19549a23d5c`](https://github.com/apache/cloudberry/commit/19549a23d5cb262867155063472efa1355b72af3) - Remove unused partition selector code from Orca (#16054) (Chris Hajas) [#916](https://github.com/apache/cloudberry/pull/916)
* [`ed7d24463b7`](https://github.com/apache/cloudberry/commit/ed7d24463b7c820095e61264909596c6fa2f0d0e) - Pass requiredPerms from parse tree to plstmt (Jingyu Wang) [#916](https://github.com/apache/cloudberry/pull/916)
* [`c069dfbcb44`](https://github.com/apache/cloudberry/commit/c069dfbcb44be51d34f6f2afd5d051a11f05eaf8) - Force two-stage local aggregate to remove duplicates (Maxim Smyatkin) [#916](https://github.com/apache/cloudberry/pull/916)
* [`b99b12bae7b`](https://github.com/apache/cloudberry/commit/b99b12bae7b62fccf7510e4f07063b024a80856b) - Error out when create or put role into system_group. (Zhenghua Lyu) [#921](https://github.com/apache/cloudberry/pull/921)
* [`821e59ea8a2`](https://github.com/apache/cloudberry/commit/821e59ea8a288ed3bcff12c37797ebb4445900d3) - [gpdemo] polish checkDemoConfig (#13672) (Junwang Zhao) [#921](https://github.com/apache/cloudberry/pull/921)
* [`2a73f3bec46`](https://github.com/apache/cloudberry/commit/2a73f3bec462b4bb554bfd9c5624cef94b26e421) - Update fix_mdps.py script to python3 syntax (David Kimura) [#921](https://github.com/apache/cloudberry/pull/921)
* [`13c72534bb7`](https://github.com/apache/cloudberry/commit/13c72534bb7662627c67ea9cc26a8a94edabd73e) - Fail the SplitUpdate if table's any child has update triggers (#17227) (Adam Lee) [#921](https://github.com/apache/cloudberry/pull/921)
* [`6441a88564e`](https://github.com/apache/cloudberry/commit/6441a88564e80138df8b42a5094bc0ad7da0c0f7) - Guc gp_resgroup_memory_query_fixed_mem cannot be larger than max_statement_mem. (Zhenghua Lyu) [#921](https://github.com/apache/cloudberry/pull/921)
* [`a00b90563af`](https://github.com/apache/cloudberry/commit/a00b90563af897d45bf506a8d972e21d036c7634) - Enhance logging for dispatch conn liveness checks (Soumyadeep Chakraborty) [#921](https://github.com/apache/cloudberry/pull/921)
* [`659823ef113`](https://github.com/apache/cloudberry/commit/659823ef113555ee9c95722a5670a9b8d15103cf) - Revert "Suppress Clang's register deprecation warning." (Brent Doil) [#921](https://github.com/apache/cloudberry/pull/921)
* [`03b31902f60`](https://github.com/apache/cloudberry/commit/03b31902f60577f1c389c8821244e3f3b8b30702) - Resolve a FIXME in MergeAttributes() (Huansong Fu) [#921](https://github.com/apache/cloudberry/pull/921)
* [`52f696e3810`](https://github.com/apache/cloudberry/commit/52f696e3810c9e8a8ab4ae290819868b7e296501) - Ignore any local .psqlrc when running tests. (Jianghua Yang) [#921](https://github.com/apache/cloudberry/pull/921)
* [`fc5bb44ef83`](https://github.com/apache/cloudberry/commit/fc5bb44ef83f27e1e4f090494aecbe7565c83698) - Use __gp_aoblkdir() in alter_table_set_am.sql (Soumyadeep Chakraborty) [#921](https://github.com/apache/cloudberry/pull/921)
* [`28facb8dcf0`](https://github.com/apache/cloudberry/commit/28facb8dcf04cfef678b0b5ccb0bf9d305112322) - Remove unportable use of timezone in recent test (Alvaro Herrera) [#921](https://github.com/apache/cloudberry/pull/921)
* [`dd5aa2092f8`](https://github.com/apache/cloudberry/commit/dd5aa2092f8a7b073ca6cf0ad53d575148e5339d) - Fix "unexpected gang size" issue. (Tender Wang) [#919](https://github.com/apache/cloudberry/pull/919)
* [`319b1e096b9`](https://github.com/apache/cloudberry/commit/319b1e096b935c9c4402f898d9f41477f4dcb82e) - Fix singlenode opr_sanity test. (Jianghua Yang) [#915](https://github.com/apache/cloudberry/pull/915)
* [`a43da9bb2ce`](https://github.com/apache/cloudberry/commit/a43da9bb2ce88f2fa12b47786cb0dc26356bbd5b) - Fix possible inconsistency between bitmap LOV table and index (Huansong Fu) [#915](https://github.com/apache/cloudberry/pull/915)
* [`95446553872`](https://github.com/apache/cloudberry/commit/954465538726640b5481b1b85c57c1aefe2ca648) - Remove remainings of "frozen insert" (Huansong Fu) [#915](https://github.com/apache/cloudberry/pull/915)
* [`63afe21d771`](https://github.com/apache/cloudberry/commit/63afe21d7719e90db0b52334270244d586f23475) - Change how new aoseg/aocsseg tuples are frozen (Huansong Fu) [#915](https://github.com/apache/cloudberry/pull/915)
* [`375f3217e8c`](https://github.com/apache/cloudberry/commit/375f3217e8cbf66c9060d16f182ddda4151303f1) - fix regex for etc/environment.d (#15254) (Sasasu) [#915](https://github.com/apache/cloudberry/pull/915)
* [`ca5e2dc967d`](https://github.com/apache/cloudberry/commit/ca5e2dc967d0db7e37760a1f39c409b7ab8aa38d) - Loading environment variable from etc/environment.d (#14327) (Sasasu) [#915](https://github.com/apache/cloudberry/pull/915)
* [`c35d399f0b9`](https://github.com/apache/cloudberry/commit/c35d399f0b9650f9d5d41c75a472b2502ba9c4b4) - Implement gp_log_backend_memory_contexts (Andrew Repp) [#915](https://github.com/apache/cloudberry/pull/915)
* [`848aefbe651`](https://github.com/apache/cloudberry/commit/848aefbe65141f56ad5979b49a7423df49f87109) - Fix memory leaks in auto_explain. (Andrey Sokolov) [#915](https://github.com/apache/cloudberry/pull/915)
* [`ca0c1809825`](https://github.com/apache/cloudberry/commit/ca0c1809825add5697db03921e5f224f866f7810) - Set replication slot's restart_lsn to not greater than redo location (Alexandra Wang) [#915](https://github.com/apache/cloudberry/pull/915)
* [`cc8e9c23698`](https://github.com/apache/cloudberry/commit/cc8e9c2369882f971045f9190053afc12dad8fa2) - gpinitsystem: Fix bug when calling ss remotely (Nihal Jain) [#915](https://github.com/apache/cloudberry/pull/915)
* [`36868ee4972`](https://github.com/apache/cloudberry/commit/36868ee4972c840988b062a6b9a6d7fc8c7335d9) - Get streaming hash aggregate back (Adam Lee) [#913](https://github.com/apache/cloudberry/pull/913)
* [`20761889c54`](https://github.com/apache/cloudberry/commit/20761889c54793aa610c2db055c62567c1b83cf2) - Add additional unit test for max_slot_wal_keep_size. (Jianghua Yang) [#910](https://github.com/apache/cloudberry/pull/910)
* [`c709425c21f`](https://github.com/apache/cloudberry/commit/c709425c21f575cb6151f718f2d82481128c2b42) - Remove safefswritesize from pg_appendonly (Brent Doil) [#910](https://github.com/apache/cloudberry/pull/910)
* [`a9385c1700f`](https://github.com/apache/cloudberry/commit/a9385c1700fa4aebec0201e5ba5debe423e5db34) - Pass tableName to AppendOnlyStorageRead_ReadNextBlock_success (Brent Doil) [#910](https://github.com/apache/cloudberry/pull/910)
* [`cb62f091c6c`](https://github.com/apache/cloudberry/commit/cb62f091c6cf8dc5bba194ac83d70b9776e712e3) - Fix error in ATSETAM to ao_column with dropped column (Huansong Fu) [#910](https://github.com/apache/cloudberry/pull/910)
* [`3f0bc2c068c`](https://github.com/apache/cloudberry/commit/3f0bc2c068c598fd4f88d4bcefb293ca6fcce2cf) - Shortcut when find either side is nullable. (Zhenghua Lyu) [#910](https://github.com/apache/cloudberry/pull/910)
* [`75429e1e379`](https://github.com/apache/cloudberry/commit/75429e1e379d9c32e6ee5d06bbdd630a936036ca) - fix distribution key not exist when creating stage table (#14758) (flykos) [#910](https://github.com/apache/cloudberry/pull/910)
* [`6a7820f6880`](https://github.com/apache/cloudberry/commit/6a7820f688010172d0e4aa7ea2c4a4abad8e0b67) - Fix gpload2 answer file. (Jianghua Yang) [#910](https://github.com/apache/cloudberry/pull/910)
* [`6d2489df5ad`](https://github.com/apache/cloudberry/commit/6d2489df5ad7efa80417ff3611a9a01c115e2852) - Resolve duplicate reloptions output in compresstype.source (Brent Doil) [#910](https://github.com/apache/cloudberry/pull/910)
* [`0f684d1fe32`](https://github.com/apache/cloudberry/commit/0f684d1fe32e5bfc50080e83e558ef2960ff57b9) - Fix wrong result due to ignore PlaceHolderVar. (Tender Wang) [#911](https://github.com/apache/cloudberry/pull/911)
* [`1332cf67ad2`](https://github.com/apache/cloudberry/commit/1332cf67ad2c8c141a790e665a75b5130e651c1c) - Fix cherry-pick issues. (reshke) [#859](https://github.com/apache/cloudberry/pull/859)
* [`29a24b23529`](https://github.com/apache/cloudberry/commit/29a24b23529cda3ecb120936938d64e15e8777a9) - Reimplement BRIN internals for AO/CO tables (Soumyadeep Chakraborty) [#859](https://github.com/apache/cloudberry/pull/859)
* [`4050cd74436`](https://github.com/apache/cloudberry/commit/4050cd74436ba47fae4f1b457e5bcfcfc79f48c7) - brin: Remove upper pages for AO/CO tables (Soumyadeep Chakraborty) [#859](https://github.com/apache/cloudberry/pull/859)
* [`8aaec525f2d`](https://github.com/apache/cloudberry/commit/8aaec525f2d0e99d3f970d79544302dcb15043a6) - uao/brin test: Correct typos (Soumyadeep Chakraborty) [#859](https://github.com/apache/cloudberry/pull/859)
* [`b637ef01fef`](https://github.com/apache/cloudberry/commit/b637ef01fefc9aa54418d536a59616ba43d8b0b7) - brin ao/co: Correct partial scan bounds logic (Soumyadeep Chakraborty) [#859](https://github.com/apache/cloudberry/pull/859)
* [`89dd29938c0`](https://github.com/apache/cloudberry/commit/89dd29938c07b6e6ab00d5db9da78373e48a094b) - brin: Disable workitems test (Soumyadeep Chakraborty) [#859](https://github.com/apache/cloudberry/pull/859)
* [`b475183cb4c`](https://github.com/apache/cloudberry/commit/b475183cb4cfd1dc758174c90ec49230c63a0b39) - brin.c: Remove dead includes (Soumyadeep Chakraborty) [#859](https://github.com/apache/cloudberry/pull/859)
* [`3e8832121a5`](https://github.com/apache/cloudberry/commit/3e8832121a5faff63a54b9333e16c66979da73ed) - Revert "Fix incremental recovery failure because the checkpoint redo wal file before divergence LSN is gone." (Alexandra Wang) [#907](https://github.com/apache/cloudberry/pull/907)
* [`7b76d4c863b`](https://github.com/apache/cloudberry/commit/7b76d4c863b0dae6468fee371430e92b659f1949) - Make 102_non_standby_recovery.pl less likely to fail. (Brent Doil) [#907](https://github.com/apache/cloudberry/pull/907)
* [`ca1ab7fc0c1`](https://github.com/apache/cloudberry/commit/ca1ab7fc0c13267d996553e9319c8f16ae5d460d) - Remove FIXME in PosgresNode.pm (Brent Doil) [#907](https://github.com/apache/cloudberry/pull/907)
* [`95831e24f51`](https://github.com/apache/cloudberry/commit/95831e24f51e248341865f7934b92c76a44df43c) - Remove uses of register due to incompatibility with C++17 and up (Andres Freund) [#907](https://github.com/apache/cloudberry/pull/907)
* [`b9cb020e27e`](https://github.com/apache/cloudberry/commit/b9cb020e27ebc09577e26846f2413831aa306106) - Fix resqueue does not work when using jdbc extend protocol. (airfan) [#907](https://github.com/apache/cloudberry/pull/907)
* [`9e294aa05a8`](https://github.com/apache/cloudberry/commit/9e294aa05a8bab102a521dcf0b5a10231065b19a) - Remove obsolete HAVE_BUGGY_SOLARIS_STRTOD (Peter Eisentraut) [#907](https://github.com/apache/cloudberry/pull/907)
* [`1073dbdf59b`](https://github.com/apache/cloudberry/commit/1073dbdf59b26d11c5e5389514a5da7c3b16bd5c) - Invalidate CatCache in AbortTransaction to clear reader gang's cache. (#14048) (Zhenghua Lyu) [#907](https://github.com/apache/cloudberry/pull/907)
* [`bcee4cb2349`](https://github.com/apache/cloudberry/commit/bcee4cb23494d265502497be77457bd1fbc8f8b2) - Make ALTER TABLE ... OWNER recurse by default (Huansong Fu) [#907](https://github.com/apache/cloudberry/pull/907)
* [`4195e3722bf`](https://github.com/apache/cloudberry/commit/4195e3722bfa5127fce9deb996290e1fface9f27) - Add HINT for restartpoint race with KeepFileRestoredFromArchive(). (Noah Misch) [#907](https://github.com/apache/cloudberry/pull/907)
* [`36988b60f44`](https://github.com/apache/cloudberry/commit/36988b60f44933b0c0fc8218a4a2f3d8fc52f848) - Fix a flaky test idle_in_transaction_session_timeout (Huansong Fu) [#907](https://github.com/apache/cloudberry/pull/907)
* [`fc7c4e7a790`](https://github.com/apache/cloudberry/commit/fc7c4e7a7904d247361472b86bc6aeffe65bb01f) - Fix singlenode jsonpath,subselect test. (Jianghua Yang) [#906](https://github.com/apache/cloudberry/pull/906)
* [`e47aa64c56e`](https://github.com/apache/cloudberry/commit/e47aa64c56e8c57efb1bd61f1bc8150e037513ea) - Dispatch ALTER DATABASE with options (Soumyadeep Chakraborty) [#906](https://github.com/apache/cloudberry/pull/906)
* [`31dabef2c15`](https://github.com/apache/cloudberry/commit/31dabef2c1570a678c68d5f216ffd49a84c50035) - ao/co: Perform serializable isolation check early (Soumyadeep Chakraborty) [#906](https://github.com/apache/cloudberry/pull/906)
* [`e9f954e865e`](https://github.com/apache/cloudberry/commit/e9f954e865ee227955da9bfa7ff7389747097f22) - Commits start blocking only if STREAMING or CATCHUP within range. (Paul Guo) [#906](https://github.com/apache/cloudberry/pull/906)
* [`352435e0483`](https://github.com/apache/cloudberry/commit/352435e0483ffad4f355544bc224486144aa8c32) - Resolve GPDB_12_MERGE_FIXME in describe.c about legacy partitioning. (Wenru Yan) [#906](https://github.com/apache/cloudberry/pull/906)
* [`6bdae9d7700`](https://github.com/apache/cloudberry/commit/6bdae9d77008e522550d7c8cfc1f22a437cbafa1) - Update gp_create_restore_point() catalog function (Jimmy Yih) [#906](https://github.com/apache/cloudberry/pull/906)
* [`bd46d007075`](https://github.com/apache/cloudberry/commit/bd46d007075f5c5ef7a13c0550a482a9647aafc0) - Refactor gp_replica_check.py to use PygreSQL to query Greenplum. (Zhenghua Lyu) [#906](https://github.com/apache/cloudberry/pull/906)
* [`636f651c4e0`](https://github.com/apache/cloudberry/commit/636f651c4e08e1f8806c9d0856f8cdc46eb295a3) - added cluster health check for gpexpand (#13757) (Rakesh Sharma) [#906](https://github.com/apache/cloudberry/pull/906)
* [`f9d3291d2e2`](https://github.com/apache/cloudberry/commit/f9d3291d2e2f16f29c671c0f13682e96b45213ce) - Revert "Avoid creating toast table for CO table during binary upgrade" (Ashwin Agrawal) [#906](https://github.com/apache/cloudberry/pull/906)
* [`221f6b2e9f1`](https://github.com/apache/cloudberry/commit/221f6b2e9f1b32496c0f1be84981a239d589f7d2) - Remove %error-verbose directive from jsonpath parser (Andrew Dunstan) [#906](https://github.com/apache/cloudberry/pull/906)
* [`a97f0422926`](https://github.com/apache/cloudberry/commit/a97f0422926229a1dc3193a44c8280aa5c17aed8) - Fix previous commit's ecpg_clocale for ppc Darwin. (Noah Misch) [#906](https://github.com/apache/cloudberry/pull/906)
* [`7be778b908f`](https://github.com/apache/cloudberry/commit/7be778b908ffe783d935610d24f95b13f57afb33) - Resolve GPDB_12_MERGE_FIXME in subselect.sql (wenru yan) [#906](https://github.com/apache/cloudberry/pull/906)
* [`872c8ffea75`](https://github.com/apache/cloudberry/commit/872c8ffea759ccf170b93c3ae6a1f4184e63e1cb) - Revert PG_TRY() / PG_CATCH() surrounding of CFI. (#13567) (Aegeaner) [#906](https://github.com/apache/cloudberry/pull/906)
* [`f824a21e193`](https://github.com/apache/cloudberry/commit/f824a21e193e93c6998e9eb30f77348155791936) - Remove unnecessary null pointer checks in gpcloud code (#13877) (jingwen-yang-yjw) [#906](https://github.com/apache/cloudberry/pull/906)
* [`9b96cc61f38`](https://github.com/apache/cloudberry/commit/9b96cc61f381ddc3042a6d3becc7cfa59bd87a80) - Don't generate unique rowid path for DML on replicated table. (Zhenghua Lyu) [#906](https://github.com/apache/cloudberry/pull/906)
* [`e9bcc8db385`](https://github.com/apache/cloudberry/commit/e9bcc8db38589da414c64207f19084a3e407ba24) - Fix partition creation to handle MAXVALUE in subpartition bounds (Zhang Mingli) [#904](https://github.com/apache/cloudberry/pull/904)
* [`cf220529bef`](https://github.com/apache/cloudberry/commit/cf220529bef13cd19fa145be4be6091dbf3aa53a) - Fix: cherry-pick "pushdown in union all, pruned partitions, plans with the ProjectSet node..." (zhoujiaqi) [#905](https://github.com/apache/cloudberry/pull/905)
* [`f009bc86855`](https://github.com/apache/cloudberry/commit/f009bc86855636a2ce3e3eaf17f49da98e963a59) - Refactoring RTE deduplication logic from PR #14304 (Jingyu Wang) [#905](https://github.com/apache/cloudberry/pull/905)
* [`2b056bc07cc`](https://github.com/apache/cloudberry/commit/2b056bc07cc280f3c63a46909863d1bb86f34c35) - Backfill isolation2 test for commit 0417465a744 (#16018) (David Kimura) [#905](https://github.com/apache/cloudberry/pull/905)
* [`34ab0b25291`](https://github.com/apache/cloudberry/commit/34ab0b2529125f20021736a374742fc09d9859f5) - Remove unuseful mdps from Orca (#16035) (Chris Hajas) [#905](https://github.com/apache/cloudberry/pull/905)
* [`3322a41df75`](https://github.com/apache/cloudberry/commit/3322a41df752fa83b322e7c50268be6d28a7c610) - Utilizing btree index's order property when used as part of Order By clause and limit (#15894) (Sanath Kumar Vobilisetty) [#905](https://github.com/apache/cloudberry/pull/905)
* [`4b6211cb97e`](https://github.com/apache/cloudberry/commit/4b6211cb97ed92d41cf80764b49c262446db5d57) - Fix memory leak in Orca (#16006) (Chris Hajas) [#905](https://github.com/apache/cloudberry/pull/905)
* [`fe394b08caa`](https://github.com/apache/cloudberry/commit/fe394b08caaeb9db8a5307330aebad5ed7c12e8b) - [ORCA] Support BitmapIndex plans for ArrayCmp on Hash indexes (Ekta Khanna) [#905](https://github.com/apache/cloudberry/pull/905)
* [`0982f343fea`](https://github.com/apache/cloudberry/commit/0982f343feabddc446ecd5ced222d5b5cd685bc1) - Consider bitmap alternative only for ANY ScalarArray (Ekta Khanna) [#905](https://github.com/apache/cloudberry/pull/905)
* [`cbc3663dd67`](https://github.com/apache/cloudberry/commit/cbc3663dd675d198cb3ebf2a215a4abc7d695b87) - ORCA: update relcache logic for setting hashable for RANGETYPES (Ekta Khanna) [#905](https://github.com/apache/cloudberry/pull/905)
* [`dbc291a979c`](https://github.com/apache/cloudberry/commit/dbc291a979c19c817b17bffba450c60c0a8eb328) - Allow ORCA plans with empty target list (#15939) (THANATOSLAVA) [#905](https://github.com/apache/cloudberry/pull/905)
* [`4de0bcc11f5`](https://github.com/apache/cloudberry/commit/4de0bcc11f5871214b076c5fed409bf74cbdb132) - Reorder scalar comparison expression in case of cast (#15880) (THANATOSLAVA) [#905](https://github.com/apache/cloudberry/pull/905)
* [`195b614de74`](https://github.com/apache/cloudberry/commit/195b614de740e4e2ebcd334e075a98c07b86ed60) - Fix unlock the pruned partitions of partition table (#15665) (Hari krishna) [#905](https://github.com/apache/cloudberry/pull/905)
* [`a2bf2f3d335`](https://github.com/apache/cloudberry/commit/a2bf2f3d33592b0a0046d7a6fc491bcb7075df4e) - Fixing crash in ORCA with skip-level correlated query (#15648) (Dev Swaroop Chattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`3b045bec099`](https://github.com/apache/cloudberry/commit/3b045bec099ed0851ecebbf44f2c1ec53be3371a) - Fix to remove Asserts on release build (#15845) (Hari krishna) [#905](https://github.com/apache/cloudberry/pull/905)
* [`4501a698468`](https://github.com/apache/cloudberry/commit/4501a6984689b38d01b4f7e7f10ede9589d0f43c) - [ORCA] Update index cost model to account for INCLUDE columns (#15697) (David Kimura) [#905](https://github.com/apache/cloudberry/pull/905)
* [`cff7c6c4e20`](https://github.com/apache/cloudberry/commit/cff7c6c4e2094ce1663c558ef2c2e0aaa229d958) - Enable push join below union all (#15535) (THANATOSLAVA) [#905](https://github.com/apache/cloudberry/pull/905)
* [`8d9cb940c2a`](https://github.com/apache/cloudberry/commit/8d9cb940c2a27298a61dd6b1130cae8f6a8f1f42) - Remove GUC/traceflag related to pruning unused columns (David Kimura) [#905](https://github.com/apache/cloudberry/pull/905)
* [`06fecda2448`](https://github.com/apache/cloudberry/commit/06fecda24488ee81885606c44f3ba90dc35d918a) - [ORCA] Enable Index-Only Scan on CTE (David Kimura) [#905](https://github.com/apache/cloudberry/pull/905)
* [`83ac047343a`](https://github.com/apache/cloudberry/commit/83ac047343ac8fc60ee015a98439741aa7533ad4) - Comment about direct dispatch's indexqualorig changes (Adam Lee) [#905](https://github.com/apache/cloudberry/pull/905)
* [`6992643ee62`](https://github.com/apache/cloudberry/commit/6992643ee625bd0d0ae79d352b2b6bf8ad66f02b) - ORCA should generate plans with the ProjectSet node (#15170) (Dev Swaroop Chattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`cf902688ec7`](https://github.com/apache/cloudberry/commit/cf902688ec70db2155e2cfd68784c9adbc8efd72) - Add workload for explain pipeline (#15730) (THANATOSLAVA) [#905](https://github.com/apache/cloudberry/pull/905)
* [`4484f5f1a43`](https://github.com/apache/cloudberry/commit/4484f5f1a43c0e3a780d6e5176edfa8e9ad96146) - Support DPE for duplicate sensitive random motions in Orca (#15629) (Chris Hajas) [#905](https://github.com/apache/cloudberry/pull/905)
* [`91edb604fd3`](https://github.com/apache/cloudberry/commit/91edb604fd3ca7236b449828ddf7d0a12eef9884) - Fall back to planner for queries with foreign partitions using greenplum_fdw (#15706) (Chris Hajas) [#905](https://github.com/apache/cloudberry/pull/905)
* [`6e9120a1978`](https://github.com/apache/cloudberry/commit/6e9120a19781a31f0f217e230852419d954d9fc4) - Support executing foreign tables from segments in Orca (#15559) (Chris Hajas) [#905](https://github.com/apache/cloudberry/pull/905)
* [`81fee68e97f`](https://github.com/apache/cloudberry/commit/81fee68e97fee5a35f1d1fc5f81809b0ba11a08b) - Fix bug in ORCA of removing required redistribution motion when query uses GROUP BY over gp_segment_id (#15537) (NISHANT SHARMA) [#905](https://github.com/apache/cloudberry/pull/905)
* [`1237a6f320f`](https://github.com/apache/cloudberry/commit/1237a6f320f304a5f31d88d3d59bc56e4ca5d41e) - [ORCA] Support cover indexes using INCLUDE columns (#15463) (David Kimura) [#905](https://github.com/apache/cloudberry/pull/905)
* [`a64a0c27010`](https://github.com/apache/cloudberry/commit/a64a0c270101e1dfe1edac32e7709f42490cad4e) - new operator shouldn't return NULL (Georgy Shelkovy) [#905](https://github.com/apache/cloudberry/pull/905)
* [`b49f333c499`](https://github.com/apache/cloudberry/commit/b49f333c49979f6ac835468342b72deddf429dd4) - Addressing Review Comments (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`1e0c18d8997`](https://github.com/apache/cloudberry/commit/1e0c18d8997ef3e8eb3390db7023f1263c2e9999) - Modifying Test Cases (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`37cfef2e7e8`](https://github.com/apache/cloudberry/commit/37cfef2e7e8e45249971c4ecf5a902c5d83d1a93) - Fixing test cases (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`57bd5184b21`](https://github.com/apache/cloudberry/commit/57bd5184b216a654691cc930e4177430a0d325d4) - Modifying code as per feedback (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`9bc0eac1069`](https://github.com/apache/cloudberry/commit/9bc0eac10694b7417b9f6a1d256e37020162dcba) - Adding inner NLJ cases (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`915d321c9f3`](https://github.com/apache/cloudberry/commit/915d321c9f3fdcaceeeca2af8d63b3c01ab49a17) - Adding Testcases (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`86170a2dbd5`](https://github.com/apache/cloudberry/commit/86170a2dbd582239a96027f2b04a41bd0bc04c44) - Adding motion when join columns in NLJ belong to different Opfamiliy (DevChattopadhyay) [#905](https://github.com/apache/cloudberry/pull/905)
* [`3a2626d5c22`](https://github.com/apache/cloudberry/commit/3a2626d5c22c2075ae9d22dbbba9fbddc77997c9) - Introduce gp_appendonly_compaction_segfile_limit (Soumyadeep Chakraborty) [#903](https://github.com/apache/cloudberry/pull/903)
* [`af8b4af8afd`](https://github.com/apache/cloudberry/commit/af8b4af8afd2f7588c6fd8a59c691c75c87978c0) - Update createidx_while_reindex tests (Brent Doil) [#903](https://github.com/apache/cloudberry/pull/903)
* [`5022854bf9e`](https://github.com/apache/cloudberry/commit/5022854bf9e4dcd29a82a2aa7b0d33736d4daebb) - Update answer file for concurrent_index_creation_should_not_deadlock (Brent Doil) [#903](https://github.com/apache/cloudberry/pull/903)
* [`4bd589dc8f4`](https://github.com/apache/cloudberry/commit/4bd589dc8f461598a06514f786ffe1b59c6a28c1) - Resolve Deadlock: Allow Concurrent Creation of Non-First Index on AO (Ashwin Agrawal) [#903](https://github.com/apache/cloudberry/pull/903)
* [`7a8d28d196a`](https://github.com/apache/cloudberry/commit/7a8d28d196a49212fb0496fb7c7141dcf3344044) - Retire gp_reject_internal_tcp_connection (Soumyadeep Chakraborty) [#903](https://github.com/apache/cloudberry/pull/903)
* [`cdf9eb90fb3`](https://github.com/apache/cloudberry/commit/cdf9eb90fb36074f189014fe50ece3f7ef539596) - Fix token for user id xxx doesn't exist (#17325) (Juyi.lmz) [#903](https://github.com/apache/cloudberry/pull/903)
* [`4964f6468ed`](https://github.com/apache/cloudberry/commit/4964f6468ed6104260e6486aea8b00f85f5a8f0c) - gp_sparse_vector: minor fixes to avoid unexpected issues (Xiaozhong Wang) [#903](https://github.com/apache/cloudberry/pull/903)
* [`32f9a31cd3e`](https://github.com/apache/cloudberry/commit/32f9a31cd3e7cc431a6693b7627a661906a0b095) - gp_sparse_vector: be compatible with the ARM platform (Xing Guo) [#903](https://github.com/apache/cloudberry/pull/903)
* [`8ec795a53ad`](https://github.com/apache/cloudberry/commit/8ec795a53ad1ebc3025cdf21accbe58c5e19e1f8) - Removed the permission check for `cpu.pressure` item when resource manager sets to 'group-v2'. (FairyFar) [#903](https://github.com/apache/cloudberry/pull/903)
* [`0f55f1c804d`](https://github.com/apache/cloudberry/commit/0f55f1c804d0cd3141b82fb196ad6a79c8f78298) - Remove GPDB_96_MERGE_FIXME in portalmem.c (wenru yan) [#903](https://github.com/apache/cloudberry/pull/903)
* [`1527760d7c8`](https://github.com/apache/cloudberry/commit/1527760d7c8eefa47ae8216775a16185705aa557) - Fix inject_fault suspend cannot be canceled (#17279) (chaotian) [#903](https://github.com/apache/cloudberry/pull/903)
* [`7c9a7824ae9`](https://github.com/apache/cloudberry/commit/7c9a7824ae9c9d8d8b90205d4c84afee8055777b) - Add warning and guard code for zero-column table. (Zhenghua Lyu) [#903](https://github.com/apache/cloudberry/pull/903)
* [`4b71649a539`](https://github.com/apache/cloudberry/commit/4b71649a539f52425b4b2aca75d961c1720761d4) - Fix incorrect codes in pg_backup_archiver.c (#899) (reshke) [#899](https://github.com/apache/cloudberry/pull/899)
* [`332a4448754`](https://github.com/apache/cloudberry/commit/332a44487545c9ca88f7ca04335dd3f94e902d2c) - Remove regression diffs (reshke) [#898](https://github.com/apache/cloudberry/pull/898)
* [`db170adb598`](https://github.com/apache/cloudberry/commit/db170adb598d278ba6eb4d6f81e19da03601145b) - pg_upgrade: fix appendonly materialized view check output misalignment (Kevin Yeap) [#898](https://github.com/apache/cloudberry/pull/898)
* [`d82f760eeca`](https://github.com/apache/cloudberry/commit/d82f760eeca5ef17cc7f8c6b7c977ad8ca7c5b2a) - Add pg_upgrade --skip-checks flag (Kalen Krempely) [#898](https://github.com/apache/cloudberry/pull/898)
* [`3616d98ff2d`](https://github.com/apache/cloudberry/commit/3616d98ff2d0a358f8b07de8fbde84293560247e) - Disable pg_upgrade's broken parallel tablespace transfer (Kevin Yeap) [#898](https://github.com/apache/cloudberry/pull/898)
* [`a4caad184f2`](https://github.com/apache/cloudberry/commit/a4caad184f209db35563c089f37bf6a3e83f8faf) - pg_upgrade invokes pg_restore with --binary-upgrade (Adam Lee) [#898](https://github.com/apache/cloudberry/pull/898)
* [`7f9b77ae57a`](https://github.com/apache/cloudberry/commit/7f9b77ae57a469b136ae9a88f0686726d3eee8f1) - Remove pg_upgrade 5X->7X functions (Adam Lee) [#898](https://github.com/apache/cloudberry/pull/898)
* [`cad49f56613`](https://github.com/apache/cloudberry/commit/cad49f56613dda12c3c595688f740aa8a89c0eca) - pg_upgrade: Resolve FIXMEs in pg_upgrade.c and info.c (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`cc10b2ef70f`](https://github.com/apache/cloudberry/commit/cc10b2ef70fcda7d003dfb088fa3d323195e9fef) - pg_dump: Resolve FIXME in dumpTableSchema. (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`2298319b1a5`](https://github.com/apache/cloudberry/commit/2298319b1a5d67db2a79c302f72ffac41c45eb97) - pg_dump: Resolve FIXME in getOwnedSeqs (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`7222699ffad`](https://github.com/apache/cloudberry/commit/7222699ffada7db968c96d2c2b84a6877d80b5d4) - pg_dump: Address FIXME in dumpAttrDef (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`c50c0c85812`](https://github.com/apache/cloudberry/commit/c50c0c8581287886d2a2f11fe4595269ebb02f11) - pg_dump: Add description for GPDB handling of public schema. (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`d2843f488a1`](https://github.com/apache/cloudberry/commit/d2843f488a1a0e52bf02df504c355e19f7c05ef8) - pg_upgrade: Remove flags to add/remove data checksums (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`9fa800d0453`](https://github.com/apache/cloudberry/commit/9fa800d04536560b82c44710b8f30cf43ac00a07) - Remove GPDB4 codepaths for pg_upgrade (Brent Doil) [#898](https://github.com/apache/cloudberry/pull/898)
* [`1bdaafe682f`](https://github.com/apache/cloudberry/commit/1bdaafe682fbd2b842413e5531dceba6c77cc0f5) - pg_upgrade: Ignore TOAST for columnar tables (Ashwin Agrawal) [#898](https://github.com/apache/cloudberry/pull/898)
* [`eef9f9c056b`](https://github.com/apache/cloudberry/commit/eef9f9c056bb910f95f5d796636cc33e366f499e) - pg_upgrade: print check output file location (Kalen Krempely) [#898](https://github.com/apache/cloudberry/pull/898)
* [`80e1e4a7521`](https://github.com/apache/cloudberry/commit/80e1e4a7521cc08a9aaf32e025915d55f3609e58) - pg_upgrade --continue-check-on-fatal exit status 1 on fatal (Kalen Krempely) [#898](https://github.com/apache/cloudberry/pull/898)
* [`8f77bbfa40d`](https://github.com/apache/cloudberry/commit/8f77bbfa40d161d6666bca7b6c78d256e1ff6a67) - fix compiler warning for genfile.c,util.c and nodeShareInputScan.c (wenru yan) [#898](https://github.com/apache/cloudberry/pull/898)
* [`3420d66838a`](https://github.com/apache/cloudberry/commit/3420d66838a4fa62d9aa20969464cbfdded2dda6) - resolve cherry-pick (reshke kirill) [#898](https://github.com/apache/cloudberry/pull/898)
* [`46ed8763264`](https://github.com/apache/cloudberry/commit/46ed8763264fdda24a2ff6cb1b11583fb60a7584) - Adds GPDB Merge notice for pg_upgrade (Gaurab Dey) [#898](https://github.com/apache/cloudberry/pull/898)
* [`fae64f92139`](https://github.com/apache/cloudberry/commit/fae64f92139894af769697763596cc32cfd5638b) - Adds --skip-target-check to skip checks on the new cluster (Gaurab Dey) [#898](https://github.com/apache/cloudberry/pull/898)
* [`3cc166cad14`](https://github.com/apache/cloudberry/commit/3cc166cad14790f586bd3e481acebe9218e9f51e) - Adds --continue-check-on-fatal option for check flag (Gaurab Dey) [#898](https://github.com/apache/cloudberry/pull/898)
* [`fca07d05148`](https://github.com/apache/cloudberry/commit/fca07d05148edd9c13ffae60b936e9140b791ecf) - upgrade: pg_strdup should not be run on the bool params (Bhuvnesh Chaudhary) [#898](https://github.com/apache/cloudberry/pull/898)
* [`44c1e6d3f1f`](https://github.com/apache/cloudberry/commit/44c1e6d3f1f1a1df095d782de9a9680bbf38e32d) - Resolve cherry-pick issues. Bring back toast_tuple_target logic. (reshke) [#884](https://github.com/apache/cloudberry/pull/884)
* [`3199861dfe3`](https://github.com/apache/cloudberry/commit/3199861dfe337d49cc432e936a7752440da1d99d) - Toasting for AO tables should still use custom toast_tuple_target (Huansong Fu) [#884](https://github.com/apache/cloudberry/pull/884)
* [`8fbc6628801`](https://github.com/apache/cloudberry/commit/8fbc66288017e1d0be03d3682a63bdd768c73335) - fix incorrect scan position during bitmap index words scan (#13479) (SmartKeyerror) [#884](https://github.com/apache/cloudberry/pull/884)
* [`04e6ac6e513`](https://github.com/apache/cloudberry/commit/04e6ac6e513a74cc5e940c5dd2fc4e2dee1c8463) - Don't dispatch temp namespace oid to writer gang (#13523) (Sasasu) [#884](https://github.com/apache/cloudberry/pull/884)
* [`b7bce83ed18`](https://github.com/apache/cloudberry/commit/b7bce83ed18ea246e4934a649211d329b9abedd2) - Preserve AO storage options during internal CTAS (Divyesh Vanjare) [#884](https://github.com/apache/cloudberry/pull/884)
* [`e3fc63322fe`](https://github.com/apache/cloudberry/commit/e3fc63322fecfc18dacb878809f5ee2c36f6740e) - gpssh: Retry with TERM env variable set during failures (Nihal Jain) [#896](https://github.com/apache/cloudberry/pull/896)
* [`5f53e448b92`](https://github.com/apache/cloudberry/commit/5f53e448b92ce182b345820bacc4ce27c4c4933f) - Request syncrep for the forget commit in the remote_apply mode (Huansong Fu) [#896](https://github.com/apache/cloudberry/pull/896)
* [`5ed2e6a77c6`](https://github.com/apache/cloudberry/commit/5ed2e6a77c660ad649833018c1df2965c8d133ee) - Optimize MPP FDW LIMIT/OFFSET push down when there is NULL/0. (#17246) (Zhang Mingli) [#896](https://github.com/apache/cloudberry/pull/896)
* [`ed35e21a585`](https://github.com/apache/cloudberry/commit/ed35e21a585a4f8292c792e514bb4d023b88089e) - [7X] Removed redundant tests (Annpurna Shahani) [#894](https://github.com/apache/cloudberry/pull/894)
* [`21e41c2b74a`](https://github.com/apache/cloudberry/commit/21e41c2b74aa9734d89a71f61263476df6366370) - Syscache lookup for pg_attribute_encoding (Huansong Fu) [#894](https://github.com/apache/cloudberry/pull/894)
* [`3045ae556da`](https://github.com/apache/cloudberry/commit/3045ae556da791d0ee906ccae36f1ab75373b1a3) - Fix an obvious memory leak in _bitmap_xlog_insert_bitmapwords(), it caused the memory usage of startup process to continue to grow. (interma) [#894](https://github.com/apache/cloudberry/pull/894)
* [`cf51441ba32`](https://github.com/apache/cloudberry/commit/cf51441ba32d218d24b417c503da5b959c9189da) - Fix crash caused by vacuum ao_aux_only on AO partitioned table. (linxu.hlx) [#894](https://github.com/apache/cloudberry/pull/894)
* [`f35acc7eb6c`](https://github.com/apache/cloudberry/commit/f35acc7eb6c40b66e17db9df3cab2a1e6a7e7932) - Fix issue https://github.com/greenplum-db/gpdb/issues/17333. (Wenlin Zhang) [#894](https://github.com/apache/cloudberry/pull/894)
* [`2207ae98c5e`](https://github.com/apache/cloudberry/commit/2207ae98c5e9abb943520dda305badfc6bc4118e) - Reject substituting extension schemas or owners matching ["$'\]. (Noah Misch) [#894](https://github.com/apache/cloudberry/pull/894)
* [`0c19bf42f7b`](https://github.com/apache/cloudberry/commit/0c19bf42f7bfcffa39fb2cd3ff7c704ce26eb3e7) - Detect integer overflow while computing new array dimensions. (Tom Lane) [#894](https://github.com/apache/cloudberry/pull/894)
* [`0e5ea80b795`](https://github.com/apache/cloudberry/commit/0e5ea80b79550264c317c82cde7f112eb583d6a0) - Add missing volatile qualifier. (#17521) (Xing Guo) [#894](https://github.com/apache/cloudberry/pull/894)
* [`6bdb0f4e250`](https://github.com/apache/cloudberry/commit/6bdb0f4e2504d72db92f4bcd19161afb170237d2) - Allocate DatumHashTable in ANALYZE memory context (Soumyadeep Chakraborty) [#894](https://github.com/apache/cloudberry/pull/894)
* [`05c4df0076c`](https://github.com/apache/cloudberry/commit/05c4df0076c87cc80c33852bac053f0dbecf48a2) - analyze: Don't leak DatumHashTable (Soumyadeep Chakraborty) [#894](https://github.com/apache/cloudberry/pull/894)
* [`76b2ad51148`](https://github.com/apache/cloudberry/commit/76b2ad511484745af282e1903df52e87abdf3576) - Prepare GUC option string only once during gang creation (Huansong Fu) [#894](https://github.com/apache/cloudberry/pull/894)
* [`36ba03bfb67`](https://github.com/apache/cloudberry/commit/36ba03bfb67476ec98f58e999e784c0acd8bce32) - Fix missing discard_output variable in shared scan node's functions (#17453) (Chris Hajas) [#894](https://github.com/apache/cloudberry/pull/894)
* [`59675ae9f4b`](https://github.com/apache/cloudberry/commit/59675ae9f4b778d6082bf32eb0f36026584b78c3) - Expose several functions for extensions (#17380) (QingMa) [#894](https://github.com/apache/cloudberry/pull/894)
* [`e56be3872cf`](https://github.com/apache/cloudberry/commit/e56be3872cfb07e5e460dba6cb9af01e31c31dcd) - Remove a duplicated pfree code from ResolveRecoveryConflictWithVirtualXIDs (Huansong Fu) [#894](https://github.com/apache/cloudberry/pull/894)
* [`e4a8e1ecb7d`](https://github.com/apache/cloudberry/commit/e4a8e1ecb7d80ae10cbda758a29cb65d1725071e) - Report dtx protocol command dispatch errors reliably (Soumyadeep Chakraborty) [#894](https://github.com/apache/cloudberry/pull/894)
* [`0c038c268eb`](https://github.com/apache/cloudberry/commit/0c038c268eb9cea649f07243172805363efc964f) - cdbdisp: Set numResults output variable (Soumyadeep Chakraborty) [#894](https://github.com/apache/cloudberry/pull/894)
* [`fc8aab88de6`](https://github.com/apache/cloudberry/commit/fc8aab88de647933ea60999d8b8cf54d4f622359) - Avoid replay dtx info in checkpoint for newly expanded segments (wuchengwen) [#894](https://github.com/apache/cloudberry/pull/894)
* [`6feeb51a30c`](https://github.com/apache/cloudberry/commit/6feeb51a30c0fa08aa3b54c35cc4a219cb3296c4) - Update cdbvars.c: remove unused variables. (kurtYansen) [#894](https://github.com/apache/cloudberry/pull/894)
* [`7fff6a58b5a`](https://github.com/apache/cloudberry/commit/7fff6a58b5a45b095c144bea432dbcc5c8bc3990) - Disable Orca refcount test on Mac (#17391) (Chris Hajas) [#894](https://github.com/apache/cloudberry/pull/894)
* [`667481c65fa`](https://github.com/apache/cloudberry/commit/667481c65faff0890cdfab6fc1d14d6d23ddf88c) - Add some LOGs for GDD backends. (Zhenghua Lyu) [#894](https://github.com/apache/cloudberry/pull/894)
* [`8cd506cead8`](https://github.com/apache/cloudberry/commit/8cd506cead8bd940aea3a4d738d1cec1cd3b245e) - Always pfree strings returned by GetDatabasePath (Alvaro Herrera) [#894](https://github.com/apache/cloudberry/pull/894)
* [`9b197af19ac`](https://github.com/apache/cloudberry/commit/9b197af19ac5ab7e104a1f826feba28c3a125513) - Fix double declaration for check_ok() in pg_upgrade.h (Peter Eisentraut) [#894](https://github.com/apache/cloudberry/pull/894)
* [`8ca05e6a20f`](https://github.com/apache/cloudberry/commit/8ca05e6a20fe9be1f5ecef4e58c140a34a99f9fa) - Add spaces before pg_ctl args in pg_upgrade code. (Brent Doil) [#894](https://github.com/apache/cloudberry/pull/894)
* [`24e4c886319`](https://github.com/apache/cloudberry/commit/24e4c8863194148469ec1772b4249d957f501959) - Fix query crash with minus memory_limit value in resgroup (#17053) (Wenru Yan) [#894](https://github.com/apache/cloudberry/pull/894)
* [`b73d71d6f66`](https://github.com/apache/cloudberry/commit/b73d71d6f6636c7f261cd2ce0906a76810dc2b3e) - Fix issues in pgarch's new directory-scanning logic. (Tom Lane) [#894](https://github.com/apache/cloudberry/pull/894)
* [`34ba2f37fd0`](https://github.com/apache/cloudberry/commit/34ba2f37fd0d639a2c24c68a5b0e7a5d3e53c77c) - Improve performance of pgarch_readyXlog() with many status files. (Robert Haas) [#894](https://github.com/apache/cloudberry/pull/894)
* [`50f26d471a4`](https://github.com/apache/cloudberry/commit/50f26d471a4c700cbe2cc98929b7d70dd81a41fd) - Fix FTS PROBE process memory leak. (dreamedcheng) [#894](https://github.com/apache/cloudberry/pull/894)
* [`09f70e8589a`](https://github.com/apache/cloudberry/commit/09f70e8589afd7c5a5a33ea9a0d6482934ea252c) - Fix "unrecognized join type" error with LASJ Not-In and network types (Chris Hajas) [#894](https://github.com/apache/cloudberry/pull/894)
* [`9692c28127b`](https://github.com/apache/cloudberry/commit/9692c28127b9d16c9398f0319617618b90936630) - Error out on too many command-line arguments (Peter Eisentraut) [#894](https://github.com/apache/cloudberry/pull/894)
* [`b7aa8ad3030`](https://github.com/apache/cloudberry/commit/b7aa8ad3030e3fe51f2ec55995fcb251698a9bb1) - Add test for VACUUM reltuple distortion (Brent Doil) [#894](https://github.com/apache/cloudberry/pull/894)
* [`1e4c3888a6d`](https://github.com/apache/cloudberry/commit/1e4c3888a6ddaefc929ca1b374408391a0c6d4cd) - Avoid VACUUM reltuples distortion. (Peter Geoghegan) [#894](https://github.com/apache/cloudberry/pull/894)
* [`10dd186c1a4`](https://github.com/apache/cloudberry/commit/10dd186c1a4156e4e87811d3b1816ea4e7995085) - Fix long running execution for bitmap index (Marbin Tan) [#894](https://github.com/apache/cloudberry/pull/894)
* [`c0fcd9924fe`](https://github.com/apache/cloudberry/commit/c0fcd9924fe79830d5e033053572be1e881d49a6) - fix redundant columns of mutlistage-agg plan (#16080) (chaotian) [#894](https://github.com/apache/cloudberry/pull/894)
* [`a8cc2a71ba1`](https://github.com/apache/cloudberry/commit/a8cc2a71ba13e35d1160ef25951fab6c6adb4214) - Change deadlock_timeout GUC to sync (Huansong Fu) [#894](https://github.com/apache/cloudberry/pull/894)
* [`30d1950a93d`](https://github.com/apache/cloudberry/commit/30d1950a93d17a0d8f60fb95ecc7328cf11ef828) - Do not throw error for INSERT into a table w/ foreign key constraint (Huansong Fu) [#894](https://github.com/apache/cloudberry/pull/894)
* [`f1d890e5e71`](https://github.com/apache/cloudberry/commit/f1d890e5e7131b05e861a01357291f98f84ed16b) - Disallow SUBPARTITION BY clause on empty partition (Divyesh Vanjare) [#894](https://github.com/apache/cloudberry/pull/894)
* [`195fbaf6978`](https://github.com/apache/cloudberry/commit/195fbaf6978de5a86a382477bdae8998b51deacc) - Fix comment on 'performDtxProtocolPrepare' (#16846) (Xiaoran Wang) [#894](https://github.com/apache/cloudberry/pull/894)
* [`af17f94c8d2`](https://github.com/apache/cloudberry/commit/af17f94c8d20ea5a82a0dca7ed3755eaa8b6e10c) - fix results wrong while using union for recursive_cte (#16782) (xuejing zhao) [#894](https://github.com/apache/cloudberry/pull/894)
* [`3c8f63c22b9`](https://github.com/apache/cloudberry/commit/3c8f63c22b98c0149dd4d871f42e737c5eba233a) - Don't emit critical error message when gppkg is missing. (#16747) (Xing Guo) [#894](https://github.com/apache/cloudberry/pull/894)
* [`91507514a18`](https://github.com/apache/cloudberry/commit/91507514a185af5ad9d46a14c1a019848e18b57a) - Fix potential use-after-free in error handling. (Jianghua Yang) [#895](https://github.com/apache/cloudberry/pull/895)
* [`ccac620b001`](https://github.com/apache/cloudberry/commit/ccac620b0018fe4b9548378574c0c9156cf9185f) - Add GUC gp_enable_statement_trigger (Marbin Tan) [#890](https://github.com/apache/cloudberry/pull/890)
* [`e3bbfda9a88`](https://github.com/apache/cloudberry/commit/e3bbfda9a88113581e464e4611e354d4ef80015f) - Default to log_checkpoints=on, log_autovacuum_min_duration=10m (Soumyadeep Chakraborty) [#890](https://github.com/apache/cloudberry/pull/890)
* [`120758cf1bd`](https://github.com/apache/cloudberry/commit/120758cf1bdfbe3ad7af64e95d436d9e65b7fa0a) - Add missing volatile qualifier. (#17273) (Xing Guo) [#890](https://github.com/apache/cloudberry/pull/890)
* [`dde8910fc10`](https://github.com/apache/cloudberry/commit/dde8910fc107e7078f72366d3e7521122de0b32a) - Fix fallback in debug build due to scalar with invalid return type (Chris Hajas) [#890](https://github.com/apache/cloudberry/pull/890)
* [`6cc9a78a82e`](https://github.com/apache/cloudberry/commit/6cc9a78a82e7d2d7a67105161b14606438adc49c) - Fix pipeline diff caused by different length of ----- (#17263) (Jingwen Yang) [#890](https://github.com/apache/cloudberry/pull/890)
* [`24bc2526c6d`](https://github.com/apache/cloudberry/commit/24bc2526c6d12c350e844db6886e4784bf8fb5d5) - Add a test case about special location URI for CREATE FOREIGN TABLE syntax (#17252) (Jingwen Yang) [#890](https://github.com/apache/cloudberry/pull/890)
* [`96ff7a76447`](https://github.com/apache/cloudberry/commit/96ff7a764475935e821ca9c73fa4f7f497467a0e) - Fix relptr's encoding of the base address. (#17255) (Zhang Hao) [#890](https://github.com/apache/cloudberry/pull/890)
* [`a368828c5ae`](https://github.com/apache/cloudberry/commit/a368828c5ae03679f120b8c81ee2bf7e434d3323) - Update comment (Marbin Tan) [#890](https://github.com/apache/cloudberry/pull/890)
* [`dc73f6ece04`](https://github.com/apache/cloudberry/commit/dc73f6ece049fd40d9af2618d19200eaf0347ced) - Consider MPP FDW LIMIT pushdown when both offset and limit clause are specified (#16919) (Jingwen Yang) [#890](https://github.com/apache/cloudberry/pull/890)
* [`76a08f8fca0`](https://github.com/apache/cloudberry/commit/76a08f8fca07c625b869aa5870fb628156238d45) - Remove a fixme of plpgsql tests (Adam Lee) [#890](https://github.com/apache/cloudberry/pull/890)
* [`f3752542bce`](https://github.com/apache/cloudberry/commit/f3752542bceb8fda891aa779332b452dc36df6b4) - Fix cherry-pick issues. (reshke) [#886](https://github.com/apache/cloudberry/pull/886)
* [`386b74fb7d3`](https://github.com/apache/cloudberry/commit/****************************************) - ALTER TABLE SET ACCESS METHOD: AOCO->Heap support (Huansong Fu) [#886](https://github.com/apache/cloudberry/pull/886)
* [`14fcd72d128`](https://github.com/apache/cloudberry/commit/14fcd72d128e59aa3f84242e2db5de06658b1370) - ALTER TABLE SET ACCESS METHOD: Heap->AOCO support (Divyesh Vanjare) [#886](https://github.com/apache/cloudberry/pull/886)
* [`374d05728a0`](https://github.com/apache/cloudberry/commit/374d05728a00a447af9099bbfc710e86008f8ea8) - Fix an issue with rle_type when changing table from AO to AOCO (Huansong Fu) [#886](https://github.com/apache/cloudberry/pull/886)
* [`182af31fc62`](https://github.com/apache/cloudberry/commit/****************************************) - ALTER TABLE SET ACCESS METHOD: AOCO->AO support (Huansong Fu) [#886](https://github.com/apache/cloudberry/pull/886)
* [`b8f3cdea6d4`](https://github.com/apache/cloudberry/commit/b8f3cdea6d47047d65e3a80d5fe54f4eaf5e6e60) - Remove existing reloptions when AM is changed (Huansong Fu) [#886](https://github.com/apache/cloudberry/pull/886)
* [`88552c820ae`](https://github.com/apache/cloudberry/commit/88552c820aef8e18dca328fd6b147b0765bc5983) - Fix populate_rel_col_encodings (reshke) [#886](https://github.com/apache/cloudberry/pull/886)
* [`dd4d96d3d4a`](https://github.com/apache/cloudberry/commit/dd4d96d3d4a54e85aa53a595679cb9df873f5d2e) - ALTER TABLE SET ACCESS METHOD: AO->AOCO support (Huansong Fu) [#886](https://github.com/apache/cloudberry/pull/886)
* [`44a249a8cf8`](https://github.com/apache/cloudberry/commit/44a249a8cf8f342837169ed87691100088f52f41) - Fix: cherry-pick "Dynamic Partition Elimination, retrieving relation columns, update tests, intermedia agg" (zhoujiaqi) [#881](https://github.com/apache/cloudberry/pull/881)
* [`62b695550f5`](https://github.com/apache/cloudberry/commit/62b695550f5565bba4998a739035f417ec44c764) - Adapting aggsplit in multi stage agg (zhoujiaqi) [#881](https://github.com/apache/cloudberry/pull/881)
* [`7bc6fd48e67`](https://github.com/apache/cloudberry/commit/7bc6fd48e67478c7084e19c866a267b68782a67a) - Fix wrong number of maxAttrNum in TupleSplitState (#14927) (Haotian Chen) [#881](https://github.com/apache/cloudberry/pull/881)
* [`2bbaddcdb27`](https://github.com/apache/cloudberry/commit/2bbaddcdb2795356e33a7011e7b061aa97ac146c) - Fix bug of wrong idx position in targetlist as ExecTupleSplit (#14954) (Haotian Chen) [#881](https://github.com/apache/cloudberry/pull/881)
* [`609d876a5b8`](https://github.com/apache/cloudberry/commit/609d876a5b843097d0fb97f1b0731f74dfca1a43) - Fix incorrect plan / output in multi stage agg (Jingyu Wang) [#881](https://github.com/apache/cloudberry/pull/881)
* [`87b3b329997`](https://github.com/apache/cloudberry/commit/87b3b32999774fd53f264955a89943e34dc8e278) - Fix crash of AggNode in executor casued by ORCA plan (#14577) (Haotian Chen) [#881](https://github.com/apache/cloudberry/pull/881)
* [`60179b110bb`](https://github.com/apache/cloudberry/commit/60179b110bb8a46c7ae01e1b204c4159c47e8173) - Support intermediate aggs in Orca plans (#13707) (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`288b9d6da00`](https://github.com/apache/cloudberry/commit/288b9d6da009d44ab938bb937fd6fa3b9ddafdf6) - Fix ORCA build break (#15548) (David Kimura) [#881](https://github.com/apache/cloudberry/pull/881)
* [`3fb13c7cc6a`](https://github.com/apache/cloudberry/commit/3fb13c7cc6a6fc72366472011e78190d9183ee2c) - [ORCA] Fix option to enable multi-distinct agg  (#15445) (David Kimura) [#881](https://github.com/apache/cloudberry/pull/881)
* [`2bfac78e2c9`](https://github.com/apache/cloudberry/commit/2bfac78e2c9f5991c6e007ff5f0e3771c6907e7c) - Marking the "PexprConvert2In" preprocessing step as "unsupported for now" (DevChattopadhyay) [#881](https://github.com/apache/cloudberry/pull/881)
* [`80f4b8e2687`](https://github.com/apache/cloudberry/commit/80f4b8e26879e1375661bccacd0fd8507fc31a11) - Fix various memory leaks in Orca (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`3f2ddb58021`](https://github.com/apache/cloudberry/commit/3f2ddb58021ac2f70bfef3668faec5ec0c568359) - Remove remaining parts of unused function data access code from Orca (#15519) (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`811d64e6591`](https://github.com/apache/cloudberry/commit/811d64e6591aa5dc9106e52437452f7fac2af945) - Re-support Dynamic Partition Elimination with semi joins in Orca (#15506) (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`6a4ea524dc9`](https://github.com/apache/cloudberry/commit/6a4ea524dc9fa77ca93057b237250ab920353d20) - Avoid multistage plans when data distribution is universal or replicated (#15235) (Hari krishna) [#881](https://github.com/apache/cloudberry/pull/881)
* [`effe9045a04`](https://github.com/apache/cloudberry/commit/effe9045a040251c32c27a4b98bf0361a2c63d6a) - Optimize retrieving relation columns in Orca (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`afdb7d8e818`](https://github.com/apache/cloudberry/commit/afdb7d8e818d3824185902f93cdf5576231eee2e) - Optimize Orca object serialization to be lazy (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`6602921d38f`](https://github.com/apache/cloudberry/commit/6602921d38f83401cbc81ee89ed4d317e2e02a0a) - Optimize ConstructRootColMappingPerPart for common case (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`fa2bde2521e`](https://github.com/apache/cloudberry/commit/fa2bde2521e70d14f0d8b8d27f43e3cf2d1601cd) - Update test description (Ekta Khanna) [#881](https://github.com/apache/cloudberry/pull/881)
* [`33b9cdd6878`](https://github.com/apache/cloudberry/commit/33b9cdd6878fa43aedca9ce33a149700dd890d74) - Update MDP tests and add test to ICG (Ekta Khanna) [#881](https://github.com/apache/cloudberry/pull/881)
* [`228d19ac77d`](https://github.com/apache/cloudberry/commit/228d19ac77d33b010793d594cc12209e7620dc66) - Update Array Coerce Cast Metadata object (Ekta Khanna) [#881](https://github.com/apache/cloudberry/pull/881)
* [`08c1ee0d07d`](https://github.com/apache/cloudberry/commit/08c1ee0d07df01a56a51f4348d1b994a2c561450) - Update CDXLScalarArrayCoerceExpr to pass elemexpr (Ekta Khanna) [#881](https://github.com/apache/cloudberry/pull/881)
* [`ebccd1f8806`](https://github.com/apache/cloudberry/commit/ebccd1f8806d6bedd6e4f127505b1e62f4a8789f) - [ORCA] Support boolean static partition pruning  (#15348) (David Kimura) [#881](https://github.com/apache/cloudberry/pull/881)
* [`1faf6252c46`](https://github.com/apache/cloudberry/commit/1faf6252c46e60d5e0a098523058134d94995b8a) - Fix missing redistribute for CTAS/insert into randomly distributed table using Orca (#15295) (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`d51cc2d77cc`](https://github.com/apache/cloudberry/commit/d51cc2d77cc8121864bc944e99eeef995a97ab21) - Check partition key opfamily in partition pruning (#15260) (THANATOSLAVA) [#881](https://github.com/apache/cloudberry/pull/881)
* [`71bf73bee50`](https://github.com/apache/cloudberry/commit/71bf73bee505d2518650b61142b67fc613254c35) - Allow queries with valid function variadic flag (nishant sharma) [#881](https://github.com/apache/cloudberry/pull/881)
* [`4eada4ed348`](https://github.com/apache/cloudberry/commit/4eada4ed348b7a4d2f06a7f2dfe36813bc68f8e7) - Implement Left Join Pruning ---------------------------- (DevChattopadhyay) [#881](https://github.com/apache/cloudberry/pull/881)
* [`6297842c5fd`](https://github.com/apache/cloudberry/commit/6297842c5fdc2a1526e57d01f1105dd1070e61eb) - Don't penalize broadcast under LASJ (not in) in Orca (#15240) (Chris Hajas) [#881](https://github.com/apache/cloudberry/pull/881)
* [`40761e71af3`](https://github.com/apache/cloudberry/commit/40761e71af3b977ef7f405372a6ad848536edf7e) - [ORCA] Alternative approach for comparison expression reordering (#15242) (David Kimura) [#881](https://github.com/apache/cloudberry/pull/881)
* [`1f4d412f546`](https://github.com/apache/cloudberry/commit/1f4d412f54620110cbb51f8df6b615bbbb2a21e4) - Fix build error -- unused var (#15255) (Chen Mulong) [#881](https://github.com/apache/cloudberry/pull/881)
* [`da16ad1f20f`](https://github.com/apache/cloudberry/commit/da16ad1f20fbba3e4d6433d82302e461233dd272) - Add Dynamic Partition Elimination for Hash Right Joins (nishant sharma) [#881](https://github.com/apache/cloudberry/pull/881)
* [`d440c1765e7`](https://github.com/apache/cloudberry/commit/d440c1765e735fb1387b5e86ce6235aab157addf) - Set fsync on because it is the value before the test (Jianghua Yang) [#887](https://github.com/apache/cloudberry/pull/887)
* [`f3bf555aa2b`](https://github.com/apache/cloudberry/commit/f3bf555aa2b3f6bb625d57704a8b578f88c8bca7) - Fix race condition in CTE reader-writer communication (#16431) (Lei (Alexandra) Wang) [#887](https://github.com/apache/cloudberry/pull/887)
* [`f07a42d590a`](https://github.com/apache/cloudberry/commit/f07a42d590a6878140ac3436e29fa4705ccb6550) - Reduce the number of pallocs when building partition bounds (David Rowley) [#887](https://github.com/apache/cloudberry/pull/887)
* [`09ab7a970fe`](https://github.com/apache/cloudberry/commit/09ab7a970feedbb6c1c4572f6e04a25ab75491d3) - Resolve FIXME in create_ctescan_path()  in pathnode.c. (#16749) (Aegeaner) [#887](https://github.com/apache/cloudberry/pull/887)
* [`cd74523c645`](https://github.com/apache/cloudberry/commit/cd74523c645be5205c748c0c4d6f8d97c067ab33) - Have ExecFindPartition cache the last found partition (David Rowley) [#887](https://github.com/apache/cloudberry/pull/887)
* [`f161c2d5017`](https://github.com/apache/cloudberry/commit/f161c2d5017135cf5056b02af0d788725deaa4b2) - Printing const-folder expression in ruleutils.c (#16839) (chaotian) [#887](https://github.com/apache/cloudberry/pull/887)
* [`8d351ab99fe`](https://github.com/apache/cloudberry/commit/8d351ab99feb1dfb0c432e22c40517e1758659f5) - fix alter table alter column type reuse index error (HelloYJohn) [#887](https://github.com/apache/cloudberry/pull/887)
* [`e314319e035`](https://github.com/apache/cloudberry/commit/e314319e03590bad11a0ba9e6eb7da2835bb1ef8) - Add volatile qualifier missed in ao segfiles (#16831) (dh-cloud) [#887](https://github.com/apache/cloudberry/pull/887)
* [`eda68cfd80f`](https://github.com/apache/cloudberry/commit/eda68cfd80f92e4e80b2a7c215bb303c89387d79) - Fix deadlock between coordinator and segments. (dreamedcheng) [#887](https://github.com/apache/cloudberry/pull/887)
* [`7e67d36ae94`](https://github.com/apache/cloudberry/commit/7e67d36ae94672fd75c9f7c325766ec378802b9a) - Adjust subquery's locus for recurse_set_operations (wenru yan) [#887](https://github.com/apache/cloudberry/pull/887)
* [`dd24804dd92`](https://github.com/apache/cloudberry/commit/dd24804dd9208323cd00fc0051c0daa4b0dd10f7) - anytable: caller need to release the memory. add a comment. (#16808) (Sasasu) [#887](https://github.com/apache/cloudberry/pull/887)
* [`94b21ea7e28`](https://github.com/apache/cloudberry/commit/94b21ea7e28e48ebc8eae272e6d2d7dfba5abeec) - Disable SET DISTRIBUTED REPLICATED for ALTER EXTERNAL TABLE (#16725) (Jingwen Yang) [#887](https://github.com/apache/cloudberry/pull/887)
* [`667bac6f1bb`](https://github.com/apache/cloudberry/commit/667bac6f1bb0c641a10a2833aa994f2ee13e835f) - Fix coordinator crash in MPPnoticeReceiver (#15664) (xuejing zhao) [#887](https://github.com/apache/cloudberry/pull/887)
* [`d97072dadf4`](https://github.com/apache/cloudberry/commit/d97072dadf424e6dd67694569b82cc4c38d6f57c) - Added support to write config files only (#14915) (Rakesh Sharma) [#887](https://github.com/apache/cloudberry/pull/887)
* [`6e26590c913`](https://github.com/apache/cloudberry/commit/6e26590c9131337874db6e7162146b33300c8b50) - Fix memory accounting bug when move MemoryContext under another accounting node (caspian) [#887](https://github.com/apache/cloudberry/pull/887)
* [`88b61dcd82f`](https://github.com/apache/cloudberry/commit/88b61dcd82f4d24e23b8c93417a59390fb6a776b) - Fix a document bug. (Laowu Wong) [#887](https://github.com/apache/cloudberry/pull/887)
* [`4236e0aadc5`](https://github.com/apache/cloudberry/commit/4236e0aadc562995b3f73262668794efa11d6d06) - [7x]: Fix  gpstart issues for large  segment host (#16661) (Praveen Kumar) [#887](https://github.com/apache/cloudberry/pull/887)
* [`03a2400654a`](https://github.com/apache/cloudberry/commit/03a2400654a8510a382ff39ba239cc541d54f31b) - Fix some typos (#16718) (Yongtao Huang) [#887](https://github.com/apache/cloudberry/pull/887)
* [`f008fa91827`](https://github.com/apache/cloudberry/commit/f008fa91827940411d21c2a9e7f3a4a61f500e22) - Handle parallel retrieve cursor errors via timeout mechanism #15143 (#15203) (1mmortal) [#887](https://github.com/apache/cloudberry/pull/887)
* [`863b66b8a8a`](https://github.com/apache/cloudberry/commit/863b66b8a8a701c4fab788e5afcfad694cf6d239) - fix gpload insert mode not included in transaction (#16147) (xiaoxiao) [#887](https://github.com/apache/cloudberry/pull/887)
* [`336b6bc4fb3`](https://github.com/apache/cloudberry/commit/336b6bc4fb33e01841b7bd10de68850c30e4b9f1) - Fix cherry-pick. (Zhang Mingli) [#888](https://github.com/apache/cloudberry/pull/888)
* [`b0e172bf358`](https://github.com/apache/cloudberry/commit/b0e172bf358bcb513f37168df419967df1dd1600) - Remove a fixme of pg_upgrade makefile (Adam Lee) [#888](https://github.com/apache/cloudberry/pull/888)
* [`dd4ad91ed0c`](https://github.com/apache/cloudberry/commit/dd4ad91ed0c0affae5938a18badeb495620c2781) - Fix issue: external table location uri including char ' (' will cause errors  (#17199)|water32) [#888](https://github.com/apache/cloudberry/pull/888)
* [`5373babee3f`](https://github.com/apache/cloudberry/commit/5373babee3f7812a2d76253239697fabc76747fe) - Show more information about logerrors in pg_exttable view. (#17182) (Jingwen Yang) [#888](https://github.com/apache/cloudberry/pull/888)
* [`5554011c18c`](https://github.com/apache/cloudberry/commit/5554011c18ccd613d1398f46a2917331ef600952) - Check orphaned files functions exclude idle sessions when doing safety check (wuchengwen) [#888](https://github.com/apache/cloudberry/pull/888)
* [`c0230d50b48`](https://github.com/apache/cloudberry/commit/c0230d50b48174a2c06e7723277832ec3f09a2de) - Fix gpsd test flakes (Chris Hajas) [#888](https://github.com/apache/cloudberry/pull/888)
* [`91f0710c216`](https://github.com/apache/cloudberry/commit/91f0710c21637bdb429c3d09197fa5d60546180d) - Fix incorrect behavior of gp_toolkit.gp_move_orphaned_files (wuchengwen) [#888](https://github.com/apache/cloudberry/pull/888)
* [`7a7788951d3`](https://github.com/apache/cloudberry/commit/7a7788951d3f1339f1aff5d77ebe0ddd363a7fc4) - remove fixme in nodeLockRows.c (wenru yan) [#888](https://github.com/apache/cloudberry/pull/888)
* [`6e8ccc533ba`](https://github.com/apache/cloudberry/commit/6e8ccc533ba93a47a4324534412bb33fdbf01f86) - remove function plan_list_is_oneoff (wenru yan) [#888](https://github.com/apache/cloudberry/pull/888)
* [`1d38c43e50c`](https://github.com/apache/cloudberry/commit/1d38c43e50cebb45692c0a62324cda017256e137) - Lift NOT_SUPPORTED notice for pg_start (stop_backup|Soumyadeep Chakraborty) [#888](https://github.com/apache/cloudberry/pull/888)
* [`f87b1bf6fde`](https://github.com/apache/cloudberry/commit/f87b1bf6fde6c7e5d652840d8c43283f1c92cabb) - Fix small overestimation of base64 encoding output length. (#17155) (Wenkang Zhang) [#888](https://github.com/apache/cloudberry/pull/888)
* [`3eacbdfd721`](https://github.com/apache/cloudberry/commit/3eacbdfd721750ac646e65171f1f52effb3ff178) - Fix GPDB_12_MERGE_FIXME in nodeShareInputScan (#17138) (xuejing zhao) [#888](https://github.com/apache/cloudberry/pull/888)
* [`43b9ba9a1c8`](https://github.com/apache/cloudberry/commit/43b9ba9a1c8f6da22603a6f0c86127f178e8cd92) - Remove GPDB_12_MERGE_FIXME in ic_tcp.c (#17154) (xuejing zhao) [#888](https://github.com/apache/cloudberry/pull/888)
* [`bfed8f69627`](https://github.com/apache/cloudberry/commit/bfed8f69627f7f76707c15cc9405a8282939869f) - eliminate unneeded comparison. (Denis Kovalev) [#888](https://github.com/apache/cloudberry/pull/888)
* [`d9b8da56d34`](https://github.com/apache/cloudberry/commit/d9b8da56d34ee40f6cfc1667c3885b118e38668b) - Reintroduce guc gp_max_partition_level (#17122) (xuejing zhao) [#888](https://github.com/apache/cloudberry/pull/888)
* [`d2863e47e7d`](https://github.com/apache/cloudberry/commit/d2863e47e7d2aec3b092cbf2d56fa354a34a55a1) - Fix gp_toolkit.__gp_aocsseg_history crash on non-aocs tables. (Zhang Mingli) [#888](https://github.com/apache/cloudberry/pull/888)
* [`a9a49325fa1`](https://github.com/apache/cloudberry/commit/a9a49325fa1b571c70762dd7338aa4cec2ddd2e2) - fix cherry-pick (Zhang Mingli) [#883](https://github.com/apache/cloudberry/pull/883)
* [`0d6e19fbc25`](https://github.com/apache/cloudberry/commit/0d6e19fbc2531d53551b9557697a61e1a00b2a3a) - Add ignore rule for "terminating connection" log (Soumyadeep Chakraborty) [#883](https://github.com/apache/cloudberry/pull/883)
* [`fe6c6d736b2`](https://github.com/apache/cloudberry/commit/fe6c6d736b26c4e598cd93cf6492f28b5ea7e34e) - Remove obsolete check in SIGTERM handler for the startup process. (Nathan Bossart) [#883](https://github.com/apache/cloudberry/pull/883)
* [`806c666408f`](https://github.com/apache/cloudberry/commit/806c666408f8edc65518da1a385a19e42d5696b4) - Check that MyProcPid == getpid() in backend signal handlers. (Nathan Bossart) [#883](https://github.com/apache/cloudberry/pull/883)
* [`11081666873`](https://github.com/apache/cloudberry/commit/11081666873ec27f2e70a2836b078e031823ffd2) - Avoid calling proc_exit() in processes forked by system(). (Nathan Bossart) [#883](https://github.com/apache/cloudberry/pull/883)
* [`e53194da5b5`](https://github.com/apache/cloudberry/commit/e53194da5b5a0b0e8c0df7fc82d055afb4653394) - Provide sigaction() for Windows. (Thomas Munro) [#883](https://github.com/apache/cloudberry/pull/883)
* [`7a976ec1d1a`](https://github.com/apache/cloudberry/commit/7a976ec1d1a3c58b6bf0a94f4bcd3c1e726270c6) - Emulate sigprocmask(), not sigsetmask(), on Windows. (Thomas Munro) [#883](https://github.com/apache/cloudberry/pull/883)
* [`764855abc39`](https://github.com/apache/cloudberry/commit/764855abc399abf5516318e7c7c299dbf1795db6) - Remove unused buf[MAXPGPATH] variable in mdcreate_ao() (Haolin Wang) [#883](https://github.com/apache/cloudberry/pull/883)
* [`eea5a1f150c`](https://github.com/apache/cloudberry/commit/eea5a1f150cf934e819112f07d7c280b8212df84) - Include distributed xid in transaction commit WAL in all cases (Huansong Fu) [#883](https://github.com/apache/cloudberry/pull/883)
* [`36974709ed2`](https://github.com/apache/cloudberry/commit/36974709ed2a36de946f06d09a332e83f04d7f20) - Resource queue white-box test for multiple portals (Soumyadeep Chakraborty) [#883](https://github.com/apache/cloudberry/pull/883)
* [`e24a2ae2084`](https://github.com/apache/cloudberry/commit/e24a2ae2084ca8d7f1a2366a9b2e80274d43d600) - Dump more info of releasing resource queue lock info (Yao Wang) [#883](https://github.com/apache/cloudberry/pull/883)
* [`18fdf04a3fa`](https://github.com/apache/cloudberry/commit/18fdf04a3fa5c0b16f2d99f79f945e22b489ad4c) - Fix typo of statistics under gporca. (Zhang Mingli) [#883](https://github.com/apache/cloudberry/pull/883)
* [`9ed83765e46`](https://github.com/apache/cloudberry/commit/9ed83765e46664e8f2182b706e1438dc90d47fda) - Remove unsupported_distribution_key_data_types check (Brent Doil) [#883](https://github.com/apache/cloudberry/pull/883)
* [`b904e17590a`](https://github.com/apache/cloudberry/commit/b904e17590ad02ac31fa8f32b38dcdcbd43fd5cc) - Delay setting the current user until the CREATE SCHEMA is dispatched (#16989) (Adam Lee) [#883](https://github.com/apache/cloudberry/pull/883)
* [`9096155becc`](https://github.com/apache/cloudberry/commit/9096155becc8c7724f82a55e64036c886caca2a2) - Revert 943811258c3 (Soumyadeep Chakraborty) [#883](https://github.com/apache/cloudberry/pull/883)
* [`7f959049e48`](https://github.com/apache/cloudberry/commit/7f959049e4815e9f616ae65808cb9f2084131fa0) - Fix bug: PLPY function causes master process reset. (#16856) (Zhang Hao) [#883](https://github.com/apache/cloudberry/pull/883)
* [`e14e869ebcc`](https://github.com/apache/cloudberry/commit/e14e869ebcc2de4aca427da24a2da518dd24b87c) - Call the right function to get the coordinator data dir (#16904) (Matt Song) [#883](https://github.com/apache/cloudberry/pull/883)
* [`53b8f35b26a`](https://github.com/apache/cloudberry/commit/53b8f35b26a0186ff3a22ea20ccc9e4b4e50261a) - fix compile warning (#16916) (Zhenglong Li) [#883](https://github.com/apache/cloudberry/pull/883)
* [`f635b16c6db`](https://github.com/apache/cloudberry/commit/f635b16c6db49b7f93fa9cc0dfcae67caf23fb88) - Updated help doc in consistent with code (#16896) (Rakesh Sharma) [#883](https://github.com/apache/cloudberry/pull/883)
* [`36d4551e322`](https://github.com/apache/cloudberry/commit/36d4551e3227d4497dec9abc4ffdb207e7b1ec55) - introduce the variable of allow_append_initplan_for_function_scan (#16803) (Zhenglong Li) [#883](https://github.com/apache/cloudberry/pull/883)
* [`9e40ae0d354`](https://github.com/apache/cloudberry/commit/9e40ae0d354877f034c15d881023a4b7396abbd1) - Fix AO table fsync memory leak. (dreamedcheng) [#883](https://github.com/apache/cloudberry/pull/883)
* [`ddf286fcc02`](https://github.com/apache/cloudberry/commit/ddf286fcc02d3823910796089328057163fe5886) - Add src/test/regress init_file to pg_buffercache tests (Brent Doil) [#883](https://github.com/apache/cloudberry/pull/883)
* [`2784862d9ea`](https://github.com/apache/cloudberry/commit/2784862d9ea39d00d5e4a05a2612f3e4af74123b) - [Cleanup] Fixup typo in comments (David Kimura) [#883](https://github.com/apache/cloudberry/pull/883)
* [`228556e2455`](https://github.com/apache/cloudberry/commit/228556e245569d3ccdd181edfb51e1f7a68f6b6b) - Enable MPP support for pg_buffercache and build by default (Brent Doil) [#883](https://github.com/apache/cloudberry/pull/883)
* [`b8701d2071d`](https://github.com/apache/cloudberry/commit/b8701d2071dd350e3467ec6a77a308845fbf21b2) - pg_buffercache: Add pg_buffercache_summary() (Andres Freund) [#883](https://github.com/apache/cloudberry/pull/883)
* [`59487bdfa4b`](https://github.com/apache/cloudberry/commit/59487bdfa4bc59a5a07811101e7d6324813170d2) - Add regression test coverage for contrib/pg_buffercache. (Tom Lane) [#883](https://github.com/apache/cloudberry/pull/883)
* [`bae29f37ccc`](https://github.com/apache/cloudberry/commit/bae29f37ccc64df2b988432934a6cdb7e0435195) - Fix gpdb can‘t drop/alter gp_exttable_fdw foreign table with wrong options (#14951) (Jingwen Yang) [#885](https://github.com/apache/cloudberry/pull/885)
* [`b9af9e6274d`](https://github.com/apache/cloudberry/commit/b9af9e6274d25a6f5b6499a94d21a692eaa84d38) - Remove quote_identifier in function list_join() (#16754) (Yongtao Huang) [#885](https://github.com/apache/cloudberry/pull/885)
* [`636e82d4199`](https://github.com/apache/cloudberry/commit/636e82d41990a586041060efccad32e573626dfc) - Fix typos clause. (Zhang Mingli) [#885](https://github.com/apache/cloudberry/pull/885)
* [`88a1b5a3051`](https://github.com/apache/cloudberry/commit/88a1b5a30516ddb9d63ee726a405bc5b7c8fc30c) - Remove duplicated function definitions in builtins.h (Zhang Mingli) [#885](https://github.com/apache/cloudberry/pull/885)
* [`a96a9df0369`](https://github.com/apache/cloudberry/commit/a96a9df03699a283f0285496c28f4545d5316491) - keep catalog inconsistency of relhassubclass after analyze (main branch) (#14978) (hyongtao-db) [#882](https://github.com/apache/cloudberry/pull/882)
* [`f0976cecf3c`](https://github.com/apache/cloudberry/commit/f0976cecf3c142d0bfadfa4b99f911f581eba758) - Fix KeepLogSeg() unittest (Asim R P) [#882](https://github.com/apache/cloudberry/pull/882)
* [`6039d4ed4ff`](https://github.com/apache/cloudberry/commit/6039d4ed4ff8b199318e8ad286b7e157ca60b27c) - Confine Greenplum-specific WAL replication behavior to coordinator (Asim R P) [#882](https://github.com/apache/cloudberry/pull/882)
* [`3062fe19c83`](https://github.com/apache/cloudberry/commit/3062fe19c83f8d4b5cb949e326ef21638354586c) - Ignore invalidated slots while computing oldest catalog Xmin (Alvaro Herrera) [#882](https://github.com/apache/cloudberry/pull/882)
* [`8b06767b2b7`](https://github.com/apache/cloudberry/commit/8b06767b2b75e50f5f862c69db47e45b4d7247d4) - Add help information for gpfdist(#16595) (HouLei) [#882](https://github.com/apache/cloudberry/pull/882)
* [`ffd5c6f7a72`](https://github.com/apache/cloudberry/commit/ffd5c6f7a72df09e06e6cbf8767cbb7a419d75de) - Fix flaky test gp_tablespace_with_faults (Huansong Fu) [#882](https://github.com/apache/cloudberry/pull/882)
* [`72e22ec2e41`](https://github.com/apache/cloudberry/commit/72e22ec2e41c1a5a08cc19c37077940eec179f2e) - fix GPDB_96_MERGE_FIXME in create_ctescan_path (#16649) (xuejing zhao) [#882](https://github.com/apache/cloudberry/pull/882)
* [`01876c9d18b`](https://github.com/apache/cloudberry/commit/01876c9d18ba263544d2f54c56dd9fab2bf2874e) - Fix orphaned temp table on coordinator (wuchengwen) [#882](https://github.com/apache/cloudberry/pull/882)
* [`d23be181711`](https://github.com/apache/cloudberry/commit/d23be181711dbbb9501c79ae5b0d822622d45008) - Fix the import of ICU collations in pg_import_system_collations() (#15673) (Tao Tang) [#882](https://github.com/apache/cloudberry/pull/882)
* [`34c45dd9f29`](https://github.com/apache/cloudberry/commit/34c45dd9f293ca728925a6d758c726e081e49930) - add duration if query is canceled (#16557) (Zhenglong Li) [#882](https://github.com/apache/cloudberry/pull/882)
* [`546d447ad2c`](https://github.com/apache/cloudberry/commit/546d447ad2cd0cc6de2ecba091a258399e7bddc3) - Fix ERROR: "Cannot add cell to table content: total cell count of XXX exceeded." (#16388) (Hongxu Ma) [#882](https://github.com/apache/cloudberry/pull/882)
* [`e3953aa9d6f`](https://github.com/apache/cloudberry/commit/e3953aa9d6f334a12b96294b0de51d522c5da667) - resolve GPDB_96_MERGE_FIXME for SplitUpdate (#16560) (xuejing zhao) [#882](https://github.com/apache/cloudberry/pull/882)
* [`d8f2bcb6032`](https://github.com/apache/cloudberry/commit/d8f2bcb603201e663fd3571eb9ea1a278dea4467) - Fix a flakiness with test gp_check_files (Huansong Fu) [#882](https://github.com/apache/cloudberry/pull/882)
* [`5adbaa65073`](https://github.com/apache/cloudberry/commit/5adbaa65073511925e191d8d3da83d212f4f4e5f) - [7X]Fix utilities do not honor -d flag when COORDINATOR_DATA_DIRECTORY is not set. (#16433) (Rakesh Sharma) [#882](https://github.com/apache/cloudberry/pull/882)
* [`63e48e661e0`](https://github.com/apache/cloudberry/commit/63e48e661e0544de41b69d4a29fd50cc63c5b638) - gpexpand: Fix error when database has tablespaces (Nihal Jain) [#882](https://github.com/apache/cloudberry/pull/882)
* [`40634555449`](https://github.com/apache/cloudberry/commit/4063455544993de87726a90253acc8c88a704777) - Raise a timeout to 180s, in test 003_recovery_targets.pl. (Noah Misch) [#882](https://github.com/apache/cloudberry/pull/882)
* [`621015af442`](https://github.com/apache/cloudberry/commit/621015af4422c09822c93956d1ab987384623f90) - Fix orphaned temp namespace catalog entry left on coordinator (wuchengwen) [#882](https://github.com/apache/cloudberry/pull/882)
* [`5168873c3c6`](https://github.com/apache/cloudberry/commit/5168873c3c698ccd619d857f04095008f2e4f0b2) - Fix icw test cases generted from cherry-pick DynamicForeignscan (zhoujiaqi) [#877](https://github.com/apache/cloudberry/pull/877)
* [`8d4b9d3ff57`](https://github.com/apache/cloudberry/commit/8d4b9d3ff576753763f1f3690d07551e68c1c121) - Fix: cherry-pick DynamicForeignscan (zhoujiaqi) [#877](https://github.com/apache/cloudberry/pull/877)
* [`142353684b2`](https://github.com/apache/cloudberry/commit/142353684b2c697a0705cff489e0aaf54e2dcc61) - Fix COPY when executed via fdw on coordinator as executor (#14846) (Chris Hajas) [#877](https://github.com/apache/cloudberry/pull/877)
* [`a9e78885782`](https://github.com/apache/cloudberry/commit/a9e788857826041f2c01df47c758882c2d3a2628) - Orca FIXME: Add checks to ensure plans with part selectors are valid (#15083) (Chris Hajas) [#877](https://github.com/apache/cloudberry/pull/877)
* [`525b87facb2`](https://github.com/apache/cloudberry/commit/525b87facb2f9a806386f9a5de53a63102e1735b) - Disable GUC optimizer_penalize_broadcast_threshold when set to 0 (#15209) (Chris Hajas) [#877](https://github.com/apache/cloudberry/pull/877)
* [`1cabe2714ef`](https://github.com/apache/cloudberry/commit/1cabe2714efdaa7c66d016771ccea5c1e5a76531) - Fix 2 compiler warnings. (Zhenghua Lyu) [#877](https://github.com/apache/cloudberry/pull/877)
* [`15c1c54332d`](https://github.com/apache/cloudberry/commit/15c1c54332d99b387a238b71d148f23631c298a8) - Add support for foreign partitions in Orca (#14890) (Chris Hajas) [#877](https://github.com/apache/cloudberry/pull/877)
* [`0d20361a390`](https://github.com/apache/cloudberry/commit/0d20361a39028856c098cc1d34eb8e32a2bc693b) - Remove unused xforms (Chris Hajas) [#877](https://github.com/apache/cloudberry/pull/877)
* [`c3c0aef4015`](https://github.com/apache/cloudberry/commit/c3c0aef40158bb5f42c5dc5bbbf21aac11cd7f3f) - Fix duplicate filter due to operator argument order (#15111) (David Kimura) [#877](https://github.com/apache/cloudberry/pull/877)
* [`dababb3773a`](https://github.com/apache/cloudberry/commit/dababb3773a122283e71dd7040a66724731770ea) - Prevent duplicate filter issue (David Kimura) [#877](https://github.com/apache/cloudberry/pull/877)
* [`33e24882cb6`](https://github.com/apache/cloudberry/commit/33e24882cb608acc9d5f327d29812199f1732c6b) - Fix bogus ORCA plan joining CTE and replicated table (#14896) (Georgy Shelkovy) [#877](https://github.com/apache/cloudberry/pull/877)
* [`d0d363e4e5a`](https://github.com/apache/cloudberry/commit/d0d363e4e5a4293f8689d491f83df2bd99b80e21) - Support HashIndexes in Orca (hari krishna) [#877](https://github.com/apache/cloudberry/pull/877)
* [`07de611941d`](https://github.com/apache/cloudberry/commit/07de611941d3778dfcf37aed9a9f6ce3af72d2c8) - [ORCA] Use catalog to determine replication safe functions (David Kimura) [#877](https://github.com/apache/cloudberry/pull/877)
* [`ff274c55a9b`](https://github.com/apache/cloudberry/commit/ff274c55a9b6aefacd41ff6e482b5b77fac75791) - Store aggregate replication safety info in catalog (David Kimura) [#877](https://github.com/apache/cloudberry/pull/877)
* [`a153b4a3f27`](https://github.com/apache/cloudberry/commit/a153b4a3f2735f2d21b4f9cc8da332964bf1665c) - FIXME : Allow SPE plans to show "Partitions selected: 1 (out of 5)" (hari krishna) [#877](https://github.com/apache/cloudberry/pull/877)
* [`572f755fc02`](https://github.com/apache/cloudberry/commit/572f755fc0210df46df80812dc94ec099dba0864) - Set `dismiss_stale_reviews: false` in .asf.yaml (Dianjin Wang) [#879](https://github.com/apache/cloudberry/pull/879)
* [`ed022d363fd`](https://github.com/apache/cloudberry/commit/ed022d363fd68c1fe63cf1faed0e68a050b39ee7) - Fix cherry-pick issues (reshke) [#871](https://github.com/apache/cloudberry/pull/871)
* [`12c1d2058e6`](https://github.com/apache/cloudberry/commit/12c1d2058e65625af9f7596e7bfd0c3e855ad72a) - Support specification of reloptions when switching storage model (Huansong Fu) [#871](https://github.com/apache/cloudberry/pull/871)
* [`776806d2035`](https://github.com/apache/cloudberry/commit/776806d20358906e7ccf3c827a3fa62521189905) - Add comment explaining AT SET ACCESS METHOD flow (Divyesh Vanjare) [#871](https://github.com/apache/cloudberry/pull/871)
* [`0b6729f1fcc`](https://github.com/apache/cloudberry/commit/0b6729f1fcc9e5e4d925f4ec89a2a20a23dd865d) - Support setting table-level reloptions for AO/AOCO tables (Soumyadeep Chakraborty) [#871](https://github.com/apache/cloudberry/pull/871)
* [`11454f9f5fc`](https://github.com/apache/cloudberry/commit/11454f9f5fc3960eecf287a219876f2a657a6181) - Fix cherry-pick. (Zhang Mingli) [#880](https://github.com/apache/cloudberry/pull/880)
* [`e2578ea92d8`](https://github.com/apache/cloudberry/commit/e2578ea92d835376293de4623a60fddebb91329f) - Fix pg_stat_statements node type unexpected warning (#15094) (ZhangHuiGui) [#880](https://github.com/apache/cloudberry/pull/880)
* [`e6430f4be68`](https://github.com/apache/cloudberry/commit/e6430f4be68ee158e1638e9191f1e22099aeb0db) - Fix flaky test function_extensions (Huansong Fu) [#880](https://github.com/apache/cloudberry/pull/880)
* [`903f5758643`](https://github.com/apache/cloudberry/commit/903f5758643681a246e089ce10976a5c315581b6) - Fix crash of initplan in mpp (#16288) (chaotian) [#880](https://github.com/apache/cloudberry/pull/880)
* [`e5803f23ae4`](https://github.com/apache/cloudberry/commit/e5803f23ae44b8b436e3436400baf25927c87f46) - Retry gang creation for non-recovery failures (soumyadeep2007) [#880](https://github.com/apache/cloudberry/pull/880)
* [`bc03c51e03d`](https://github.com/apache/cloudberry/commit/bc03c51e03d88de483a388ed78746af0e5e31378) - Fix SET command that sends DTX protocol command when shouldn't (Huansong Fu) [#880](https://github.com/apache/cloudberry/pull/880)
* [`8dc163f60d6`](https://github.com/apache/cloudberry/commit/8dc163f60d6564bc80d5125e47b45ec4aac22332) - Fix segment fault in addOneOption() . (#16504) (Aegeaner) [#880](https://github.com/apache/cloudberry/pull/880)
* [`bad7e3b647d`](https://github.com/apache/cloudberry/commit/bad7e3b647d47585d5572d1463afddcb99d81d69) - Make GetConfigOption/GetConfigOptionResetString return "" for NULL. (Tom Lane) [#880](https://github.com/apache/cloudberry/pull/880)
* [`8b0f06b6f43`](https://github.com/apache/cloudberry/commit/8b0f06b6f4301f587879134c5fc6cbfcea36fbbd) - Be more wary about NULL values for GUC string variables. (Tom Lane) [#880](https://github.com/apache/cloudberry/pull/880)
* [`39e15abae97`](https://github.com/apache/cloudberry/commit/39e15abae97a40faf29351544a7a0cfb9996212c) - Fix parallel_retrieve_cursor diffs. (Zhang Mingli) [#880](https://github.com/apache/cloudberry/pull/880)
* [`277714c2138`](https://github.com/apache/cloudberry/commit/277714c21381eaa2816db63d9d11176bb9d23c5d) - Add test case for PR-11946 (#16669) (Yongtao Huang) [#880](https://github.com/apache/cloudberry/pull/880)
* [`6430c479d8a`](https://github.com/apache/cloudberry/commit/6430c479d8ab350b30d721ef8e1ad5b2fa1ab424) - Fix bug: cannot use LOCALE flag with a str pattern (#16666) (Yongtao Huang) [#880](https://github.com/apache/cloudberry/pull/880)
* [`f17d2ef9ef1`](https://github.com/apache/cloudberry/commit/f17d2ef9ef134382957a0883f42de8cacc71348b) - Fix a typo in cdbmutate.c  (#16658) (Max Laakkonen) [#880](https://github.com/apache/cloudberry/pull/880)
* [`1b6f6f6ff80`](https://github.com/apache/cloudberry/commit/1b6f6f6ff80b52482f326e2189567fc833336743) - Update appendonlyblockdirectory.c: imporve coding style. (#811) (reshke) [#811](https://github.com/apache/cloudberry/pull/811)
* [`fe8326add25`](https://github.com/apache/cloudberry/commit/fe8326add2575892aabf01bd09919e5c5143ffce) - print log message with write_stderr when reach vmem or resgroup limit. (Wenru Yan) [#878](https://github.com/apache/cloudberry/pull/878)
* [`566a3d8a1de`](https://github.com/apache/cloudberry/commit/566a3d8a1def49dbebb6f59d7c4cec990008149a) - Analyzedb: Add materialized views to list of tables to be analyzed (#16410) (Marcus Robb) [#878](https://github.com/apache/cloudberry/pull/878)
* [`4ac70ed5669`](https://github.com/apache/cloudberry/commit/4ac70ed5669a23c6708b1fb39f1d4b4dadecb7e0) - Fix the wrong permissions warning on the pgpass file (#16207) (Moonsn) [#878](https://github.com/apache/cloudberry/pull/878)
* [`a9f26d7cd5e`](https://github.com/apache/cloudberry/commit/a9f26d7cd5e692c308083b3b7fa61aea2841aee5) - Bring work_mem out of deprecation (soumyadeep2007) [#878](https://github.com/apache/cloudberry/pull/878)
* [`0c830714271`](https://github.com/apache/cloudberry/commit/0c830714271464d1fb689dee70883e659cc43756) - Archive current timeline history file after recovery finishes if needed (Jimmy Yih) [#878](https://github.com/apache/cloudberry/pull/878)
* [`4b5d93994cb`](https://github.com/apache/cloudberry/commit/4b5d93994cb84282f2c257d57381719670345494) - Fix flaky test in create_index (#16368) (bhari) [#878](https://github.com/apache/cloudberry/pull/878)
* [`7fb9b5c6880`](https://github.com/apache/cloudberry/commit/7fb9b5c68805a969df10b859cf66678db646fdea) - Remove unnecessary trailing semicolons from Python scripts. (#15976) (Richy Wang) [#878](https://github.com/apache/cloudberry/pull/878)
* [`d167c736c1b`](https://github.com/apache/cloudberry/commit/d167c736c1bc1877aae7a019f1f9206b68b98ba6) - Fix flakiness of regression test in create_index (#16343) (bhari) [#878](https://github.com/apache/cloudberry/pull/878)
* [`546d7b8a9df`](https://github.com/apache/cloudberry/commit/546d7b8a9df7e0eac484236f020641d795d3e787) - Fix recursive CTE mergejoin having motion on WTS (Divyesh Vanjare) [#878](https://github.com/apache/cloudberry/pull/878)
* [`40f3dc2c584`](https://github.com/apache/cloudberry/commit/40f3dc2c5842e7068d3f932551825d0ae2b61263) - Disallow non-standalone ALTER distribution (soumyadeep2007) [#878](https://github.com/apache/cloudberry/pull/878)
* [`04647e24486`](https://github.com/apache/cloudberry/commit/04647e24486337d3f307b88388d3df0a65ef578e) - Fix gpstop pipeline flakiness after #15727 (sruthip2) [#878](https://github.com/apache/cloudberry/pull/878)
* [`aa9cb766222`](https://github.com/apache/cloudberry/commit/aa9cb76622213b2e675d120a3f8743066b28d340) - Fix partition single node test. (Jianghua Yang) [#878](https://github.com/apache/cloudberry/pull/878)
* [`3b2d07b605a`](https://github.com/apache/cloudberry/commit/3b2d07b605a24f0ec9d88707027580abb51f8706) - add some test results into .gitignore (#16254) (Zhenglong Li) [#878](https://github.com/apache/cloudberry/pull/878)
* [`33e877344a6`](https://github.com/apache/cloudberry/commit/33e877344a6cf7d36c6f6ff468fd0b1bd9651273) - Remove a FIXME in tupser.c (Huansong Fu) [#878](https://github.com/apache/cloudberry/pull/878)
* [`7ac7eb629ea`](https://github.com/apache/cloudberry/commit/7ac7eb629ea19eba90a0a0fea67657c303986bfc) - Don't use PGC_S_OVERRIDE for setting gp_role GUC (Ashwin Agrawal) [#878](https://github.com/apache/cloudberry/pull/878)
* [`dc79dab0a78`](https://github.com/apache/cloudberry/commit/dc79dab0a7825557c94ee88fc14b6d2a543d6248) - Make gp_session_role alias of gp_role using map_old_guc_names (Ashwin Agrawal) [#878](https://github.com/apache/cloudberry/pull/878)
* [`beac4003edb`](https://github.com/apache/cloudberry/commit/beac4003edb38c180b3ebc8bc4b2a3bdabed710d) - Add logic for checking the process holding the lock file exists (sruthip2) [#878](https://github.com/apache/cloudberry/pull/878)
* [`65209eb3830`](https://github.com/apache/cloudberry/commit/65209eb38308e2e8b4908f2c1a3cfdbb13a71047) - Make slabs and generations use gp_malloc (free|Soumyadeep Chakraborty) [#878](https://github.com/apache/cloudberry/pull/878)
* [`62307f34ff8`](https://github.com/apache/cloudberry/commit/62307f34ff86f31c9eee86e1f8ebec129beba425) - Refine error message for EXCHANGE PARTITION ... WITH (WITHOU VALIDATION|Huansong Fu) [#878](https://github.com/apache/cloudberry/pull/878)
* [`dbae11e750c`](https://github.com/apache/cloudberry/commit/dbae11e750cb43133d9da8036586185d8551019f) - Added test case for pr 15283 (#15528) (Rakesh Sharma) [#878](https://github.com/apache/cloudberry/pull/878)
* [`84748f57752`](https://github.com/apache/cloudberry/commit/84748f577521643f0b52202eda4581f18436307d) - Rewrite test interrupt_holdoff_count (Evgeniy Ratkov) [#878](https://github.com/apache/cloudberry/pull/878)
* [`a902d1fadb6`](https://github.com/apache/cloudberry/commit/a902d1fadb6767dc46bf9d22608dcd4cb610959c) - Add test case for PR 15279. (Zhenghua Lyu) [#878](https://github.com/apache/cloudberry/pull/878)
* [`cf583e890fd`](https://github.com/apache/cloudberry/commit/cf583e890fdfd63d12bc45120ef7e484af5eaed8) - Possible qual postponing past ANTI-JOIN/LASJ_NOTIN-JOIN. (dh-cloud) [#878](https://github.com/apache/cloudberry/pull/878)
* [`3acf999b6ff`](https://github.com/apache/cloudberry/commit/3acf999b6ff32eacff864db70ae183fc58fa9072) - Replace last PushOverrideSearchPath() call with set_config_option(). (Noah Misch) [#878](https://github.com/apache/cloudberry/pull/878)
* [`b21d8dc5f2c`](https://github.com/apache/cloudberry/commit/b21d8dc5f2c552426a58c42fceab647647790750) - Do not use immediate restart in regress test (Huansong Fu) [#878](https://github.com/apache/cloudberry/pull/878)
* [`df1e2ff5ae7`](https://github.com/apache/cloudberry/commit/df1e2ff5ae7ecfda2f45f7b2f2ea82102ea4547e) - Prevent CREATE TABLE from using dangling tablespace (#876) (Hao Wu) [#876](https://github.com/apache/cloudberry/pull/876)
* [`76184113928`](https://github.com/apache/cloudberry/commit/76184113928ec6a062a7f429b671d3b2ae6602c4) - Fix cherry-picks. (Zhang Mingli) [#874](https://github.com/apache/cloudberry/pull/874)
* [`a35742c199d`](https://github.com/apache/cloudberry/commit/a35742c199d2871a17436a6c5a4cf0912b42b0fc) - io limit: save oid in catalog instead of tablespace name (#16324) (RMT) [#874](https://github.com/apache/cloudberry/pull/874)
* [`9cdce7a009a`](https://github.com/apache/cloudberry/commit/9cdce7a009aabaee016ebf0837cbd337ac943c14) - Fix ldap crash when ldaptls=1 and ldapscheme is not set. (#16326) (zhaorui) [#874](https://github.com/apache/cloudberry/pull/874)
* [`676325a1b1c`](https://github.com/apache/cloudberry/commit/676325a1b1cc12880308a9a2e745b0e6fd89c67c) - Use syscache in GetExtTableEntryIfExists (Huansong Fu) [#874](https://github.com/apache/cloudberry/pull/874)
* [`a8f3f4f16da`](https://github.com/apache/cloudberry/commit/a8f3f4f16da2d43b09dbd685929b59fd6757b222) - analyzedb should be skipped on Temp tables (#16095) (Chandan Kunal) [#874](https://github.com/apache/cloudberry/pull/874)
* [`6fdc18dfbdf`](https://github.com/apache/cloudberry/commit/6fdc18dfbdfff94463fc978dd126006bdd4622eb) - Remove some duplicated function declare in elog.c (Xiaoran Wang) [#874](https://github.com/apache/cloudberry/pull/874)
* [`84c2ea58a06`](https://github.com/apache/cloudberry/commit/84c2ea58a06348d6522f70149661a471f3bc9d47) - Flag persistent WalSndError on repslot invalidation (Soumyadeep Chakraborty) [#874](https://github.com/apache/cloudberry/pull/874)
* [`1549da710ce`](https://github.com/apache/cloudberry/commit/1549da710ce35e7befc7157916e526c9237f8803) - Remove two dead test out files (Huansong Fu) [#874](https://github.com/apache/cloudberry/pull/874)
* [`5d3aab11d84`](https://github.com/apache/cloudberry/commit/5d3aab11d84802f40ef260535cece2ba197c3bbe) - gp_system views: Remove \n from sed replacement (Soumyadeep Chakraborty) [#874](https://github.com/apache/cloudberry/pull/874)
* [`683e14560de`](https://github.com/apache/cloudberry/commit/683e14560deb94868605a4ebea661b29c7fb54a2) - Remove a FIXME in cdbaocsam.h (Huansong Fu) [#874](https://github.com/apache/cloudberry/pull/874)
* [`dfc5c23e2af`](https://github.com/apache/cloudberry/commit/dfc5c23e2af8334aa0914509e07af339daddbc8a) - Disuse physical list for legacy planner. (#16014) (Aegeaner) [#874](https://github.com/apache/cloudberry/pull/874)
* [`7c809f87458`](https://github.com/apache/cloudberry/commit/7c809f8745836546137d34c9714571d166af6680) - Fix flaky test `cluster_gp` (Marbin Tan) [#874](https://github.com/apache/cloudberry/pull/874)
* [`028f0d5085e`](https://github.com/apache/cloudberry/commit/028f0d5085e1beb9b666c4925f05212812199aa1) - Disallow altering the mpp_execute option of foreign wrappers (Adam Lee) [#874](https://github.com/apache/cloudberry/pull/874)
* [`7c486072467`](https://github.com/apache/cloudberry/commit/7c4860724672e2f673ddcc2b08f40425eb15e3fb) - Invalidate its foreign table's relcache when alter server (Adam Lee) [#874](https://github.com/apache/cloudberry/pull/874)
* [`8dec21d0dc9`](https://github.com/apache/cloudberry/commit/8dec21d0dc973544481ded1d6385ff6a23fd7730) - Reset plan cache to prevent flakes in ICW privileges (Jingyu Wang) [#874](https://github.com/apache/cloudberry/pull/874)
* [`a77b918f2df`](https://github.com/apache/cloudberry/commit/a77b918f2df826cf2397cd9de6f8f5aa0d422b61) - Fix flaky create_index test (Chris Hajas) [#874](https://github.com/apache/cloudberry/pull/874)
* [`bbd2f95b876`](https://github.com/apache/cloudberry/commit/bbd2f95b876b4ba343664ae993a3e17f8db9dbb4) - Allocate histogram sample array on heap (Jingyu Wang) [#874](https://github.com/apache/cloudberry/pull/874)
* [`59ed5267767`](https://github.com/apache/cloudberry/commit/59ed52677672aa0c9bb2880214f65a06053fb34d) - minirepro: collect using sequence (wenru yan) [#874](https://github.com/apache/cloudberry/pull/874)
* [`1841def94da`](https://github.com/apache/cloudberry/commit/1841def94da58a7e8a9bed94d9a9a990d1af01cb) - [7X] Keep order while removing duplicated paths. (#16068) (Xing Guo) [#874](https://github.com/apache/cloudberry/pull/874)
* [`3cc5995d721`](https://github.com/apache/cloudberry/commit/3cc5995d721b1136c9e9f85bbc6b5ff28644e2d2) - LockErrorCleanup(): missing RESUME_INTERRUPTS() call before return. (Haolin Wang) [#874](https://github.com/apache/cloudberry/pull/874)
* [`4591ad33bc0`](https://github.com/apache/cloudberry/commit/4591ad33bc09d7c72b00a30ddff705babea2609d) - VarBlockIsValid(): offset is not updated when checking the offset array. (Haolin Wang) [#874](https://github.com/apache/cloudberry/pull/874)
* [`74732f9d0c1`](https://github.com/apache/cloudberry/commit/74732f9d0c1ae970fc11d92ae224a2d151e81409) - Use findCMDInPath("bash") in Command (#15496) (RMT) [#874](https://github.com/apache/cloudberry/pull/874)
* [`d85692fec68`](https://github.com/apache/cloudberry/commit/d85692fec685a6fe386d03f565905cdc77214919) - walrep: Use regular libpq protocol (Soumyadeep Chakraborty) [#874](https://github.com/apache/cloudberry/pull/874)
* [`ebd5e18536e`](https://github.com/apache/cloudberry/commit/ebd5e18536eee75d2565c94120a96f9b89ede673) - Fix icw test cases generted from "ORCA support ext stats, Fix EPQ..." (zhoujiaqi) [#855](https://github.com/apache/cloudberry/pull/855)
* [`6bf82a57a78`](https://github.com/apache/cloudberry/commit/6bf82a57a781e9454332110baf4c7c7d5030ba7d) - ORCA ignores empty or unsupported ext stats (zhoujiaqi) [#855](https://github.com/apache/cloudberry/pull/855)
* [`a1712501411`](https://github.com/apache/cloudberry/commit/a1712501411ff46bc6f68faefb98fd05b8abf528) - Fix core dump generated by "ORCA support ext stats, Fix EPQ..." (zhoujiaqi) [#855](https://github.com/apache/cloudberry/pull/855)
* [`cdf1754546b`](https://github.com/apache/cloudberry/commit/cdf1754546bf6fa971f89d29d9c5f2dbfba26506) - Orca FIXME: Remove references to RelIsPartitioned (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`01749f37d27`](https://github.com/apache/cloudberry/commit/01749f37d272dbf6fa03eae0296eb91d6ba5645a) - Address a couple of Orca fixmes (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`8ce38cf2ea3`](https://github.com/apache/cloudberry/commit/8ce38cf2ea3efc61fbc362f86d276d5a7979700c) - Remove renaming orca fixme (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`f13bb32212b`](https://github.com/apache/cloudberry/commit/f13bb32212b84ed2ac23c7c367b5169bca8c5561) - Remove Orca FIXME in PrunePartitions (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`1aa7beb6727`](https://github.com/apache/cloudberry/commit/1aa7beb6727e037b134d5e6e02f07a109b4dd77c) - Orca FIXME: skip dropped columns (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`9f6d47643e3`](https://github.com/apache/cloudberry/commit/9f6d47643e3f396a5bc29bac3a7a1d807c83484a) - Fix incorrect result from hash join on char column (Jingyu Wang) [#855](https://github.com/apache/cloudberry/pull/855)
* [`20a996a2c17`](https://github.com/apache/cloudberry/commit/20a996a2c17704a0e158a1b8f9015954db00d6f7) - Orca FIXME: Improve stats calculation during static partition selection (#14958) (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`4b66e17b619`](https://github.com/apache/cloudberry/commit/4b66e17b619a4ccec3702345b737ed4fa761613f) - Support Direct Dispatch for a randomly distributed table, when filtered on gp_segment_id (nishant sharma) [#855](https://github.com/apache/cloudberry/pull/855)
* [`31254f40fc4`](https://github.com/apache/cloudberry/commit/31254f40fc44c132c35ad4625f6fad9b3ed9a1cb) - [ORCA] Allow push down of filter with BETWEEN predicate (#14872) (David Kimura) [#855](https://github.com/apache/cloudberry/pull/855)
* [`ff712241cd8`](https://github.com/apache/cloudberry/commit/ff712241cd87efa03695bd980bb77607db95f010) - Fix bug that Orca fails to decorrelate subqueries order by outer reference (#14905) (gpopt) [#855](https://github.com/apache/cloudberry/pull/855)
* [`4b3e0bb982f`](https://github.com/apache/cloudberry/commit/4b3e0bb982fe66b3920b5f561eaf97686780fec3) - Fix unused variable compile warnings (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`68cdac563bc`](https://github.com/apache/cloudberry/commit/68cdac563bc2f372719de87a70a61feedc883262) - FIXME remove gp_enable_sort_distinct and noduplicates optimizing (#14105) (Yao Wang) [#855](https://github.com/apache/cloudberry/pull/855)
* [`fc28511153a`](https://github.com/apache/cloudberry/commit/fc28511153acb3794d65b5f8da4cc03f9b29de37) - Fix bug that nestloop join fails to materialize the inner child for some cases (#14835) (gpopt) [#855](https://github.com/apache/cloudberry/pull/855)
* [`b5486f362d8`](https://github.com/apache/cloudberry/commit/b5486f362d80bb2dd21feb7218b278fc7773d31d) - Address FIXME for Orca constraint assertion (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`3e157c2d576`](https://github.com/apache/cloudberry/commit/3e157c2d576a882c2db44b60714493594d524522) - Address Orca FIXME: remove test (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`edb1fc26705`](https://github.com/apache/cloudberry/commit/edb1fc267050d3028370b3cc88fc7b817d73d9d1) - Address combining partition selectors stats FIXME in Orca (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`ba61d483185`](https://github.com/apache/cloudberry/commit/ba61d483185d00ef65a817a032e0a61dd5bcc56a) - Address CTE translation FIXMEs (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`8b784b219b2`](https://github.com/apache/cloudberry/commit/8b784b219b2a61c500cff696809c37c4ff988efa) - Remove unused mdpart_constraint from indexes in Orca (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`6312869a344`](https://github.com/apache/cloudberry/commit/6312869a34462337c9612c81b69589aa247b4f2b) - Resolve Orca FIXME for FValidPartEnforcers (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`fa59b58c814`](https://github.com/apache/cloudberry/commit/fa59b58c8141d875ad077c301d3a69c65d0e863a) - Remove unused Orca partitioning code in Orca (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`574537722dd`](https://github.com/apache/cloudberry/commit/574537722ddaf8d5fc5d7eba891764b41204796b) - Fall back in Orca for queries with RTE of type TableFunc (#14898) (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`7af9f19333e`](https://github.com/apache/cloudberry/commit/7af9f19333e8d9bf8466b7faf621a32cb1c417a7) - Add GUC optimizer_enable_foreign_table (#14844) (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`0ca83b2b469`](https://github.com/apache/cloudberry/commit/0ca83b2b46993a253ceffd5bf130035bc06156c6) - [ORCA] Add support for multi-variant n-distinct correlated stats (#14778) (David Kimura) [#855](https://github.com/apache/cloudberry/pull/855)
* [`20880d52603`](https://github.com/apache/cloudberry/commit/20880d52603dfbac033e6aca14429b915230cf3c) - FIXME: Rewrite IndexOpProperties API (Jingyu Wang) [#855](https://github.com/apache/cloudberry/pull/855)
* [`44a82a79c82`](https://github.com/apache/cloudberry/commit/44a82a79c82ef79cf95733317727a7717bf4af6e) - Fix EPQ for DML operations (#14304) (Alexandr Barulev) [#855](https://github.com/apache/cloudberry/pull/855)
* [`13f19cd1d77`](https://github.com/apache/cloudberry/commit/13f19cd1d777107d3f12b99fcc0b004e161ed682) - Remove unused num_leaf_partitions attribute in Orca (#14777) (Chris Hajas) [#855](https://github.com/apache/cloudberry/pull/855)
* [`14a78014f54`](https://github.com/apache/cloudberry/commit/14a78014f5488d89fbc5f48a341f10c2798fa227) - Enable direct dispatch if distribution column is of 'varchar' type and update hash function selection based on distribution policy of table (nishant sharma) [#855](https://github.com/apache/cloudberry/pull/855)
* [`7e1497f6bd7`](https://github.com/apache/cloudberry/commit/7e1497f6bd7886ad6caab250b6bab43b65c76c4f) - [ORCA] Use extended stats to estimate correlated cardinality (#14674) (David Kimura) [#855](https://github.com/apache/cloudberry/pull/855)
* [`e1ec6403f2a`](https://github.com/apache/cloudberry/commit/e1ec6403f2a293dccdb99d39e3e8453ce5085517) - LLVM bitcode generation for gpopt/gporca/gpcloud disabled (Tao Tang) [#855](https://github.com/apache/cloudberry/pull/855)
* [`f9f8bdbb2d1`](https://github.com/apache/cloudberry/commit/f9f8bdbb2d1bf8f0f53fd7c2a8bdc42c72c15ae4) - Update scripts to use python3 (Anusha Shakarad) [#855](https://github.com/apache/cloudberry/pull/855)
* [`b45cab5c7dc`](https://github.com/apache/cloudberry/commit/b45cab5c7dc244bbe6d07535df4aa2813355424b) - Fix locking clause on foreign table missing when ORCA is enabled (wuchengwen) [#855](https://github.com/apache/cloudberry/pull/855)
* [`7a8da0708b5`](https://github.com/apache/cloudberry/commit/7a8da0708b50dd9ac6bc2a61551e3cdfb8682720) - Add a GUC to discard redistribute hashjoin for Orca (#14642) (gpopt) [#855](https://github.com/apache/cloudberry/pull/855)
* [`1cbba02378b`](https://github.com/apache/cloudberry/commit/1cbba02378bfdf07b7e733cddd31f522c04cd9a4) - Revert "Only super user can set the GUC gp_resource_group_bypass." (Zhenghua Lyu) [#872](https://github.com/apache/cloudberry/pull/872)
* [`6c0f027ccd2`](https://github.com/apache/cloudberry/commit/6c0f027ccd278c56d8270d2347a7d6691246f85e) - Add gp_stat_progress_dtx_recovery for observability (Marbin Tan) [#872](https://github.com/apache/cloudberry/pull/872)
* [`57496bd335e`](https://github.com/apache/cloudberry/commit/57496bd335ef35bf37a16e7e2eb5ca320ac00534) - Fix threading.Thread.getName() is deprecated (#15996) (Yongtao Huang) [#872](https://github.com/apache/cloudberry/pull/872)
* [`123fcde8fc6`](https://github.com/apache/cloudberry/commit/123fcde8fc64780e7f17e2f962a314e5341e7cdb) - Fix flaky test for crash_recovery_dtm (Marbin Tan) [#872](https://github.com/apache/cloudberry/pull/872)
* [`2b4399c89f5`](https://github.com/apache/cloudberry/commit/2b4399c89f53e0a1988bcea165e930715c482d04) - Remove deprecated distutils (#15986) (Yongtao Huang) [#872](https://github.com/apache/cloudberry/pull/872)
* [`733e71cf854`](https://github.com/apache/cloudberry/commit/733e71cf854f7cc56b181fa892e9526d845a6603) - Remove dead code updateRoleForRecoveredSegs() (Ashwin Agrawal) [#872](https://github.com/apache/cloudberry/pull/872)
* [`9a608506f81`](https://github.com/apache/cloudberry/commit/9a608506f8126f5578099464d90c92f7bbfcc477) - gpcheckcat shouldn't throw dependency error for pg_subscription and pg_transform (Huansong Fu) [#872](https://github.com/apache/cloudberry/pull/872)
* [`611de07d9f9`](https://github.com/apache/cloudberry/commit/611de07d9f9f6d7b027d7ed73554bae095b255b2) - Fix the warning of pg_basebackup(). (#15784) (Zhang Hao) [#872](https://github.com/apache/cloudberry/pull/872)
* [`696aba727e1`](https://github.com/apache/cloudberry/commit/696aba727e129ec2abd92489bd5f7e860a6323f7) - Fix the version argument to ArgumentParser is deprecated. (#15948) (Yongtao Huang) [#872](https://github.com/apache/cloudberry/pull/872)
* [`af40a379856`](https://github.com/apache/cloudberry/commit/af40a379856b5dacd451cd96a3d90868b7f342a7) - fix cherry-pick (Zhang Mingli) [#867](https://github.com/apache/cloudberry/pull/867)
* [`97cebb1cc1c`](https://github.com/apache/cloudberry/commit/97cebb1cc1ca453b30b9f189cf421c3679fadb1a) - gpcheckcat: Add new option -x to set session level GUCs (#15962) (Praveen Kumar) [#867](https://github.com/apache/cloudberry/pull/867)
* [`f48b5443895`](https://github.com/apache/cloudberry/commit/f48b5443895218dd461442813e22a97cf8238304) - add view gp_resgroup_status_per_segment (xuejing zhao) [#867](https://github.com/apache/cloudberry/pull/867)
* [`24e71b856bb`](https://github.com/apache/cloudberry/commit/24e71b856bba57556e9c4d0a15d6f541ffc03e42) - Move single row expression handler list-concat logic to parser. (#16015) (reshke) [#867](https://github.com/apache/cloudberry/pull/867)
* [`030a102d3eb`](https://github.com/apache/cloudberry/commit/030a102d3eb6ebc9777b309aed773675820aebb7) - Change the default value of dtx_phase2_retry_second's to 600s (#15632) (Hongxu Ma) [#867](https://github.com/apache/cloudberry/pull/867)
* [`06e5792cda2`](https://github.com/apache/cloudberry/commit/06e5792cda273c4b7403f689f04bff1284e87724) - Remove assertion to allow per-phase progress reporting on VACUUM AO/CO. (Haolin Wang) [#867](https://github.com/apache/cloudberry/pull/867)
* [`6ccaed817c8`](https://github.com/apache/cloudberry/commit/6ccaed817c8979cca98b4ed94c59af3823a27c62) - Drop GUC_NOT_IN_SAMPLE from guc_checkpoint_timeout (Andrew Repp) [#867](https://github.com/apache/cloudberry/pull/867)
* [`2a00124a25e`](https://github.com/apache/cloudberry/commit/2a00124a25e594adefb0b1b5817eb4d181b1a214) - Remove block on changing checkpoint_timeout GUC (Andrew Repp) [#867](https://github.com/apache/cloudberry/pull/867)
* [`38a82ed063d`](https://github.com/apache/cloudberry/commit/38a82ed063dfb51d82ac14866e246fe547f5165e) - fix bitmap index (#15923) (Zhenglong Li) [#867](https://github.com/apache/cloudberry/pull/867)
* [`4b750879ac3`](https://github.com/apache/cloudberry/commit/4b750879ac3d1c4b33101b577ace87cef1f9d723) - Optimize gpfdist external table logic (#15987) (zhaorui) [#867](https://github.com/apache/cloudberry/pull/867)
* [`58ab3cb637a`](https://github.com/apache/cloudberry/commit/58ab3cb637a74c4f37994ee4f491e8daa44622b9) - remove FIXME in function create_motion_path_for_updel (#15968) (Zhenglong Li) [#867](https://github.com/apache/cloudberry/pull/867)
* [`6cec3486563`](https://github.com/apache/cloudberry/commit/6cec3486563e89123f61e911a58c78b3e63e019f) - Correct comment on relallivisible for AO/CO tables (Soumyadeep Chakraborty) [#870](https://github.com/apache/cloudberry/pull/870)
* [`f51358b8e21`](https://github.com/apache/cloudberry/commit/f51358b8e2132f58c62c244ea8eafc25e9e13ae1) - Correct faulty comment about relstats aggregation (Soumyadeep Chakraborty) [#870](https://github.com/apache/cloudberry/pull/870)
* [`53cf3334821`](https://github.com/apache/cloudberry/commit/53cf33348213da69e4f0f4298ec7a2d8625d274a) - Create Block Directory for Materialized Views of AO storage. (Zhang Mingli) [#866](https://github.com/apache/cloudberry/pull/866)
* [`2b31b258ef1`](https://github.com/apache/cloudberry/commit/2b31b258ef1a0ba12ea6c54cd7ed9fb9c1a2f923) - Add Cases for Issue 15794, 15767 and 15793. (Zhenghua Lyu) [#869](https://github.com/apache/cloudberry/pull/869)
* [`3420d9bf830`](https://github.com/apache/cloudberry/commit/3420d9bf830eee614174762b92169fe3d3378967) - Clean up the use of the deprecated unittest.TestCase.assertEquals() (#15947) (Yongtao Huang) [#869](https://github.com/apache/cloudberry/pull/869)
* [`8072a952119`](https://github.com/apache/cloudberry/commit/8072a952119745ee292ab4533d552f155180e65f) - Open parallel cursors on behalf of the current user (#15820) (Adam Lee) [#869](https://github.com/apache/cloudberry/pull/869)
* [`065e449aa73`](https://github.com/apache/cloudberry/commit/065e449aa73d87d98c0cf44709892c04344f9989) - Fix: BaseException.message deprecated since Python 2.6 (#15943) (Yongtao Huang) [#869](https://github.com/apache/cloudberry/pull/869)
* [`1107fbd0ac4`](https://github.com/apache/cloudberry/commit/1107fbd0ac45ea24152d2f028466ee3cf3dfd426) - Remove the deprecated 'universal newline' mode from open(). (#15933) (Xing Guo) [#869](https://github.com/apache/cloudberry/pull/869)
* [`81673445c53`](https://github.com/apache/cloudberry/commit/81673445c5377d7daeeede417a8b4fd8908961ef) - Remove dead function FinalizeDirectDispatchDataForSlice() (#15925) (Yongtao Huang) [#869](https://github.com/apache/cloudberry/pull/869)
* [`a3f17fe8ab6`](https://github.com/apache/cloudberry/commit/a3f17fe8ab64faa21709b2b7d86aa5dc14ecb1c2) - Fix a flaky test die_commit_pending_replication (Huansong Fu) [#869](https://github.com/apache/cloudberry/pull/869)
* [`a245727a4a8`](https://github.com/apache/cloudberry/commit/a245727a4a8a4113906e044451ba59c7c37a69f1) - function_extensions: Fix tmpdir ls flake (Soumyadeep Chakraborty) [#869](https://github.com/apache/cloudberry/pull/869)
* [`6d20299b62c`](https://github.com/apache/cloudberry/commit/6d20299b62c7a9d9cb769e14131dda22694ae7da) - Handle RLS dependencies in inlined set-returning functions properly. (Tom Lane) [#869](https://github.com/apache/cloudberry/pull/869)
* [`b6c7dbd5cac`](https://github.com/apache/cloudberry/commit/b6c7dbd5caca56420e9b4c2efa72fa2f78f4b319) - Fix another flaky aggregates ICW test (#15895) (Chris Hajas) [#869](https://github.com/apache/cloudberry/pull/869)
* [`5ccfc384092`](https://github.com/apache/cloudberry/commit/5ccfc38409214f1bfb6021e6b65af011585a0707) - fix compiler warning for gcc-12. (#15813) (Yang, Ying-chao) [#869](https://github.com/apache/cloudberry/pull/869)
* [`16ff096c3ee`](https://github.com/apache/cloudberry/commit/16ff096c3eea3911fe2dcbfd0b0c8d3eb8a7224b) - Fix gp_stat_bgwriter which shows incomplete/incorrect results (Huansong Fu) [#869](https://github.com/apache/cloudberry/pull/869)
* [`cfed4db9984`](https://github.com/apache/cloudberry/commit/cfed4db998493ee61153fcfef180bf6ea3f8814c) - resqueue test: Close holdable cursor instead (Soumyadeep Chakraborty) [#869](https://github.com/apache/cloudberry/pull/869)
* [`0015f032225`](https://github.com/apache/cloudberry/commit/0015f032225683c61eb04238b79dfc0a84083810) - Adjust sepgsql expected output for 681d9e462 et al. (Tom Lane) [#869](https://github.com/apache/cloudberry/pull/869)
* [`41e8e99e49e`](https://github.com/apache/cloudberry/commit/41e8e99e49e3c55d481778d26b64894124f4ae2d) - Update distribution policy for dropping distribution key dependency (Ekta Khanna) [#869](https://github.com/apache/cloudberry/pull/869)
* [`1eea879f729`](https://github.com/apache/cloudberry/commit/1eea879f729c60d35168916ba0e641c252eeebd1) - Assert AO/CO exclusion for anti-wraparound vacuums (Soumyadeep Chakraborty) [#869](https://github.com/apache/cloudberry/pull/869)
* [`7dfd904d1fc`](https://github.com/apache/cloudberry/commit/7dfd904d1fc412a64ad1a08f385baa7a06f03751) - Post fix: Change resgroup name. (Yongtao Huang) [#869](https://github.com/apache/cloudberry/pull/869)
* [`a084c9fc215`](https://github.com/apache/cloudberry/commit/a084c9fc215cabfc882e6f10d908b150d4f5b0ea) - remove upsert fixme from isolation_schedule (#15636) (chaotian) [#869](https://github.com/apache/cloudberry/pull/869)
* [`fd247d855b1`](https://github.com/apache/cloudberry/commit/fd247d855b1bf2bcb1ed006ce0545a49bad53c17) - Fix an issue with vacuum in TAP test (Huansong Fu) [#869](https://github.com/apache/cloudberry/pull/869)
* [`62d33aa0f6b`](https://github.com/apache/cloudberry/commit/62d33aa0f6b1956c9d46a876fa5a82691b2bfbea) - Fix flaky test AOCO_Compression (Huansong Fu) [#869](https://github.com/apache/cloudberry/pull/869)
* [`ee180975942`](https://github.com/apache/cloudberry/commit/ee180975942dcf1bb1e07e8b6bb782e04c0db456) - ignore tag.sql tag.out (Jianghua Yang) [#869](https://github.com/apache/cloudberry/pull/869)
* [`688bff0280f`](https://github.com/apache/cloudberry/commit/688bff0280f66b0a7b92c7fb83a7a2d4e5519408) - resqueue: Fix statement leak for holdable cursors (Soumyadeep Chakraborty) [#869](https://github.com/apache/cloudberry/pull/869)
* [`467281d9165`](https://github.com/apache/cloudberry/commit/467281d91650ae650e1366bd1680da74f86339f4) - Fix ao_filehandler for new attnum-filenum changes (Divyesh Vanjare) [#860](https://github.com/apache/cloudberry/pull/860)
* [`23d9f59b6eb`](https://github.com/apache/cloudberry/commit/23d9f59b6eba1096b7acd964dd72d82db3f01041) - Fix pg_aocsseg to work with attnum-filenum mapping (Divyesh Vanjare) [#860](https://github.com/apache/cloudberry/pull/860)
* [`b7711a84068`](https://github.com/apache/cloudberry/commit/b7711a8406864600b24167a81e33e96d6bfdda54) - Remove FIXME in partition_pruning that was already addressed (Chris Hajas) [#860](https://github.com/apache/cloudberry/pull/860)
* [`9e54b320e17`](https://github.com/apache/cloudberry/commit/9e54b320e1787aad68b136a30e25d1c29d8e7777) - Allow role names started with 'gp_' (Huansong Fu) [#860](https://github.com/apache/cloudberry/pull/860)
* [`476969de1e1`](https://github.com/apache/cloudberry/commit/476969de1e16ea9c4e6c0cea99f5ce23ee2236ec) - Fixing ONLY keyword for multiple tables in GRANT/REVOKE (Huansong Fu) [#860](https://github.com/apache/cloudberry/pull/860)
* [`0d6ac3a13e9`](https://github.com/apache/cloudberry/commit/0d6ac3a13e97fafe46c97a0be3c06e3027194959) - Fix flacky case 002_archiving.pl (Wenru Yan) [#860](https://github.com/apache/cloudberry/pull/860)
* [`4ad58005f75`](https://github.com/apache/cloudberry/commit/4ad58005f75301003ce14b65b8fbb417307369c5) - Fix flaky archiving tap test (#15544) (kaknikhil) [#860](https://github.com/apache/cloudberry/pull/860)
* [`7a37b527d73`](https://github.com/apache/cloudberry/commit/7a37b527d7392b129651b32447a7d614cd14db82) - Add tests for partial wal file generation (#15469) (Tao-Ma) [#860](https://github.com/apache/cloudberry/pull/860)
* [`c337b3df469`](https://github.com/apache/cloudberry/commit/c337b3df469eded685cb366227a24bfcad31af91) - Remove FIXME: cast the unknown typed literal to text (7X) (#15576) (Yongtao Huang) [#860](https://github.com/apache/cloudberry/pull/860)
* [`a319e065963`](https://github.com/apache/cloudberry/commit/a319e0659635ec012c192dcac76417caffdad88d) - Fix a comment in pg_dump (Jimmy Yih) [#860](https://github.com/apache/cloudberry/pull/860)
* [`7a8c7a67e93`](https://github.com/apache/cloudberry/commit/7a8c7a67e93aeaa9e36d1011d4900035c7f85ecc) - rename totalRowsScannned to totalRowsScanned (Haolin Wang) [#860](https://github.com/apache/cloudberry/pull/860)
* [`eecff115846`](https://github.com/apache/cloudberry/commit/eecff11584675a0d56c4c6174e62cab60a99ec79) - Remove unused Progress class from gpload (Bradford D. Boyle) [#860](https://github.com/apache/cloudberry/pull/860)
* [`61fd385886a`](https://github.com/apache/cloudberry/commit/61fd385886a60637771ac9082a8fc424afc2affd) - solve GPDB_96_MERGE_FIXME in planner.c (Tao Tang) [#860](https://github.com/apache/cloudberry/pull/860)
* [`20754373f49`](https://github.com/apache/cloudberry/commit/20754373f496677516a371fc4bf2ba53fc0c5482) - Remove GPDB_96_MERGE_FIXME in relnode.c:724 (#15557) (QingMa) [#860](https://github.com/apache/cloudberry/pull/860)
* [`d83f34bd860`](https://github.com/apache/cloudberry/commit/d83f34bd8601ca8ed1349c7106fb21bb144c7c0b) - Remove dead function in src/backend/cdb (7X) (#15553) (Yongtao Huang) [#860](https://github.com/apache/cloudberry/pull/860)
* [`77a6161a0eb`](https://github.com/apache/cloudberry/commit/77a6161a0eb21c4bfa39ff87a781e8a21112b8ae) - Fix code indent. (Zhang Mingli) [#860](https://github.com/apache/cloudberry/pull/860)
* [`6909ec814b6`](https://github.com/apache/cloudberry/commit/6909ec814b6eb33e09a5e14497634d64ae7e33bc) - Fix cherry-pick. (Zhang Mingli) [#858](https://github.com/apache/cloudberry/pull/858)
* [`22410a7a5b9`](https://github.com/apache/cloudberry/commit/22410a7a5b9098da5c9e6d9288cfaa956952374d) - Refactor AO macro RelationIsAppendOptimized (#15546) (chaotian) [#858](https://github.com/apache/cloudberry/pull/858)
* [`5851f9876b6`](https://github.com/apache/cloudberry/commit/5851f9876b6b6bed9177785e1cdc40feef2164fd) - Remove fixme from cdbpathlocus_for_insert (#15574) (chaotian) [#858](https://github.com/apache/cloudberry/pull/858)
* [`fd4abf4f7ec`](https://github.com/apache/cloudberry/commit/fd4abf4f7ec761a87feab59bba6798c91daa6424) - Disable -Wdeprecated-non-prototype in the back branches. (Tom Lane) [#858](https://github.com/apache/cloudberry/pull/858)
* [`9a9908688fe`](https://github.com/apache/cloudberry/commit/9a9908688fea51b5f5330186189bf17090e314dc) - Remove fixme in prepunion.c:848 (#15595) (QingMa) [#858](https://github.com/apache/cloudberry/pull/858)
* [`4e3230978a1`](https://github.com/apache/cloudberry/commit/4e3230978a19d787d24427640f6e7e2cd0a6d3bf) - Fix url_curl.c headers handling (#14976) (Alexey Gordeev) [#858](https://github.com/apache/cloudberry/pull/858)
* [`e6f956b8638`](https://github.com/apache/cloudberry/commit/e6f956b86386e3350ea7eaecccf064570445dfd0) - Replace -1 with InvalidAORowNum (Huansong Fu) [#858](https://github.com/apache/cloudberry/pull/858)
* [`4551673b2bb`](https://github.com/apache/cloudberry/commit/4551673b2bbeb7a60fab0e6986182f69b6e66a14) - Don't store plain types in short varlena format (Adam Lee) [#858](https://github.com/apache/cloudberry/pull/858)
* [`79b8ace965a`](https://github.com/apache/cloudberry/commit/79b8ace965ad334f0317238ff25d1446389ed806) - resource_queue.sql: Add pg_locks sanity check (Soumyadeep Chakraborty) [#864](https://github.com/apache/cloudberry/pull/864)
* [`cdad9dc3916`](https://github.com/apache/cloudberry/commit/cdad9dc3916e2b342846c9589a21f995132a08af) - Fix multi-row DEFAULT handling for INSERT ... SELECT rules. (Dean Rasheed) [#864](https://github.com/apache/cloudberry/pull/864)
* [`7f68241e2c6`](https://github.com/apache/cloudberry/commit/7f68241e2c6a2f023ab5fd84d00f2d7da6d2c844) - Yet further fixes for multi-row VALUES lists for updatable views. (Tom Lane) [#864](https://github.com/apache/cloudberry/pull/864)
* [`2d61bf5266d`](https://github.com/apache/cloudberry/commit/2d61bf5266dea8c786113886a19ca8986ceafaf6) - starting segments in execute mode post recovery (#15599) (Piyush Chandwadkar) [#864](https://github.com/apache/cloudberry/pull/864)
* [`d3c84a9a81b`](https://github.com/apache/cloudberry/commit/d3c84a9a81ba7a706c0a7bc4688119899e1e3069) - Remove MemoryContext s_tupSerMemCtxt (#15779) (Lei (Alexandra) Wang) [#864](https://github.com/apache/cloudberry/pull/864)
* [`e02db30aa42`](https://github.com/apache/cloudberry/commit/e02db30aa424c51a99df6a13267f0fce307cce11) - Clean up comments. (Jianghua Yang) [#864](https://github.com/apache/cloudberry/pull/864)
* [`21404af3303`](https://github.com/apache/cloudberry/commit/21404af3303034a10af3840b03bc8bc9d331f02b) - Post fix: replace 'egrep' with 'grep -E' in gpMgmt (#15810) (Yongtao Huang) [#864](https://github.com/apache/cloudberry/pull/864)
* [`6a2c96da050`](https://github.com/apache/cloudberry/commit/6a2c96da0507f5fcff2cf609c2fe1efcc9fff50b) - Replace 'egrep' with 'grep -E'. (#15804) (Xing Guo) [#864](https://github.com/apache/cloudberry/pull/864)
* [`37c0b696a63`](https://github.com/apache/cloudberry/commit/37c0b696a63568ec98bd89b38faa531e33772b0e) - Update comment of createMotionLayerState() (#15800) (Yongtao Huang) [#864](https://github.com/apache/cloudberry/pull/864)
* [`81d4dd86561`](https://github.com/apache/cloudberry/commit/81d4dd86561583159a9ac709c682e00c468ecd97) - Enable `wal_compression` by default (Marbin Tan) [#864](https://github.com/apache/cloudberry/pull/864)
* [`2f11cacb742`](https://github.com/apache/cloudberry/commit/2f11cacb742d752c6c272369dbef5279e97a7768) - Only super user can set the GUC gp_resource_group_bypass. (Zhenghua Lyu) [#864](https://github.com/apache/cloudberry/pull/864)
* [`2de0e285f5e`](https://github.com/apache/cloudberry/commit/2de0e285f5e8f2b580cbe4f3e4e200f0855f5e68) - Revert "Refactor cdbpullup_missingVarWalker." (Zhenghua Lyu) [#864](https://github.com/apache/cloudberry/pull/864)
* [`f917aa34468`](https://github.com/apache/cloudberry/commit/f917aa344681029d2b73d7324b5265f80f1942e8) - Remove FIXME in relation_open() (#15786) (Yongtao Huang) [#864](https://github.com/apache/cloudberry/pull/864)
* [`5eb5061024b`](https://github.com/apache/cloudberry/commit/5eb5061024b58acdcadcb0421e49213e187c7b23) - Add missing nodes for function raw_expression_tree_walker(). (wenru yan) [#864](https://github.com/apache/cloudberry/pull/864)
* [`eba05de737e`](https://github.com/apache/cloudberry/commit/eba05de737eea2e62926749bdb729acc55dc6bfb) - Resolve FIXMEs in datetime.c (Huansong Fu) [#864](https://github.com/apache/cloudberry/pull/864)
* [`f0fd4bd2298`](https://github.com/apache/cloudberry/commit/f0fd4bd2298b3d9a327fce283633750c6fa63d12) - CI fix: don't log too much interconnect events (#15761) (Adam Lee) [#864](https://github.com/apache/cloudberry/pull/864)
* [`745b89a8e83`](https://github.com/apache/cloudberry/commit/745b89a8e83f8f4ebcbf19dd8b65b47db4c2e7f2) - Refactor cdbpullup_missingVarWalker. (Zhenghua Lyu) [#864](https://github.com/apache/cloudberry/pull/864)
* [`b1aebd3afe0`](https://github.com/apache/cloudberry/commit/b1aebd3afe01557ceea40801082f9cddb6429a97) - Remove deadcode cdbpullup_isExprCoveredByTargetlist. (Zhenghua Lyu) [#864](https://github.com/apache/cloudberry/pull/864)
* [`32d33bf966b`](https://github.com/apache/cloudberry/commit/32d33bf966bb3d55a98000c8a95b52a596ca6677) - Move a FIXME from postgresql.conf.sample to guc.c (Huansong Fu) [#864](https://github.com/apache/cloudberry/pull/864)
* [`c48753031ac`](https://github.com/apache/cloudberry/commit/c48753031ac492dc21dff4df2c3f62a7a4678841) - Bump minimum requirement of zstd to 1.4.0 (Marbin Tan) [#864](https://github.com/apache/cloudberry/pull/864)
* [`8cf1abafb45`](https://github.com/apache/cloudberry/commit/8cf1abafb45f60da1469d7523f098903688bce31) - Refactor ALTER TABLE SET AM to be align with 2970799 (reshke) [#861](https://github.com/apache/cloudberry/pull/861)
* [`53e108496dd`](https://github.com/apache/cloudberry/commit/53e108496dde8a32a2a56c7c883efaab30743a2a) - Fix cherry-pick issues (reshke) [#861](https://github.com/apache/cloudberry/pull/861)
* [`8208479bb56`](https://github.com/apache/cloudberry/commit/8208479bb568b72da7ffbce8092d49b736cea055) - Fix an issue where the pg_appendonly entry is not removed during AO->heap (Huansong Fu) [#861](https://github.com/apache/cloudberry/pull/861)
* [`c83e5c6f3a1`](https://github.com/apache/cloudberry/commit/c83e5c6f********************************) - Check relam changes in alter_table_set_am test (Huansong Fu) [#861](https://github.com/apache/cloudberry/pull/861)
* [`fc5f67f1508`](https://github.com/apache/cloudberry/commit/fc5f67f1508fd95a64f2f6769d2027675a775957) - Add tests for ALTER TABLE SET WITH for AO to Heap tables (Huansong Fu) [#861](https://github.com/apache/cloudberry/pull/861)
* [`1c759854184`](https://github.com/apache/cloudberry/commit/1c759854184398fd7674c8f712a3ef7c140b0564) - ALTER TABLE SET ACCESS METHOD: AO->Heap support (Huansong Fu) [#861](https://github.com/apache/cloudberry/pull/861)
* [`a79eb160673`](https://github.com/apache/cloudberry/commit/a79eb160673f7dff876827cc5cbaa0c171db63ed) - AT SET WITH() syntax to change table access method (Soumyadeep Chakraborty) [#861](https://github.com/apache/cloudberry/pull/861)
* [`f94aab469fa`](https://github.com/apache/cloudberry/commit/f94aab469faf6698d903ba42eaa2db31d296bbc8) - ALTER TABLE SET ACCESS METHOD: Heap->AO support (Soumyadeep Chakraborty) [#861](https://github.com/apache/cloudberry/pull/861)
* [`c3f7cbc4dda`](https://github.com/apache/cloudberry/commit/c3f7cbc4ddae7eaafe677ca0391105792024da58) - Add support for SET ACCESS METHOD in ALTER TABLE (Michael Paquier) [#861](https://github.com/apache/cloudberry/pull/861)
* [`325df54863c`](https://github.com/apache/cloudberry/commit/325df54863c218a8f05726163a5051df8b4af11b) - Resolve two FIXMEs in extractPageInfo() (Huansong Fu) [#856](https://github.com/apache/cloudberry/pull/856)
* [`c040da238a1`](https://github.com/apache/cloudberry/commit/c040da238a16fc158969d823cfde3b651295f028) - Resolve a FIXME for gpcheckcat (Huansong Fu) [#856](https://github.com/apache/cloudberry/pull/856)
* [`04d2a5a2035`](https://github.com/apache/cloudberry/commit/04d2a5a203543221bbb83918cf76c7fde4c1ab96) - Fix GPDB_96_MERGE_FIXME: verify walker works on Sequence node (in nodeFuncs.c) (Tao Tang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`cfd67240f05`](https://github.com/apache/cloudberry/commit/cfd67240f0574d145d52627a6c5397c78aa61151) - Remove GPDB_95_MERGE_FIXME in groupingset*. (#15680) (Wenlin Zhang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`8c6a3533497`](https://github.com/apache/cloudberry/commit/8c6a3533497f16e9619740343e174453e773d4d2) - Remove GPDB_96_MERGE_FIXME in subselect_gp*. (#15587) (Wenlin Zhang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`ae976a6f0f2`](https://github.com/apache/cloudberry/commit/ae976a6f0f2065888cf3d123bb158e7b172d989a) - Resolve GPDB_96_MERGE_FIXME in prepunion.c about recursive cte (xuejing zhao) [#856](https://github.com/apache/cloudberry/pull/856)
* [`6e94aaf727f`](https://github.com/apache/cloudberry/commit/6e94aaf727f2d72854258ad762be03cc5bed492a) - Change key `log` to UNRESERVED_KEYWORD (#15606) (Xiaoran Wang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`a1ddb29cc12`](https://github.com/apache/cloudberry/commit/a1ddb29cc1233e84ba0b98fafcc875bbf3e216ba) - [typo] polish parameter name of _get_remove_cmd (#15473) (zhjwpku) [#856](https://github.com/apache/cloudberry/pull/856)
* [`4e08a005fe2`](https://github.com/apache/cloudberry/commit/4e08a005fe25ff27368f56f66ccf5a0955eb5d43) - Set next OID before restoring schema during pg_upgrade (Jimmy Yih) [#856](https://github.com/apache/cloudberry/pull/856)
* [`d181ca6a0a8`](https://github.com/apache/cloudberry/commit/d181ca6a0a8e7aa4e5cb612f2509607d974f8ffc) - Remove dead hash agg GUCs (Adam Lee) [#856](https://github.com/apache/cloudberry/pull/856)
* [`c0124395eac`](https://github.com/apache/cloudberry/commit/c0124395eac2a0437609a4eaf92351d5ae10bf73) - Fix an issue where we increment command counter incorrectly (Huansong Fu) [#856](https://github.com/apache/cloudberry/pull/856)
* [`de51dfcc6f1`](https://github.com/apache/cloudberry/commit/de51dfcc6f15b2edd0cc4929ed9b490ed5af77f8) - Fix dangling pointer in ExecDynamicIndexScan() (#15619) (Yao Wang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`261f6bdffef`](https://github.com/apache/cloudberry/commit/261f6bdffef46807953d095c953f8259a56eb9df) - Remove FIXME: no longer to pass paramExecTypes as a param (7X) (#15649) (Yongtao Huang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`0eacb9fe299`](https://github.com/apache/cloudberry/commit/0eacb9fe2993791822971f432da0fc4b47d3e8e9) - Fix IC bugs in ic_proxy_ibuf_push() (#15139) (Hongxu Ma) [#856](https://github.com/apache/cloudberry/pull/856)
* [`c41d675efc0`](https://github.com/apache/cloudberry/commit/c41d675efc0fc40289d8fe94307b767355c60299) - Enhance logging for FTS time out (#14185) (Yao Wang) [#856](https://github.com/apache/cloudberry/pull/856)
* [`6032c5878c9`](https://github.com/apache/cloudberry/commit/6032c5878c909bb033eed4c8ab919a90fc2bb697) - Fix: 'unrecognized node type: 145' in transformExpr (#15184) (gtygo) [#856](https://github.com/apache/cloudberry/pull/856)
* [`cc592337e01`](https://github.com/apache/cloudberry/commit/cc592337e019eca6fdbe481756d51059f63a420d) - Fix typos. (Zhang Mingli) [#856](https://github.com/apache/cloudberry/pull/856)
* [`2786dd0c524`](https://github.com/apache/cloudberry/commit/2786dd0c524323ada98e6d85ec9d16029a29a185) - ASSERT relation is AO in GetAppendOnlyEntryAuxOids/GetAppendOnlyEntry (Huansong Fu) [#856](https://github.com/apache/cloudberry/pull/856)
* [`2c290bc66d4`](https://github.com/apache/cloudberry/commit/2c290bc66d4805f9e30fc723d1633b13bef593d3) - Error if partitioned table passed to gp_toolkit.__gp_aocsseg(oid) (Brent Doil) [#856](https://github.com/apache/cloudberry/pull/856)
* [`8e49f0b4301`](https://github.com/apache/cloudberry/commit/8e49f0b43014490bae4ec2659715e343db38f920) - Fixed the string comparison warning (Annpurna Shahani) [#856](https://github.com/apache/cloudberry/pull/856)
* [`87ab71c51f1`](https://github.com/apache/cloudberry/commit/87ab71c51f1ce495937214e06f2a4634f3d262cd) - Fix flaky idle_gang_cleaner case (#15228) (QingMa) [#856](https://github.com/apache/cloudberry/pull/856)
* [`e1168bda54f`](https://github.com/apache/cloudberry/commit/e1168bda54f844c81b420e64824fe67df8de54a1) - FIX: cherry-pick missing icw test cases (zhoujiaqi) [#863](https://github.com/apache/cloudberry/pull/863)
* [`4de7cd73123`](https://github.com/apache/cloudberry/commit/4de7cd7312306c874a86f1da80036e09db4c348a) - Rename database name in test_dbconn.py to avoid mismatching (Zhang Mingli) [#857](https://github.com/apache/cloudberry/pull/857)
* [`e36838cea19`](https://github.com/apache/cloudberry/commit/e36838cea19640acef31c31f81433be0a8a26eb5) - FIX icw test from Foreign Scans (zhoujiaqi) [#839](https://github.com/apache/cloudberry/pull/839)
* [`58a65a41049`](https://github.com/apache/cloudberry/commit/58a65a41049ebf27e471d4f1987c7f63a0763380) - Fix relcache lookup in Orca when selecting from sequence (Chris Hajas) [#839](https://github.com/apache/cloudberry/pull/839)
* [`1e84aadd5ee`](https://github.com/apache/cloudberry/commit/1e84aadd5eecc4b9f6b6bada24a8f2281342afaa) - Use external table FDW in Orca instead of manually constructing external table (Chris Hajas) [#839](https://github.com/apache/cloudberry/pull/839)
* [`08702ccc5f4`](https://github.com/apache/cloudberry/commit/08702ccc5f479e17e5a6c28ed955e72cd6ed7500) - Add support for Foreign Scans in Orca (Chris Hajas) [#839](https://github.com/apache/cloudberry/pull/839)
* [`be8b5e0f930`](https://github.com/apache/cloudberry/commit/be8b5e0f9307941d09fd09be288c11d3a1fd8064) - Refactor External Scans to Foreign Scans in Orca (Chris Hajas) [#839](https://github.com/apache/cloudberry/pull/839)
* [`98bf11e160e`](https://github.com/apache/cloudberry/commit/98bf11e160eeb7197eb38fd15729e58f11bb5108) - Add the REPLACE keyword to let cred-alert ignore (Wu Ning) [#839](https://github.com/apache/cloudberry/pull/839)
* [`e727234a71a`](https://github.com/apache/cloudberry/commit/e727234a71a755fe7c46c2801a9a8e50d4649860) - Remove table Oid for DML on partition table (#14623) (gpopt) [#839](https://github.com/apache/cloudberry/pull/839)
* [`8a6f7c8f89f`](https://github.com/apache/cloudberry/commit/8a6f7c8f89ffdcc7e5112652c4267bfd1f6d462b) - Penalize hash join in case of skew (Jingyu Wang) [#839](https://github.com/apache/cloudberry/pull/839)
* [`1533deccf6a`](https://github.com/apache/cloudberry/commit/1533deccf6a42e8cdfeaf4e270489cbcb8a047ca) - Fix cherry-pick. (Zhang Mingli) [#852](https://github.com/apache/cloudberry/pull/852)
* [`87f3d033f3d`](https://github.com/apache/cloudberry/commit/87f3d033f3d6993e769e740858bf8d0da6904dca) - Escape database name for dbconn. (Zhenghua Lyu) [#852](https://github.com/apache/cloudberry/pull/852)
* [`29ee40bb388`](https://github.com/apache/cloudberry/commit/29ee40bb3882544aa8b2db596c75e5b9dc10950b) - Exclude views from missing file view check (Huansong Fu) [#852](https://github.com/apache/cloudberry/pull/852)
* [`623db83b8ea`](https://github.com/apache/cloudberry/commit/623db83b8ea80cfe47627563d45e107b5c175b2e) - Using __gp_aoseg/__gp_aocsseg in missing/orphaned file views (Huansong Fu) [#852](https://github.com/apache/cloudberry/pull/852)
* [`1f7de781f43`](https://github.com/apache/cloudberry/commit/1f7de781f434dd6a2a00c6ef90693d2664471d57) - Fix up stale gp_default_storage_options comment (#15518) (David Kimura) [#852](https://github.com/apache/cloudberry/pull/852)
* [`f4a9a6f6bea`](https://github.com/apache/cloudberry/commit/f4a9a6f6bea70365fcb259492493646d626edd0c) - fix GPDB_96_MERGE_FIXME, do not need to check forceDistRandom in set_cte_pathlist (#15527) (xuejing zhao) [#852](https://github.com/apache/cloudberry/pull/852)
* [`8c1b1367de1`](https://github.com/apache/cloudberry/commit/8c1b1367de161321a3613a7ef3688001d39114a0) - FIX BUG: unrecognized node type: 147 (7X) (#15495) (Yongtao Huang) [#852](https://github.com/apache/cloudberry/pull/852)
* [`a48ec0258f6`](https://github.com/apache/cloudberry/commit/a48ec0258f692a5272a690e02f7abaef5498dd5b) - Add test case for over-eager constraint exclusion (7X) (#15476) (Yongtao Huang) [#852](https://github.com/apache/cloudberry/pull/852)
* [`189aaf7a795`](https://github.com/apache/cloudberry/commit/189aaf7a7950f529cfb0f2a23912ae70dabbdc55) - Fix spelling errors identified by lintian (#15483) (Bradford Boyle) [#852](https://github.com/apache/cloudberry/pull/852)
* [`ab220b7b088`](https://github.com/apache/cloudberry/commit/ab220b7b08871d7b2f1ab58a634b0b404ad555b9) - modify partition_join.out to fix pipeline (#15502) (xuejing zhao) [#852](https://github.com/apache/cloudberry/pull/852)
* [`97dfb96b576`](https://github.com/apache/cloudberry/commit/97dfb96b5764760bd5fd83219255a7fcf98d9dcb) - Fix bypass catalog unittest (#15499) (RMT) [#852](https://github.com/apache/cloudberry/pull/852)
* [`218df8193f9`](https://github.com/apache/cloudberry/commit/218df8193f9607b9f6bfc21a870f2abc5eff4dbf) - Fix erroneous Valgrind markings in AllocSetRealloc. (Tom Lane) [#852](https://github.com/apache/cloudberry/pull/852)
* [`80d004563e7`](https://github.com/apache/cloudberry/commit/80d004563e7d6771c9f325d280005f76f72c3aec) - resove fixme for lateral left join in partition_join.sql (#15492) (xuejing zhao) [#852](https://github.com/apache/cloudberry/pull/852)
* [`46b79fd1f0b`](https://github.com/apache/cloudberry/commit/46b79fd1f0be0f96ac6864ccef5a17c1324020a4) - mem intensive agg should contains mixed type. (erchuan) [#852](https://github.com/apache/cloudberry/pull/852)
* [`ec848dee4ac`](https://github.com/apache/cloudberry/commit/ec848dee4ac35157a90f9e186ddcdcd4a72458be) - Improve refresh materialized view with "no data" option (wenru yan) [#852](https://github.com/apache/cloudberry/pull/852)
* [`14c8244695c`](https://github.com/apache/cloudberry/commit/14c8244695cf696009bc74caf96f33629aacad76) - Fix the legacy bug of the DatabaseFrozenIds lock (#15414) (Hongxu Ma) [#852](https://github.com/apache/cloudberry/pull/852)
* [`088cc7749e9`](https://github.com/apache/cloudberry/commit/088cc7749e9b7d97119c53f5f3e49eb66139bad1) - using 'env perl' instead of absolute path (#15493) (RMT) [#852](https://github.com/apache/cloudberry/pull/852)
* [`b5b17b93840`](https://github.com/apache/cloudberry/commit/b5b17b93840b7fe59f6a11b73726752f3f57767f) - Add system PATH to  findCmdInPath (#15477) (RMT) [#852](https://github.com/apache/cloudberry/pull/852)
* [`0b987de6039`](https://github.com/apache/cloudberry/commit/0b987de603915fd0596a3b7c63b8b192ba68bae5) - Fix colLargeRowIndexes array overflow (Jianghua Yang) [#853](https://github.com/apache/cloudberry/pull/853)
* [`ca9379e4541`](https://github.com/apache/cloudberry/commit/ca9379e4541da85b0c02ddbd90862fc5fbbb9109) - brin ao/co: Fix final partial range summarization (Soumyadeep Chakraborty) [#846](https://github.com/apache/cloudberry/pull/846)
* [`a0ab44f026e`](https://github.com/apache/cloudberry/commit/a0ab44f026e7df52f690e0b2609e6ef6fd1a1646) - brin ao/co: Improve summarize tests (Soumyadeep Chakraborty) [#846](https://github.com/apache/cloudberry/pull/846)
* [`588f5c9c6f8`](https://github.com/apache/cloudberry/commit/588f5c9c6f824eb80966678e3c1d916843771763) - brin: table AM API for block traversal (Soumyadeep Chakraborty) [#846](https://github.com/apache/cloudberry/pull/846)
* [`48a8d25182a`](https://github.com/apache/cloudberry/commit/48a8d25182a9207801aa363e5fa820ccc59c577e) - brin: Restore specific range summarization - heap (Soumyadeep Chakraborty) [#846](https://github.com/apache/cloudberry/pull/846)
* [`da87706ef11`](https://github.com/apache/cloudberry/commit/da87706ef1123c023e6f9a13f3db2d3ed5b3cfc4) - BRIN: mask BRIN_EVACUATE_PAGE for WAL consistency checking (Alvaro Herrera) [#846](https://github.com/apache/cloudberry/pull/846)
* [`dbc7a710ea6`](https://github.com/apache/cloudberry/commit/dbc7a710ea61c372d4a1b7f2bc6b3e33c505cb05) - Refactor internals to support pageinspect (#850) (reshke) [#850](https://github.com/apache/cloudberry/pull/850)
* [`3419239371a`](https://github.com/apache/cloudberry/commit/3419239371a33ed3f6596a8d9b06fc319e2556c5) - fix test cases (Zhang Mingli) [#845](https://github.com/apache/cloudberry/pull/845)
* [`2b79a1b0883`](https://github.com/apache/cloudberry/commit/2b79a1b0883a646869dad1a02a1c34e45a7ffefa) - Primary should stop waiting for replication when mirror is stopped (Alexandra Wang) [#845](https://github.com/apache/cloudberry/pull/845)
* [`89c63d90daf`](https://github.com/apache/cloudberry/commit/89c63d90daf928e0321ba7349dfe4502d86d93f1) - Add a gdb script for debugging purposes (Alexandra Wang) [#845](https://github.com/apache/cloudberry/pull/845)
* [`39e1d9efefc`](https://github.com/apache/cloudberry/commit/39e1d9efefccb2f825d54cb8bfe229441f2f6ddb) - set bypass query's memory limit to statement_mem (wenru yan) [#845](https://github.com/apache/cloudberry/pull/845)
* [`a8f37a3a6e3`](https://github.com/apache/cloudberry/commit/a8f37a3a6e343c990fd0824c3b1d78e52d13796b) - Fix another flaky aggregates ICW test (#15433) (Chris Hajas) [#845](https://github.com/apache/cloudberry/pull/845)
* [`27ef32832b0`](https://github.com/apache/cloudberry/commit/27ef32832b0d551a9f91a78802ef54fa386454da) - Merge redundant test cases in regress tests partition (partition1|Huansong Fu) [#845](https://github.com/apache/cloudberry/pull/845)
* [`977ff769ce1`](https://github.com/apache/cloudberry/commit/977ff769ce1734eaafcf555b6cd6566ae347903f) - Free the dbpath string when it becomes unnecessary (Andrey Sokolov) [#845](https://github.com/apache/cloudberry/pull/845)
* [`170a45f75ef`](https://github.com/apache/cloudberry/commit/170a45f75ef43411b5ff76bcc2c8fdc4f52a9a35) - Fix mirror checkpointer error on the alter database query (Andrey Sokolov) [#845](https://github.com/apache/cloudberry/pull/845)
* [`6413927d1ae`](https://github.com/apache/cloudberry/commit/6413927d1ae40db09825488ef11b747be4114692) - Do not trigger fault in dtx recovery process except a few (Huansong Fu) [#845](https://github.com/apache/cloudberry/pull/845)
* [`f9ec484db1c`](https://github.com/apache/cloudberry/commit/f9ec484db1c934a408dd1dd68e529f01d68af93f) - FIX BUG: apply get_ao_compression_ratio() to a root partitioned table with AO children (7X) (#15390) (Yongtao Huang) [#845](https://github.com/apache/cloudberry/pull/845)
* [`21af1e9d620`](https://github.com/apache/cloudberry/commit/21af1e9d6205f7832d9e856771ec14d31a55c130) - Fix InterruptHoldoffCount not being reset issue (#15279) (1mmortal) [#845](https://github.com/apache/cloudberry/pull/845)
* [`a8bb21bd6b3`](https://github.com/apache/cloudberry/commit/a8bb21bd6b36594fc946d9bbe9713a14293beb0d) - Ban enums as distribution and partition keys (Andrew Repp) [#845](https://github.com/apache/cloudberry/pull/845)
* [`06c2345cdde`](https://github.com/apache/cloudberry/commit/06c2345cdde0aae57a517fee3051287502730117) - Fix typo. (Zhang Mingli) [#845](https://github.com/apache/cloudberry/pull/845)
* [`86ccd8c5e30`](https://github.com/apache/cloudberry/commit/86ccd8c5e305f25358bd48fe8c43cd825baa40e0) - Revive previously defunct autovacuum GUCs (Brent Doil) [#845](https://github.com/apache/cloudberry/pull/845)
* [`47aa78fa5ec`](https://github.com/apache/cloudberry/commit/47aa78fa5ec53f70df1e2abbdfefff107ec5779b) - Remove dead code related to memtuples, as no more stored hashtables (Ashwin Agrawal) [#845](https://github.com/apache/cloudberry/pull/845)
* [`b471d413fb7`](https://github.com/apache/cloudberry/commit/b471d413fb750429a5c7146fddd09ad09d2d0ed3) - Remove unused memtuple_binding code for AOCO (Ashwin Agrawal) [#845](https://github.com/apache/cloudberry/pull/845)
* [`b25f1a68649`](https://github.com/apache/cloudberry/commit/b25f1a68649f3ba89784b4bfae29cac089bfb485) - Initialize capability of default resource queue in .dat file (Huansong Fu) [#845](https://github.com/apache/cloudberry/pull/845)
* [`ad450a64803`](https://github.com/apache/cloudberry/commit/ad450a648039e1c2bd8cea84400a49018f7958c0) - Make remapper->typmodmap alloc in remapper->mycontext. (Jianghua Yang) [#851](https://github.com/apache/cloudberry/pull/851)
* [`563b7ca024f`](https://github.com/apache/cloudberry/commit/563b7ca024fbbecec2b531595312ce20541cf2f8) - gpcheckcat: Avoid false positives in vpinfo check (Soumyadeep Chakraborty) [#847](https://github.com/apache/cloudberry/pull/847)
* [`2a77533e73e`](https://github.com/apache/cloudberry/commit/2a77533e73e9e61a70343159a7da466677a50ca2) - gpcheckcat: Replace expensive pg_attribute query (Soumyadeep Chakraborty) [#847](https://github.com/apache/cloudberry/pull/847)
* [`1e2d348e937`](https://github.com/apache/cloudberry/commit/1e2d348e9374ba78855a33ac55e62cb60c0b84c7) - change IC_PROXY_INVALID_CONTENT to int16 (#15219) (Hongxu Ma) [#847](https://github.com/apache/cloudberry/pull/847)
* [`3e832f1c06e`](https://github.com/apache/cloudberry/commit/3e832f1c06e1ee61ce846f97ebf0be534d1f7604) - Fix obsolete references to SnapshotNow in comment. (#15189) (Xiaoran Wang) [#847](https://github.com/apache/cloudberry/pull/847)
* [`48865dbc01a`](https://github.com/apache/cloudberry/commit/48865dbc01a96d7467d76ffaf5e7653e1da9fb02) - gpexpand: Fix tables not copied to new segments (Nihal Jain) [#847](https://github.com/apache/cloudberry/pull/847)
* [`f30f66ba5fa`](https://github.com/apache/cloudberry/commit/f30f66ba5fa95e5f41286a7f26f3b37188deb236) - Fix pullup error when the target list contains RelabelType node (#15173) (dreamedcheng) [#847](https://github.com/apache/cloudberry/pull/847)
* [`eb442939e95`](https://github.com/apache/cloudberry/commit/eb442939e95a234bf457a2ceb02d7c841bfd1599) - Fix index DDL operations are recorded in QEs' pg_last_stat_operation (#14822) (dreamedcheng) [#847](https://github.com/apache/cloudberry/pull/847)
* [`957d7158693`](https://github.com/apache/cloudberry/commit/957d7158693d02daa6063c2b0318585b9c2dc59b) - Add filenum field to pg_attribute_encoding (Divyesh Vanjare) [#847](https://github.com/apache/cloudberry/pull/847)
* [`c56911bd19b`](https://github.com/apache/cloudberry/commit/c56911bd19bb3a1e7ee9f7a8b88cefc8f3464d1d) - Prevent function execution which access distributed table on entrydb query executor. (m7onov) [#847](https://github.com/apache/cloudberry/pull/847)
* [`f9dbcda775b`](https://github.com/apache/cloudberry/commit/f9dbcda775b1dedbf346f3c10372393bae26704c) - Allow GRANT on pg_log_backend_memory_contexts(). (Jeff Davis) [#847](https://github.com/apache/cloudberry/pull/847)
* [`9a52f7e478a`](https://github.com/apache/cloudberry/commit/9a52f7e478a33804772a6fbefd41b5cadba536a6) - Fix format error of the lib name on Mac m1 (#15140) (Jermy Li) [#847](https://github.com/apache/cloudberry/pull/847)
* [`ed99c719402`](https://github.com/apache/cloudberry/commit/ed99c71940275925c2ed57362da3ddccf43261e1) - Avoid syncscan for vacuum full and cluster for catalog tables (Ashwin Agrawal) [#847](https://github.com/apache/cloudberry/pull/847)
* [`15a9683ee32`](https://github.com/apache/cloudberry/commit/15a9683ee320c4ccc64d785d7ebebb28529552ed) - remove resGroupId check in runaway detector (wenru yan) [#847](https://github.com/apache/cloudberry/pull/847)
* [`cfa6d829ce8`](https://github.com/apache/cloudberry/commit/cfa6d829ce88286bc6b551d273927586efa2300f) - AO/CO: reword misleading comment (Alexandra Wang) [#847](https://github.com/apache/cloudberry/pull/847)
* [`4c15d6b333a`](https://github.com/apache/cloudberry/commit/4c15d6b333a6ec3bcd70ab5e6d5111e5d0931eaa) - add permission check to file /sys/fs/cgroup/cgroup.procs (#15133) (Zhenglong Li) [#847](https://github.com/apache/cloudberry/pull/847)
* [`438a6a6a698`](https://github.com/apache/cloudberry/commit/438a6a6a698e90fa71be08ffd55f0bcfe7e1e9a8) - Fix ic_proxy compilation for when HOST_NAME_MAX is unavailable (Jimmy Yih) [#847](https://github.com/apache/cloudberry/pull/847)
* [`d5767f0211d`](https://github.com/apache/cloudberry/commit/d5767f0211d75cf20e378aa88afbb044b54da617) - Update Orca explain pipeline for rhel8 changes (#14585) (Chris Hajas) [#834](https://github.com/apache/cloudberry/pull/834)
* [`3139828797a`](https://github.com/apache/cloudberry/commit/3139828797a25fedd55bde71a176b55bcaed8334) - ORCA: Fix data corruption error for domain (#14457) (Ekta Khanna) [#834](https://github.com/apache/cloudberry/pull/834)
* [`df143a59e13`](https://github.com/apache/cloudberry/commit/df143a59e138dfc93696e6a29b18c7390d67d6d9) - Update Orca test pipeline to use rhel8 (#14567) (Chris Hajas) [#834](https://github.com/apache/cloudberry/pull/834)
* [`0d578b9fc42`](https://github.com/apache/cloudberry/commit/0d578b9fc42b1b4042b6640d5f79ef2c81ecb717) - Assign different Mdid types to Relation, Index and Constraint to avoid Oid conflict (#14411) (gpopt) [#834](https://github.com/apache/cloudberry/pull/834)
* [`8da76608f49`](https://github.com/apache/cloudberry/commit/8da76608f496d12e978720db4e20bd58ca50d363) - Add hooks for extensiona, allow custom Table-AM to implement custom xlog (#848) (Xun Gong) [#848](https://github.com/apache/cloudberry/pull/848)
* [`cdbc1f79d1c`](https://github.com/apache/cloudberry/commit/cdbc1f79d1c37822dcdb3869aca1185615f1efa6) - Optimize code of IVM and fix some typos (#833) (Yongtao Huang) [#833](https://github.com/apache/cloudberry/pull/833)
* [`82a3576d12c`](https://github.com/apache/cloudberry/commit/82a3576d12cda9ef382af6593553249600e86c98) - Refactor SaveOidAssignments and RestoreOidAssignments logic. (Zhenghua Lyu) [#844](https://github.com/apache/cloudberry/pull/844)
* [`6cc27ae78f7`](https://github.com/apache/cloudberry/commit/6cc27ae78f75d38b80d1e654f8973e15c406d1b2) - Declare gp_gettmid as an extern function (#14498) (Yini Li) [#844](https://github.com/apache/cloudberry/pull/844)
* [`5cba6e1d3a4`](https://github.com/apache/cloudberry/commit/5cba6e1d3a440c6961f303828400f950df74f154) - Fix cumulative statistics collection for AO aux tables (Andrew Repp) [#844](https://github.com/apache/cloudberry/pull/844)
* [`154428304e5`](https://github.com/apache/cloudberry/commit/154428304e5536c7d78763e43beb3af2d8d15671) - Add assert to the length of shared hash table name (#14163) (Xiaoran Wang) [#844](https://github.com/apache/cloudberry/pull/844)
* [`9581fa61b75`](https://github.com/apache/cloudberry/commit/9581fa61b75dec8ef8823303b1dfb4ac4afe2f74) - Fix syntax error with CREATE MATERIALIZED VIEW (Brent Doil) [#844](https://github.com/apache/cloudberry/pull/844)
* [`df53aab6ba0`](https://github.com/apache/cloudberry/commit/df53aab6ba0b034c57eee85f056bba060cd1f9cb) - Add test case for reloptions when adding child partition w/ different AM than parent (xuqi.wxq) [#844](https://github.com/apache/cloudberry/pull/844)
* [`5d26f07ae28`](https://github.com/apache/cloudberry/commit/5d26f07ae2840e18823fdaab92ac089cf81faec4) - If QEs hit errors in explain analyze, rethrow the error before ExplainPrintPlan (#14588) (Divyesh Vanjare) [#844](https://github.com/apache/cloudberry/pull/844)
* [`bf0b823d8e1`](https://github.com/apache/cloudberry/commit/bf0b823d8e11c43a6c21f7a4a5ab27984f78d0e0) - pfree() the pstrdup()'d string userDoption (Adam Lee) [#844](https://github.com/apache/cloudberry/pull/844)
* [`85d36bf0a43`](https://github.com/apache/cloudberry/commit/85d36bf0a4365190b81386e3d9a1c96b7123a55f) - fix the issue of cannot create temporary table like existing table with comments (#14742) (Zhenglong Li) [#844](https://github.com/apache/cloudberry/pull/844)
* [`3d66a5074eb`](https://github.com/apache/cloudberry/commit/3d66a5074eb5007314d89c6ee0e765c1978dac8e) - Fix a flaky test case querying pg_class (Adam Lee) [#844](https://github.com/apache/cloudberry/pull/844)
* [`f5f5e3ff445`](https://github.com/apache/cloudberry/commit/f5f5e3ff445316ae8398536b7f7db5cc27bed415) - Add ZSTD compression support for gpfdist writable external table (#14250) (HouLei) [#844](https://github.com/apache/cloudberry/pull/844)
* [`3252ffd5002`](https://github.com/apache/cloudberry/commit/3252ffd500297e13c148c82abd35e49f5666bc16) - fix delete with split update (#15288) (Haotian Chen) [#838](https://github.com/apache/cloudberry/pull/838)
* [`68c758bd439`](https://github.com/apache/cloudberry/commit/68c758bd4394c6c16563e1e69e4bf1785b5e04cc) - fix tids are not in order when building bitmap index (#15271) (Zhenglong Li) [#838](https://github.com/apache/cloudberry/pull/838)
* [`8ef6dec04d2`](https://github.com/apache/cloudberry/commit/8ef6dec04d22b69b53fa2a683a42b1db885de444) - Fix bug of upstream merged feature COMMIT AND CHAIN in GPDB (#14179) (Haotian Chen) [#838](https://github.com/apache/cloudberry/pull/838)
* [`3b2274fd3d9`](https://github.com/apache/cloudberry/commit/3b2274fd3d908794c6ade2e3a2c43587342f7be1) - using TRUSTED_SHELL in gpcreateseg.sh (#15269) (RMT) [#838](https://github.com/apache/cloudberry/pull/838)
* [`65808fbde38`](https://github.com/apache/cloudberry/commit/65808fbde38431fff496f57cd135dc9f1f412511) - Connect as utility in SET_LOCALE_VARS_BASED_ON_COORDINATOR() (Adam Lee) [#838](https://github.com/apache/cloudberry/pull/838)
* [`09ae0737d7e`](https://github.com/apache/cloudberry/commit/09ae0737d7e71fba00abab770aad47637a64fdf2) - Comment codes about mop_high_watermark of pg_conn (Adam Lee) [#838](https://github.com/apache/cloudberry/pull/838)
* [`5d702b1b96b`](https://github.com/apache/cloudberry/commit/5d702b1b96ba380213a8afc9475b7464a4a026bf) - Remove element connofs from PQconninfoOption (Adam Lee) [#838](https://github.com/apache/cloudberry/pull/838)
* [`c9d303778e7`](https://github.com/apache/cloudberry/commit/c9d303778e7ecfd7be879ba913cd993b0c609dda) - Removed dead code in DistributedSnapshot_Copy (#15205) (FairyFar) [#838](https://github.com/apache/cloudberry/pull/838)
* [`c287df77694`](https://github.com/apache/cloudberry/commit/c287df7769416bbd58c98c5d7034995fa5082298) - Fix typo allocatd -> allocated (#15272) (Mr Dk) [#838](https://github.com/apache/cloudberry/pull/838)
* [`a06d6fe8b6f`](https://github.com/apache/cloudberry/commit/a06d6fe8b6fdf5e3c617f3b57aee6ee5c2540cdb) - gpstop: Fix kill_9_segment_processes function (Nihal Jain) [#838](https://github.com/apache/cloudberry/pull/838)
* [`a03d2b857a9`](https://github.com/apache/cloudberry/commit/a03d2b857a9b240326ad47560db3191f47cf503c) - FIXME enable 64bit bitmapset and update visimap (#14784) (Yao Wang) [#843](https://github.com/apache/cloudberry/pull/843)
* [`6584d1bfe08`](https://github.com/apache/cloudberry/commit/6584d1bfe08da124ddeb139d5c17ad8e30fc02a2) - fix: Add bounds checking for aggregate filter array access (Jianghua Yang) [#843](https://github.com/apache/cloudberry/pull/843)
* [`3ad23219007`](https://github.com/apache/cloudberry/commit/3ad23219007416e939a092a2dc730695d5c4883c) - Fix pg_rewind when log is a symlink (Soumyadeep Chakraborty) [#843](https://github.com/apache/cloudberry/pull/843)
* [`aed1520d29f`](https://github.com/apache/cloudberry/commit/aed1520d29ffed4c73abb47ba7f8e11cefe8a596) - Fix hang of multi-dqa with filter in planner (#14950) (QingMa) [#843](https://github.com/apache/cloudberry/pull/843)
* [`226a8ef8949`](https://github.com/apache/cloudberry/commit/226a8ef8949c39229736647fc2d875e76183a1b5) - In binary upgrade, dump the encoding clause for dropped columns (Brent Doil) [#843](https://github.com/apache/cloudberry/pull/843)
* [`6afffa97e10`](https://github.com/apache/cloudberry/commit/6afffa97e1012a639216b2432a12dbfbc690b901) - Get the case qp_functions_in_subquery_constant back (Adam Lee) [#843](https://github.com/apache/cloudberry/pull/843)
* [`e0a82bf94ab`](https://github.com/apache/cloudberry/commit/e0a82bf94abe16b3d43eb3ba6b46f8374d910191) - gpinitsystem: fix bash syntax when remote locale is incorrect (#15053) (Sasasu) [#843](https://github.com/apache/cloudberry/pull/843)
* [`25c8b9de06d`](https://github.com/apache/cloudberry/commit/25c8b9de06dea9c32705d915ab98160d65fd95f2) - FTS: remove dead code and fix some typo (Junwang Zhao) [#843](https://github.com/apache/cloudberry/pull/843)
* [`6262846cac4`](https://github.com/apache/cloudberry/commit/6262846cac441e9f8a1f7888d240ebc4e93313dc) - Resolve wrong result when direct dispatch, If opno of clause does not belong to opfamily of distributed key(#14977) (xuejing zhao) [#843](https://github.com/apache/cloudberry/pull/843)
* [`a1c2d445fae`](https://github.com/apache/cloudberry/commit/a1c2d445faec16048aa6ddf8ac4aa39317e01af7) - Remove FIXME related to bgwriter in sample config (Ashwin Agrawal) [#843](https://github.com/apache/cloudberry/pull/843)
* [`41635102925`](https://github.com/apache/cloudberry/commit/416351029259afe37c1009235a5d870d9b1ab289) - Resolve merge fixmes from syncrep.c (Ashwin Agrawal) [#843](https://github.com/apache/cloudberry/pull/843)
* [`b832648425c`](https://github.com/apache/cloudberry/commit/b832648425c4c627c27ce64ac4d8fc3500134718) - Remove FIXME about RelationIs*() macros (Ashwin Agrawal) [#843](https://github.com/apache/cloudberry/pull/843)
* [`130a7b2ec1f`](https://github.com/apache/cloudberry/commit/130a7b2ec1fd92ad65471318db3ccfc142b95b30) - Remove fixme from appendonly_index_validate_scan() (Ashwin Agrawal) [#843](https://github.com/apache/cloudberry/pull/843)
* [`d58fde97419`](https://github.com/apache/cloudberry/commit/d58fde9741950fca532455bd222979c93219bbb5) - Double gpfdist listening to one port (HouLei) [#843](https://github.com/apache/cloudberry/pull/843)
* [`2e922468d61`](https://github.com/apache/cloudberry/commit/2e922468d61096f3477a279f71439494b79c1b62) - Fix LWLockHeldByMe assert failure in SharedSnapshotDump (#15007) (dreamedcheng) [#843](https://github.com/apache/cloudberry/pull/843)
* [`13408655543`](https://github.com/apache/cloudberry/commit/1340865554399a9910ad7efb020bd90f9f54be23) - Modify 019_replslot_limit.pl test for Greenplum, take 2 (Alexandra Wang) [#843](https://github.com/apache/cloudberry/pull/843)
* [`e1d4b817d9e`](https://github.com/apache/cloudberry/commit/e1d4b817d9eb0537a3ec84499624124d1d091e79) - [7X] Skip the foreign key constraint validation. (#14344) (Xing Guo) [#843](https://github.com/apache/cloudberry/pull/843)
* [`a3590d212be`](https://github.com/apache/cloudberry/commit/a3590d212be25c8c64995db92050caf29042b43a) - Remove merge fixme from isolation2 setup test (Ashwin Agrawal) [#843](https://github.com/apache/cloudberry/pull/843)
* [`874ec50997d`](https://github.com/apache/cloudberry/commit/874ec50997d1119a6a6831117d59af3aa2766c01) - Remove merge fixme from transformGpPartitionDefinition() (Ashwin Agrawal) [#843](https://github.com/apache/cloudberry/pull/843)
* [`9b5a8a3d356`](https://github.com/apache/cloudberry/commit/9b5a8a3d35664aed514192ed581b1592723d0b1c) - Remove extra `InvalidateCatalogSnapshotConditionally` call (Marbin Tan) [#843](https://github.com/apache/cloudberry/pull/843)
* [`c7397dc33f3`](https://github.com/apache/cloudberry/commit/c7397dc33f3af5b07fe2aebb918d513aeb02d362) - Add magic number field in the ICProxyPkt (#14926) (Hongxu Ma) [#831](https://github.com/apache/cloudberry/pull/831)
* [`7158e52a99b`](https://github.com/apache/cloudberry/commit/7158e52a99bd21d249dce08a750e8176496f6a84) - inclusive terminology for Greenplum 7 in gpfdist (#14987) (hyongtao-db) [#831](https://github.com/apache/cloudberry/pull/831)
* [`5c214df020c`](https://github.com/apache/cloudberry/commit/5c214df020c8da3508751560cf87ab4c785c8ba2) - Inclusive Terminology for Greenplum 7 in gpload (#14979) (hyongtao-db) [#831](https://github.com/apache/cloudberry/pull/831)
* [`30d54291280`](https://github.com/apache/cloudberry/commit/30d542912808f5d91578546edce47f3b46bd506b) - No need pstrdup to initialize the dispatched query string (Huansong Fu) [#831](https://github.com/apache/cloudberry/pull/831)
* [`6dcf8c9bffe`](https://github.com/apache/cloudberry/commit/6dcf8c9bffe2fce4404918853196f2acfe8f0c6b) - correct fmtopts column format of pg_exttable (#14937) (Huiliang.liu) [#831](https://github.com/apache/cloudberry/pull/831)
* [`9c5f9652455`](https://github.com/apache/cloudberry/commit/9c5f9652455c7c3b9cb38a0ad3e2ad976374487e) - Fix the failed unit test case (#14930) (Zhenglong Li) [#831](https://github.com/apache/cloudberry/pull/831)
* [`934f513db43`](https://github.com/apache/cloudberry/commit/934f513db4391a51b47947287107acfb77558b01) - Set the default value for option execute_on of gp_exttable_fdw (#14797) (Jingwen Yang) [#831](https://github.com/apache/cloudberry/pull/831)
* [`12ba449dbdf`](https://github.com/apache/cloudberry/commit/12ba449dbdf6202762e83238cd34af12c1a7154c) - Test parallel retrieve cursor can be closed if sender wait in WatLatch when MQ has no space (#14925) (xuejing zhao) [#831](https://github.com/apache/cloudberry/pull/831)
* [`a7cf6d679f9`](https://github.com/apache/cloudberry/commit/a7cf6d679f91dc70ce0cb119a08225f1277becdc) - Fix flaky bfv_partition_plans test (Chris Hajas) [#831](https://github.com/apache/cloudberry/pull/831)
* [`03732b43730`](https://github.com/apache/cloudberry/commit/03732b43730a9433c7d7f6c000b1e7748f3d9147) - Remove inaccurate FIXME message (Jingyu Wang) [#831](https://github.com/apache/cloudberry/pull/831)
* [`f2eafed28d3`](https://github.com/apache/cloudberry/commit/f2eafed28d340cb30a1b4371246f02ebc2a9d312) - FIXME: Remove redundant Get/SetStaticPruneResult (Jingyu Wang) [#831](https://github.com/apache/cloudberry/pull/831)
* [`c3926f05bea`](https://github.com/apache/cloudberry/commit/c3926f05beabfdeb757fd2ad03b20c5aea83fce4) - test/regress: fix misleading error message (HustonMmmavr) [#831](https://github.com/apache/cloudberry/pull/831)
* [`c6b87f73552`](https://github.com/apache/cloudberry/commit/c6b87f73552b3c0d1a79bb3e8380dc31d920309a) - FIXME: It looks like PostgreSQL planner pruning feature does not work properly (Tao-Ma) [#828](https://github.com/apache/cloudberry/pull/828)
* [`64d680293b4`](https://github.com/apache/cloudberry/commit/64d680293b4415591a870c695c2df800e1b59115) - Temporary change the qp_dropped_cols expected file (Tao-Ma) [#828](https://github.com/apache/cloudberry/pull/828)
* [`202225ea115`](https://github.com/apache/cloudberry/commit/202225ea11514feb4c664ba8da34d2102ac7866c) - Add test for pg_attribute_encoding entries with various AOCS table creation syntax (Huansong Fu) [#828](https://github.com/apache/cloudberry/pull/828)
* [`78ebf55f128`](https://github.com/apache/cloudberry/commit/78ebf55f1287b22ca31555617abce29b77f9115c) - Add test case for fix wrong results caused by over-eager constraint exclusion (#14592) (xuejing zhao) [#828](https://github.com/apache/cloudberry/pull/828)
* [`68cba7eda18`](https://github.com/apache/cloudberry/commit/68cba7eda1833d36b2e0f69aae6808aa20fc5332) - (main only): fix wrong results caused by over-eager constraint exclusion (#14553) (xuejing zhao) [#828](https://github.com/apache/cloudberry/pull/828)
* [`0d5eec6f9ba`](https://github.com/apache/cloudberry/commit/0d5eec6f9ba9b31f1f9de083187f0e6e29b3327a) - fix compiler warning caused by gpfdist compression external table (#14599) (HouLei) [#828](https://github.com/apache/cloudberry/pull/828)
* [`da1f254b828`](https://github.com/apache/cloudberry/commit/da1f254b8289b4fbb3c1d63ce00361c7fd0a449b) - Fix failure when DynamicSeqScan has a subPlan #14505 (Evgeniy Ratkov) [#828](https://github.com/apache/cloudberry/pull/828)
* [`bc9cc1c0a72`](https://github.com/apache/cloudberry/commit/bc9cc1c0a7260bfb68f70fdd78cdc186eaf26cab) - Add stream zstd compress for gpfdist to gpdb7 (#14144) (HouLei) [#828](https://github.com/apache/cloudberry/pull/828)
* [`1a56de3f07d`](https://github.com/apache/cloudberry/commit/1a56de3f07d6e95ddcaf04dbd51a8c7484cc422a) - Ensure that an active outer snapshot exists prior to executing SPI (Adam Lee) [#828](https://github.com/apache/cloudberry/pull/828)
* [`6784abd9f38`](https://github.com/apache/cloudberry/commit/6784abd9f389c429c1cba35522ffa3651c8a3845) - Fix the test extension to execute SQL code inside of a Portal (#14515) (Adam Lee) [#828](https://github.com/apache/cloudberry/pull/828)
* [`b478dc20b18`](https://github.com/apache/cloudberry/commit/b478dc20b18e9df80659434e57b69b89cbfef990) - Fix checkpoint wal replay failed issue (Wang Weinan) [#828](https://github.com/apache/cloudberry/pull/828)
* [`4c61c98deb5`](https://github.com/apache/cloudberry/commit/4c61c98deb53974d5423fb44909d7144f88b7f2a) - Remove two not used elements of Agg (Adam Lee) [#828](https://github.com/apache/cloudberry/pull/828)
* [`fa7f4c697f0`](https://github.com/apache/cloudberry/commit/fa7f4c697f01c500d9c0ed61357adeeffe1c078b) - Minor changes to align with the upstream (Adam Lee) [#828](https://github.com/apache/cloudberry/pull/828)
* [`ef4725ab755`](https://github.com/apache/cloudberry/commit/ef4725ab755c38a5ccbed3649cebf238d575729d) - Acquire sample rows for root partition to build extend statistics (QingMa) [#828](https://github.com/apache/cloudberry/pull/828)
* [`63f7c13ef38`](https://github.com/apache/cloudberry/commit/63f7c13ef38b0110adc9352f971bc231c3957a8b) - src/timezone/zic.c: handle strdup failure (#836) (Ilya Shipitsin) [#836](https://github.com/apache/cloudberry/pull/836)
* [`e9dc7c39568`](https://github.com/apache/cloudberry/commit/e9dc7c3956822c4dd6baccba81785c843c021cf5) - CI: run the parallel regression tests with a user-specified limit on the number of simultaneous connections. (Jianghua Yang) [#835](https://github.com/apache/cloudberry/pull/835)
* [`3cd299d9b0e`](https://github.com/apache/cloudberry/commit/3cd299d9b0e2e04180c3d38e89f293a458bbaf2a) - Support create directory table with location. (zhangwenchao) [#798](https://github.com/apache/cloudberry/pull/798)
* [`fa79ed67e59`](https://github.com/apache/cloudberry/commit/fa79ed67e597bc0946637ed95c99c997e86325f8) - Ban reindexdb --concurrently (Soumyadeep Chakraborty) [#832](https://github.com/apache/cloudberry/pull/832)
* [`d71f6160828`](https://github.com/apache/cloudberry/commit/d71f6160828dc5467e90e7999673eeef075d9bd6) - Move stats related tests under disable_autovacuum for validity of index's reltuples. (Haolin Wang) [#832](https://github.com/apache/cloudberry/pull/832)
* [`29b33b29b31`](https://github.com/apache/cloudberry/commit/29b33b29b3175e44c008ac50e27ebb971f395f27) - SIGPIPE during ending not exhausted external table scan (#14020) (Georgy Shelkovy) [#832](https://github.com/apache/cloudberry/pull/832)
* [`d9888d7b3b5`](https://github.com/apache/cloudberry/commit/d9888d7b3b538c86d80820b1e3c310015ddf2239) - Remove duplicate headerfile (Himanshu Pandey) [#832](https://github.com/apache/cloudberry/pull/832)
* [`7979b1cbe63`](https://github.com/apache/cloudberry/commit/7979b1cbe63b16b9b99956a44c183eb3607b957c) - Fix incorrect distkey when copy partitions on segment. (#14502) (Xing Guo) [#832](https://github.com/apache/cloudberry/pull/832)
* [`339eebdd4eb`](https://github.com/apache/cloudberry/commit/339eebdd4eb4ca6ae6f227e7f9830e038b61af52) - Remove FIXME in test index_constraint_naming_upgrade (Huansong Fu) [#832](https://github.com/apache/cloudberry/pull/832)
* [`b6e9971f2ae`](https://github.com/apache/cloudberry/commit/b6e9971f2aebb63dd1a9cc025a8ab286ea43c7b6) - ao/co index build scans: Only use SnapshotAny (Soumyadeep Chakraborty) [#832](https://github.com/apache/cloudberry/pull/832)
* [`41b93eebbc2`](https://github.com/apache/cloudberry/commit/41b93eebbc22e3efd45f5801cd4287dd930ffecf) - Fix icw test from "Cherry-Pick CUBE grouping set, predicates from subquery ..." (zhoujiaqi) [#830](https://github.com/apache/cloudberry/pull/830)
* [`5fd7ed78e97`](https://github.com/apache/cloudberry/commit/5fd7ed78e976ae54919fe5cd961b66ef53ec1368) - ORCA: Support multiple grouping sets distinct (zhoujiaqi) [#830](https://github.com/apache/cloudberry/pull/830)
* [`e1ea1cf19a4`](https://github.com/apache/cloudberry/commit/e1ea1cf19a469f328380ded6f7b081963e301b43) - [ORCA] Enable multiple grouping set specs (#14366) (David Kimura) [#830](https://github.com/apache/cloudberry/pull/830)
* [`04a3db6b3ff`](https://github.com/apache/cloudberry/commit/04a3db6b3ff79911a1069d5ce2a01d1946d025bc) - Fix CBitSet Intersection logic in Orca (#14379) (Chris Hajas) [#830](https://github.com/apache/cloudberry/pull/830)
* [`544c11e876c`](https://github.com/apache/cloudberry/commit/544c11e876c6df41a1d84a8b050bebe76968d733) - Fix query preprocessor for nested Select-Project-NaryJoin (Ekta Khanna) [#830](https://github.com/apache/cloudberry/pull/830)
* [`5f220d245b9`](https://github.com/apache/cloudberry/commit/5f220d245b9616e36371e8bf93fefe7f54bc507f) - Rename branches in Orca CI to main (#14401) (Chris Hajas) [#830](https://github.com/apache/cloudberry/pull/830)
* [`cc6a521ae8d`](https://github.com/apache/cloudberry/commit/cc6a521ae8d150ea2613514f54942c55f51f67ae) - [ORCA] Enable CUBE result grouping set  (#14329) (David Kimura) [#830](https://github.com/apache/cloudberry/pull/830)
* [`153ba3b0907`](https://github.com/apache/cloudberry/commit/153ba3b0907c4b43a00ea1f02ab04584e80b89ca) - Remove "trigger" from the ORCA code (Jingyu Wang) [#830](https://github.com/apache/cloudberry/pull/830)
* [`87aa7a88793`](https://github.com/apache/cloudberry/commit/87aa7a887932e4d23d56e1b83a2318973c4ce2ca) - Remove obsolete partition functions and declaration in ORCA (Jingyu Wang) [#830](https://github.com/apache/cloudberry/pull/830)
* [`e5269ff959b`](https://github.com/apache/cloudberry/commit/e5269ff959b7fb9c1c2a2c358c2360e03aa239c8) - Orca supports propagating predicates from EXISTS/ANY subquery to outer relation (#14098) (gpopt) [#830](https://github.com/apache/cloudberry/pull/830)
* [`ebd236e0034`](https://github.com/apache/cloudberry/commit/ebd236e003487a8bcd68ff2daf75d3942aa4d93c) - Simplify Nary join with inner joins if predicate is false in Orca (#14284) (Chris Hajas) [#830](https://github.com/apache/cloudberry/pull/830)
* [`85d7ba83f87`](https://github.com/apache/cloudberry/commit/85d7ba83f87335b2a45f1652da743552dd3b30bf) - Patch handling of composite type returning TVF that evaluates to const (#14283) (THANATOSLAVA) [#830](https://github.com/apache/cloudberry/pull/830)
* [`1f8095fd67d`](https://github.com/apache/cloudberry/commit/1f8095fd67d69508206d4423c80286f3bb6c3d2d) - Comment out assertion in CBucket::SplitAndMergeBuckets (#14272) (gpopt) [#830](https://github.com/apache/cloudberry/pull/830)
* [`dfa9bb5bc0f`](https://github.com/apache/cloudberry/commit/dfa9bb5bc0f3d45f87b1ff4694fdb4610171ee78) - Allow direct dispatch when filtering on gp_segment_id (hari krishna) [#830](https://github.com/apache/cloudberry/pull/830)
* [`7ffe21e3b77`](https://github.com/apache/cloudberry/commit/7ffe21e3b774e4a55717f8bcddeebed2cc2b3b93) - [ORCA] Fix window frame translator related FIXMEs (#14191) (David Kimura) [#830](https://github.com/apache/cloudberry/pull/830)
* [`1fb1701ab77`](https://github.com/apache/cloudberry/commit/1fb1701ab77100fc86ad5ae6c8dd6072b6ab67ac) - Move temp files to results dir to avoid untracked files. (#15832) (Zhang Mingli) [#793](https://github.com/apache/cloudberry/pull/793)
* [`aec3384edd4`](https://github.com/apache/cloudberry/commit/aec3384edd4fd96cffbe2d5ddeee4962d1382bc9) - Fix related code in copyfrom.c (Yongtao Huang) [#793](https://github.com/apache/cloudberry/pull/793)
* [`99bd992a1c9`](https://github.com/apache/cloudberry/commit/99bd992a1c9cc87d0f878743572aa6860c52792a) - FIX BUG: COPY FORM doesn't throw out ERROR: extra data after last expected column (7X) (#15612) (Yongtao Huang) [#793](https://github.com/apache/cloudberry/pull/793)
* [`a12d2d4b15c`](https://github.com/apache/cloudberry/commit/a12d2d4b15c29bbdf53bdba09911de7a4953680b) - Fix test cases of Cherry-pick. (Zhang Mingli) [#826](https://github.com/apache/cloudberry/pull/826)
* [`c8988399da7`](https://github.com/apache/cloudberry/commit/c8988399da7a6a33ae161824509f1c33c36003f1) - Fix flaky test 'partition' (Huansong Fu) [#826](https://github.com/apache/cloudberry/pull/826)
* [`8a168bf5027`](https://github.com/apache/cloudberry/commit/8a168bf50273bb5c7ff23f5de14953775db4fa1c) - remove prev_memtuple_len (Haolin Wang) [#826](https://github.com/apache/cloudberry/pull/826)
* [`e48d8e7ad6b`](https://github.com/apache/cloudberry/commit/e48d8e7ad6bd5e0c8f2f0f2b9126fabd1a494fce) - Suppress memset() in memtuple_form_to() for performance. (Haolin Wang) [#826](https://github.com/apache/cloudberry/pull/826)
* [`dcbf5e58bd7`](https://github.com/apache/cloudberry/commit/dcbf5e58bd7ed3c1b5d80900c3915506cd747ffc) - Add .gitignore entries for distributed_snapshot test (Huansong Fu) [#826](https://github.com/apache/cloudberry/pull/826)
* [`d15bfdd5461`](https://github.com/apache/cloudberry/commit/d15bfdd5461d2c64dbf6665a6e40efff82379a4d) - Fix possible mirror startup failure by fts promotion (dh-cloud) [#826](https://github.com/apache/cloudberry/pull/826)
* [`e9ee30d3a41`](https://github.com/apache/cloudberry/commit/e9ee30d3a41f30c2b349381e9335f8bec5b48b4e) - Process encoding option for external table custom format (#14702) (Huiliang.liu) [#826](https://github.com/apache/cloudberry/pull/826)
* [`89e7800aca7`](https://github.com/apache/cloudberry/commit/89e7800aca727c37de297017ba14165930f8f336) - Remove result tuple bound push down if qual (Jingyu Wang) [#826](https://github.com/apache/cloudberry/pull/826)
* [`3031181dd45`](https://github.com/apache/cloudberry/commit/3031181dd4535c82173798b0ffb443a1b96f0b12) - Fix parallel retrieve cursor on select transient record types issue (刘明哲) [#826](https://github.com/apache/cloudberry/pull/826)
* [`f1bd48dc7ca`](https://github.com/apache/cloudberry/commit/f1bd48dc7ca5e976c148bebdd305eff5dda9fcbe) - Fixed Resource management DDL report "WARNING: unrecognized node type" when log_statement='ddl'. (FairyFar) [#826](https://github.com/apache/cloudberry/pull/826)
* [`cc9c84350ab`](https://github.com/apache/cloudberry/commit/cc9c84350ab80645983448aa208cfd93526a772b) - Record pg_controldata output of failed segment (#14648) (Annpurna Shahani) [#826](https://github.com/apache/cloudberry/pull/826)
* [`a21db922392`](https://github.com/apache/cloudberry/commit/a21db922392dacef26f82c70c94dd7994ba57ac5) - Fix the flaky case truncate_gp (#14695) (Adam Lee) [#826](https://github.com/apache/cloudberry/pull/826)
* [`a94f48956ce`](https://github.com/apache/cloudberry/commit/a94f48956ce004c335b9c1ae709e6ffc5eb5bbf3) - Set the GPHOME_CLIENTS correctly in the script (#14732) (Adam Lee) [#826](https://github.com/apache/cloudberry/pull/826)
* [`384619e4df5`](https://github.com/apache/cloudberry/commit/384619e4df5ec47d0a4f1e51e7c1570d93f4e990) - gpexpand.status_detail should be distributed by "table_oid". (Zhenghua Lyu) [#826](https://github.com/apache/cloudberry/pull/826)
* [`8cdc29de7a1`](https://github.com/apache/cloudberry/commit/8cdc29de7a1556859477575d49bfdf31ced16fe3) - Convert float8 with double instead of long double (Denis Smirnov) [#826](https://github.com/apache/cloudberry/pull/826)
* [`a0f39821178`](https://github.com/apache/cloudberry/commit/a0f39821178fcc226aef53784f9f5b59dfe50c5b) - ORCA dsisable the update and delete on partitioned tables (zhoujiaqi) [#822](https://github.com/apache/cloudberry/pull/822)
* [`284d900550f`](https://github.com/apache/cloudberry/commit/284d900550f74b8b33e1a055bcee51c660c16d23) - Orca enable update and delete on partitioned tables (#14129) (David Kimura) [#822](https://github.com/apache/cloudberry/pull/822)
* [`ec0c86e5a1a`](https://github.com/apache/cloudberry/commit/ec0c86e5a1af42187ff277ba1663a5c318623400) - Enhance the code style and some fixes of IVM (#829) (Yongtao Huang) [#829](https://github.com/apache/cloudberry/pull/829)
* [`4bd2d57baa9`](https://github.com/apache/cloudberry/commit/4bd2d57baa90aec88c1da062b912e7c760271d2c) - Fix test cases of cherry-pick. (Jianghua Yang) [#827](https://github.com/apache/cloudberry/pull/827)
* [`96d06cafad8`](https://github.com/apache/cloudberry/commit/96d06cafad8f6ef573a705d21941370618685fba) - Resolve a FIXME in merge_leaf_stats() (Huansong Fu) [#827](https://github.com/apache/cloudberry/pull/827)
* [`f2764ef4c66`](https://github.com/apache/cloudberry/commit/f2764ef4c662ccbd7527f86a4b435fe41dcb25b1) - ao/co: don't dereference dangling pointers (#14468) (Adam Lee) [#827](https://github.com/apache/cloudberry/pull/827)
* [`142596d5843`](https://github.com/apache/cloudberry/commit/142596d58433796ec2ae75f726c909919402d42e) - fix ic-proxy mis-disconnect addrs after reload config file (#14415) (Jianghua Yang) [#827](https://github.com/apache/cloudberry/pull/827)
* [`2c238f6fc37`](https://github.com/apache/cloudberry/commit/2c238f6fc37c96acdb1e8e40bc7d4f426c4ab0fe) - Make gpactivatestandby do retry loop after standby promote (Jimmy Yih) [#827](https://github.com/apache/cloudberry/pull/827)
* [`276d6a855ad`](https://github.com/apache/cloudberry/commit/276d6a855ad8c6aa05522491e98a5fdba3187507) - Add numsegments check for gpcheckcat (Nihal Jain) [#827](https://github.com/apache/cloudberry/pull/827)
* [`32be1d074ef`](https://github.com/apache/cloudberry/commit/32be1d074ef252399988f77e78db421fa096400a) - Fix gpcheckcat partition distribution policy check (Nihal Jain) [#827](https://github.com/apache/cloudberry/pull/827)
* [`f770fbb757c`](https://github.com/apache/cloudberry/commit/f770fbb757c0714333812e975f88e51158d2ea35) - Check with assert if AO table's toast table kept existing relfrozenxid unchanged. (#13499) (Aegeaner) [#827](https://github.com/apache/cloudberry/pull/827)
* [`fce2c290552`](https://github.com/apache/cloudberry/commit/fce2c2905526559626124489102ba07966b63d8c) - Fix range tables opening's locking issue inside ExecInitModifyTable(). (#14418) (Aegeaner) [#827](https://github.com/apache/cloudberry/pull/827)
* [`e8c2b52cfb8`](https://github.com/apache/cloudberry/commit/e8c2b52cfb89f7db5ce4a60ee6f3a6b773c6c302) - Workaround flaky test "dispatch" (#14429) (David Kimura) [#827](https://github.com/apache/cloudberry/pull/827)
* [`0f9ea5e85f4`](https://github.com/apache/cloudberry/commit/0f9ea5e85f451e96e6af9bb43c7df0634e3e2ac2) - Refactor to extract SET_VAR to gp_bash_functions.sh (Junwang Zhao) [#827](https://github.com/apache/cloudberry/pull/827)
* [`680a0197b3c`](https://github.com/apache/cloudberry/commit/680a0197b3c61b81aa67f7e785930c4a7cbb44fc) - Fix makefile to remove tablespace-step target all (Junwang Zhao) [#827](https://github.com/apache/cloudberry/pull/827)
* [`2c3ad98952f`](https://github.com/apache/cloudberry/commit/2c3ad98952fae9d650c1efdc48b4595ae37d9b8a) - ao/co: Move unique index DML tests to new group (Soumyadeep Chakraborty)
* [`74c48c597d9`](https://github.com/apache/cloudberry/commit/74c48c597d9c5061726707dd444b813f9c97ae4d) - ao/co: Remove FIXME for DML state mechanism (Soumyadeep Chakraborty) [#827](https://github.com/apache/cloudberry/pull/827)
* [`ed1d76f4fa6`](https://github.com/apache/cloudberry/commit/ed1d76f4fa6e3bf0a90c4fb46729177696121ff1) - ao/co: Refactor DML states (Soumyadeep Chakraborty) [#827](https://github.com/apache/cloudberry/pull/827)
* [`2f4bafda0dd`](https://github.com/apache/cloudberry/commit/2f4bafda0ddccf2c8de2ec3777c0575a3110c209) - fix upgrading external tables with dropped cols (Kalen Krempely) [#827](https://github.com/apache/cloudberry/pull/827)
* [`4be1bf50eab`](https://github.com/apache/cloudberry/commit/4be1bf50eab09cf7a0c9b2478358ba7ff8594f91) - Remove unnecessary code that dealt with locus of UNION plans. (#10879) (Heikki Linnakangas) [#827](https://github.com/apache/cloudberry/pull/827)
* [`33e08725fb4`](https://github.com/apache/cloudberry/commit/33e08725fb447dae7309af5d1b34e0a75c6badc0) - fix gp_gettmid to return correct startup timestamp. (#14204) (Violet Cheng) [#827](https://github.com/apache/cloudberry/pull/827)
* [`ca2123f6b9d`](https://github.com/apache/cloudberry/commit/ca2123f6b9d74258bb181aacbfd8b76fac8e7f6e) - Fix typo. (#14290) (fiograf) [#827](https://github.com/apache/cloudberry/pull/827)
* [`a1353bb7a8b`](https://github.com/apache/cloudberry/commit/a1353bb7a8b3adfff7f52f2e29c0f4b3245daf14) - make headerfile accessible to pxf extension (#14046) (Himanshu Pandey) [#827](https://github.com/apache/cloudberry/pull/827)
* [`e6e665ea07b`](https://github.com/apache/cloudberry/commit/e6e665ea07ba84b6ee907cd6e31d3e00858c65ae) - gp_replica_check: resolve FIXMEs (Ashwin Agrawal) [#827](https://github.com/apache/cloudberry/pull/827)
* [`0e4928cd83e`](https://github.com/apache/cloudberry/commit/0e4928cd83eed173f80a1840f7347e14d5eb62e4) - gp_replica_check: cosmetic changes to output (Ashwin Agrawal) [#827](https://github.com/apache/cloudberry/pull/827)
* [`ede16fd9b9a`](https://github.com/apache/cloudberry/commit/ede16fd9b9ae429847be8f3942b0a6bf9a1b574d) - Bring back cdbsubselect_drop_distinct. (#14322) (Aegeaner) [#827](https://github.com/apache/cloudberry/pull/827)
* [`19f2e32575c`](https://github.com/apache/cloudberry/commit/19f2e32575c5cd38128e99417a9f77f1a6542232) - Fix gpload regress case failure when OS user is not gpadmin (#14301) (dreamedcheng) [#827](https://github.com/apache/cloudberry/pull/827)
* [`5330213a81f`](https://github.com/apache/cloudberry/commit/5330213a81f4da148d03d007fa2f6b23d70793e6) - Add tests for ALTER TABLE ONLY ... SET TABLESPACE (Huansong Fu) [#827](https://github.com/apache/cloudberry/pull/827)
* [`495309595a5`](https://github.com/apache/cloudberry/commit/495309595a57fb724728cc5a0516d1627afb5c4d) - Rename UpdateSerializableCommandId() to UpdateCommandIdInSnapshot() (Adam Lee) [#827](https://github.com/apache/cloudberry/pull/827)
* [`ff57b4890e0`](https://github.com/apache/cloudberry/commit/ff57b4890e03a1c65e908539bbe0aa420354f2bf) - Remove FIXME in bfv_olap_optimizer.out. (#14319) (Zhenghua Lyu) [#827](https://github.com/apache/cloudberry/pull/827)
* [`60eb05436a2`](https://github.com/apache/cloudberry/commit/60eb05436a23474ea3afbbebe211587f0c77ec4e) - Support ONLY keyword for GRANT/REVOKE (Huansong Fu) [#827](https://github.com/apache/cloudberry/pull/827)
* [`173864a37cb`](https://github.com/apache/cloudberry/commit/173864a37cb28ed8b7813678975b28de55d178e7) - Change FIXME to note in join_hash case. (Zhenghua Lyu) [#827](https://github.com/apache/cloudberry/pull/827)
* [`cd258f33920`](https://github.com/apache/cloudberry/commit/cd258f33920a047a7815090b6b454c023ceb140e) - Remove fixme in qp_correlated_query. (#14314) (Zhenghua Lyu) [#827](https://github.com/apache/cloudberry/pull/827)
* [`8e3de84113e`](https://github.com/apache/cloudberry/commit/8e3de84113e06200c1096e85a21e6a5cc41c533b) - Bring back cdbsubselect_drop_distinct from 5X branch. (#14258) (Aegeaner) [#827](https://github.com/apache/cloudberry/pull/827)
* [`a2d8a0ec6bd`](https://github.com/apache/cloudberry/commit/a2d8a0ec6bd004c9bc1acefbc023e7d241bc8bd9) - Tests of VACUUM (SKIP_LOCKED) (#14265) (Yao Wang) [#827](https://github.com/apache/cloudberry/pull/827)
* [`bb5d05287fa`](https://github.com/apache/cloudberry/commit/bb5d05287fa82f5b9e7a90e27ccaf634eea202ab) - fix crash on get_ao_compression_ratio of heap table (#14205) (Haotian Chen) [#827](https://github.com/apache/cloudberry/pull/827)
* [`e45481b2763`](https://github.com/apache/cloudberry/commit/e45481b27637ea6a2ec7e1e9089e5386fe1bdfe9) - Removed unnecessary variable assignment. (Gleb Khudyaev) [#827](https://github.com/apache/cloudberry/pull/827)
* [`b1a6c602cf4`](https://github.com/apache/cloudberry/commit/b1a6c602cf442da95fc8222e0ada5d811fd6464b) - pg_upgrade: Resolve test FIXME (Brent Doil) [#827](https://github.com/apache/cloudberry/pull/827)
* [`f819916fea1`](https://github.com/apache/cloudberry/commit/f819916fea1f5fbeafaed51e3c688039d0dcb438) - pg_upgrade: Resolve FIXME for spclocation column (Brent Doil) [#827](https://github.com/apache/cloudberry/pull/827)
* [`765c9ed7eba`](https://github.com/apache/cloudberry/commit/765c9ed7ebac77d8949be78f106570c4bf76e85b) - Fix: Ensure Smgr Relation is Opened Before Accessing AO Segment Files (yjhjstz) [#827](https://github.com/apache/cloudberry/pull/827)
* [`f2245856fdc`](https://github.com/apache/cloudberry/commit/f2245856fdcbba5a637b76b59470642615f05566) - Fix dependency bug with minirepro and materialized views (#14223) (Chris Hajas) [#827](https://github.com/apache/cloudberry/pull/827)
* [`c66c184aca2`](https://github.com/apache/cloudberry/commit/c66c184aca24d375b9297d835c72853c3aaaee84) - Fix incorrect sortOp and eqOp generated by IsCorrelatedEqualityOpExpr. (#14108) (1mmortal) [#827](https://github.com/apache/cloudberry/pull/827)
* [`32221f4499a`](https://github.com/apache/cloudberry/commit/32221f4499a32504e6b356a8fe05f0269fc812db) - Forward complete QE notice messages (#11563) (dh-cloud) [#827](https://github.com/apache/cloudberry/pull/827)
* [`b0b1c367138`](https://github.com/apache/cloudberry/commit/b0b1c36713845d82c1c3ad1e84d9595d3a66640b) - Dump number of segments during minirepro and gpsd (#14225) (Chris Hajas) [#827](https://github.com/apache/cloudberry/pull/827)
* [`47fe6d9aa58`](https://github.com/apache/cloudberry/commit/47fe6d9aa58342b9a565d48e87be0a7edea8da9a) - Clean some typos about IVM (#825) (Yongtao Huang) [#825](https://github.com/apache/cloudberry/pull/825)
* [`976e4c82897`](https://github.com/apache/cloudberry/commit/976e4c82897d351a5a97f632eec6fe57e9ab6a80) - Fix crash that partition table has no encoding attributes for new columns (#820) (Hao Wu) [#820](https://github.com/apache/cloudberry/pull/820)
* [`e24b737aa6a`](https://github.com/apache/cloudberry/commit/e24b737aa6aa33d95e2be9d5c967a8a2d8ac064d) - Fix test cases of cherry-pick. (Zhang Mingli) [#823](https://github.com/apache/cloudberry/pull/823)
* [`4cfaad7b36d`](https://github.com/apache/cloudberry/commit/4cfaad7b36d7005aeb30789e8754ebac0a21d83c) - dsnap: Remove dead function (Soumyadeep Chakraborty) [#823](https://github.com/apache/cloudberry/pull/823)
* [`accf1b887de`](https://github.com/apache/cloudberry/commit/accf1b887debefbe7ea1b6a77875d2714f2ed0c1) - Fix test_consume_xids where it consumes +1 to what's intended (Huansong Fu) [#823](https://github.com/apache/cloudberry/pull/823)
* [`3706ac29d3a`](https://github.com/apache/cloudberry/commit/3706ac29d3ae4bfdda17908a43a5d8a04518c5f0) - Remove GPDB_12_MERGE_FIXME in cdb_create_multistage_grouping_paths() (#14556) (QingMa) [#823](https://github.com/apache/cloudberry/pull/823)
* [`1eeb8ef6d95`](https://github.com/apache/cloudberry/commit/1eeb8ef6d958feca68f87a9de97ac4ae31eeb2c5) - Remove the generated 'stdin' and 'stdout' files (Adam Lee) [#823](https://github.com/apache/cloudberry/pull/823)
* [`793bffd26e3`](https://github.com/apache/cloudberry/commit/793bffd26e381c04b3003929e7361824a1a2e2f3) - ao/co: Ban speculative insert in parse analysis (Soumyadeep Chakraborty) [#823](https://github.com/apache/cloudberry/pull/823)
* [`e3e971d9888`](https://github.com/apache/cloudberry/commit/e3e971d98885ccb6d45e4d6c930fd4a5bc0a1054) - Clean up compilation warnings coming from PL/Perl with clang-12~ (#14657) (Chen Mulong) [#823](https://github.com/apache/cloudberry/pull/823)
* [`840ea494aa0`](https://github.com/apache/cloudberry/commit/840ea494aa0e99bf5483c98c4274e58dfd477a76) - ao/co: Clarify tuple_lock API (Soumyadeep Chakraborty) [#823](https://github.com/apache/cloudberry/pull/823)
* [`5cc58d6e24e`](https://github.com/apache/cloudberry/commit/5cc58d6e24ed9e4fbb284662af348f3ef5215f56) - ao/co: Clarify compute_xid_horizon_for_tuples API (Soumyadeep Chakraborty) [#823](https://github.com/apache/cloudberry/pull/823)
* [`0f65387744f`](https://github.com/apache/cloudberry/commit/0f65387744f53a7fb5be9fba9f082b15f88f72db) - Remove handle RangeSubselect in checkWellFormedRecursionWalker. (Zhenghua Lyu) [#823](https://github.com/apache/cloudberry/pull/823)
* [`f7b10c1bf4c`](https://github.com/apache/cloudberry/commit/f7b10c1bf4cafed68019278a5c00dc906d50f7da) - Support "Mq" in isolation test framework (Alexandra Wang) [#823](https://github.com/apache/cloudberry/pull/823)
* [`49ae63ad033`](https://github.com/apache/cloudberry/commit/49ae63ad03324c6f055ab24410e02098fbc543e0) - Fix gp_dqa test to explicitly analyze tables (#14643) (Chris Hajas) [#823](https://github.com/apache/cloudberry/pull/823)
* [`27d18ddc6dd`](https://github.com/apache/cloudberry/commit/27d18ddc6dd2df3906a6af08e2516272d3b62791) - Remove dead code MPool (#15511) (Yongtao Huang) [#792](https://github.com/apache/cloudberry/pull/792)
* [`7f919d894cd`](https://github.com/apache/cloudberry/commit/7f919d894cda3cca712f9fd1868aff7303eeb84a) - Fix icw test "Derive Combined Hashed Spec For Outer Joins" (zhoujiaqi) [#804](https://github.com/apache/cloudberry/pull/804)
* [`6c289ada596`](https://github.com/apache/cloudberry/commit/6c289ada596aa16e9bfb7284e535530102cf79a9) - FIX: Ordered set agg with a ref type column will generate coredump (zhoujiaqi) [#804](https://github.com/apache/cloudberry/pull/804)
* [`7f6f4a6b601`](https://github.com/apache/cloudberry/commit/7f6f4a6b601585f2126bb63c2554e36082c6d704) - Derive Combined Hashed Spec For Outer Joins (#14045) (THANATOSLAVA) [#804](https://github.com/apache/cloudberry/pull/804)
* [`f0a6160d3cc`](https://github.com/apache/cloudberry/commit/f0a6160d3ccc1290c3b733b633ff298d26707340) - [ORCA] Allow empty target list (#14159) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`b43dfb64bf2`](https://github.com/apache/cloudberry/commit/b43dfb64bf2217272b2a5d1684901157778e1e59) - Allow ORCA to generate DML plans on GENERATED column tables (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`c9f44457d6c`](https://github.com/apache/cloudberry/commit/c9f44457d6c0a0664a7c749c6384e048946a978f) - Add exception using unsupported default comparator evaulator types (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`f4c7f1c37df`](https://github.com/apache/cloudberry/commit/f4c7f1c37df6d215588d48c04895bae72ccec66e) - Address FIXMEs in gpdbwrappers.cpp file (#14104) (Ekta Khanna) [#804](https://github.com/apache/cloudberry/pull/804)
* [`06d00130c5e`](https://github.com/apache/cloudberry/commit/06d00130c5e1d12998b9d025b097e98dfb38fe50) - Add HashValue function to CPartitionPropagationSpec (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`0600f616ec6`](https://github.com/apache/cloudberry/commit/0600f616ec6ae3cb6f1cf64eadc87583e95137cc) - [ORCA] Resolve merge FIXMEs in CPartitionPropagationSpec (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`a76a41736cb`](https://github.com/apache/cloudberry/commit/a76a41736cbcee1c87032267957ff24930962fc8) - [ORCA] Fix duplicate cast predicates  (#14061) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`5a720a1e7c6`](https://github.com/apache/cloudberry/commit/5a720a1e7c6a0465f833bf7b32090add2126bb83) - Allow certain functions to be safely executed on replicated slices in Orca (#13873) (Chris Hajas) [#804](https://github.com/apache/cloudberry/pull/804)
* [`ac6926d4288`](https://github.com/apache/cloudberry/commit/ac6926d4288d7293ddfbd2333a311d72df20cce9) - Update ordered-set agg preprocess step for skew (Ekta Khanna) [#804](https://github.com/apache/cloudberry/pull/804)
* [`0e3e7a3b25e`](https://github.com/apache/cloudberry/commit/0e3e7a3b25e428e1745039449c3a8c6d54cd2d78) - Remove ORCA specific duplicate code (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`f5bdc807a75`](https://github.com/apache/cloudberry/commit/f5bdc807a75adeb0b2b18df184047e1f45345f17) - Remove unused function PdxlnBitmapIndexPathForChildPart() (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`2034e5e3b2a`](https://github.com/apache/cloudberry/commit/2034e5e3b2aee32a9b1cd5adef768b0f51f43de4) - Remove FIXME label to gut has_oids (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`463f2fdb8d2`](https://github.com/apache/cloudberry/commit/463f2fdb8d2a729b90b1d83f9560d8d0501d3a42) - Remove unused GUC optimizer_enable_partial_index (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`26c35a8ab16`](https://github.com/apache/cloudberry/commit/26c35a8ab16ffd54a20ed05140fccbd95bd7f550) - Enable direct dispatch for DML statements with ORCA (#13471) (24nishant) [#804](https://github.com/apache/cloudberry/pull/804)
* [`a565c9fc7eb`](https://github.com/apache/cloudberry/commit/a565c9fc7eb9657ae4eef68d5788dcaded9ce08c) - Derive combined hashed spec outer join master revert (#13967) (THANATOSLAVA) [#804](https://github.com/apache/cloudberry/pull/804)
* [`c34428846e0`](https://github.com/apache/cloudberry/commit/c34428846e0b290e6cf6fb56e973c52b850ebc4e) - Fix assert op executor node (#13900) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`fcb9d008afd`](https://github.com/apache/cloudberry/commit/fcb9d008afd5d5e5baf5c2f80a0c40ec8ae561a7) - Fix improper copying of group statistics in Orca (#13926) (Chris Hajas) [#804](https://github.com/apache/cloudberry/pull/804)
* [`6e7b4d58e61`](https://github.com/apache/cloudberry/commit/6e7b4d58e6137b143b751e12e3884c276a6a3610) - Remove WITH OIDS from ORCA code base (#13920) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`32ba45efd10`](https://github.com/apache/cloudberry/commit/32ba45efd10b7b66e60b211f857c67de0fa24ece) - Derive Combined Hashed Spec For Outer Joins - Patch (#13899) (THANATOSLAVA) [#804](https://github.com/apache/cloudberry/pull/804)
* [`b763ec9ce33`](https://github.com/apache/cloudberry/commit/b763ec9ce33830d0473c18588d14dd93fe4c2411) - Convert ORCA pipelines to use Vault variables (#13907) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`0ffc4af83d9`](https://github.com/apache/cloudberry/commit/0ffc4af83d9fc1167743ed7a191a13a4017f1eb2) - ORCA Update reset stat to handle cyclic memo path (#13879) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`fc065034df7`](https://github.com/apache/cloudberry/commit/fc065034df7287b2e037eaa2f6474d1389724204) - Queries on Distributed Replicated tables hangs when using optimizer (DevChattopadhyay) [#804](https://github.com/apache/cloudberry/pull/804)
* [`b1d2e27ef85`](https://github.com/apache/cloudberry/commit/b1d2e27ef858976fa6c5af5b20ea8741d4f5fc3c) - [ORCA] Fix duplicate stats reset (#13817) (David Kimura) [#804](https://github.com/apache/cloudberry/pull/804)
* [`aff4a81d68f`](https://github.com/apache/cloudberry/commit/aff4a81d68ffbcb2af71a686b5df5b92ad9595ac) - Derive Combined Hashed Spec For Outer Joins (#13714) (THANATOSLAVA) [#804](https://github.com/apache/cloudberry/pull/804)
* [`0faf8e18367`](https://github.com/apache/cloudberry/commit/0faf8e18367acc7256cc2e7450b18f000467bcbb) - FIX: re-enable the NL-index in ORCA and fix the Join2IndexApplyGeneric (#807) (jiaqizho) [#807](https://github.com/apache/cloudberry/pull/807)
* [`cf204de471b`](https://github.com/apache/cloudberry/commit/cf204de471bb89312e6b93c13cff0a757a7f7043) - Fix cheryy-pick test cases. (Zhang Mingli) [#818](https://github.com/apache/cloudberry/pull/818)
* [`c70f0a8ade2`](https://github.com/apache/cloudberry/commit/c70f0a8ade256b3707b7d65bd4d07a1a747c9899) - Support ALTER TABLE SET DISTRIBUTED BY for external tables (Huansong Fu) [#818](https://github.com/apache/cloudberry/pull/818)
* [`c53d3db6dcf`](https://github.com/apache/cloudberry/commit/c53d3db6dcf615bc069cb3e31cc2b6ebdcfb6118) - Disable flaky qp_union_intersect test (David Kimura) [#818](https://github.com/apache/cloudberry/pull/818)
* [`e8798016d34`](https://github.com/apache/cloudberry/commit/e8798016d344617430033c6239983d1efd6250d8) - Revamp ic-proxy logging (Soumyadeep Chakraborty) [#818](https://github.com/apache/cloudberry/pull/818)
* [`af49d1ba769`](https://github.com/apache/cloudberry/commit/af49d1ba769964084da78a7ce3a255cf4248f09f) - Fix memory leak in DynamicBitmapScans (Chris Hajas) [#818](https://github.com/apache/cloudberry/pull/818)
* [`ae56ae677d6`](https://github.com/apache/cloudberry/commit/ae56ae677d687751c0d93439ffd65bc309bae42a) - Disable flaky qp_union_intersect test (Chris Hajas) [#818](https://github.com/apache/cloudberry/pull/818)
* [`eed5ab6827b`](https://github.com/apache/cloudberry/commit/eed5ab6827b3715225543e2468e21c34c76cbaa9) - Revert "Unify ic-proxy log level under GUC gp_log_interconnect's control, and remove macro IC_PROXY_LOG_LEVEL" (#14207) (David Kimura) [#818](https://github.com/apache/cloudberry/pull/818)
* [`d20bbab9de3`](https://github.com/apache/cloudberry/commit/d20bbab9de394c657dff1c9b5895cc0725cd3be4) - Unify ic-proxy log level under GUC gp_log_interconnect's control, and remove macro IC_PROXY_LOG_LEVEL (Aegeaner) [#818](https://github.com/apache/cloudberry/pull/818)
* [`4f9ceb8ac5e`](https://github.com/apache/cloudberry/commit/4f9ceb8ac5e166273ff6fb321db95a058d422bea) - Revert "Fix pipeline failure caused by psql_gp_commands test" (Adam Lee) [#818](https://github.com/apache/cloudberry/pull/818)
* [`f3ea5c6c5b8`](https://github.com/apache/cloudberry/commit/f3ea5c6c5b86202b50591327bda03c94561296ca) - Exclude AMs with internal handlers in gpcheckcat dependency checks (Adam Lee) [#818](https://github.com/apache/cloudberry/pull/818)
* [`454fa31bf18`](https://github.com/apache/cloudberry/commit/454fa31bf18c7a601283523aff888fba249267c6) - Fix cases of cherry-pick (Zhang Mingli) [#816](https://github.com/apache/cloudberry/pull/816)
* [`ace5385c486`](https://github.com/apache/cloudberry/commit/ace5385c486fc0b7a32b8d89d0a2eeb098e818e4) - Replace netstat with ss (code cleanup) (Nihal Jain) [#816](https://github.com/apache/cloudberry/pull/816)
* [`831f6a331d2`](https://github.com/apache/cloudberry/commit/831f6a331d2e1abd0884e074a5dc11cd8a54430f) - Change cryptography package version (Nihal Jain) [#816](https://github.com/apache/cloudberry/pull/816)
* [`f484c5a423c`](https://github.com/apache/cloudberry/commit/f484c5a423cfaff119b7c3b8b6cecbaf62f2bfeb) - [typo] compatable => compatible (#14084) (Junwang Zhao) [#816](https://github.com/apache/cloudberry/pull/816)
* [`96738725fdd`](https://github.com/apache/cloudberry/commit/96738725fddb76e03b86cc6cb63df25cb3df42f9) - Fix pipeline failure caused by psql_gp_commands test (Wenru Yan) [#816](https://github.com/apache/cloudberry/pull/816)
* [`561dd884e9b`](https://github.com/apache/cloudberry/commit/561dd884e9b3fed3a63905c58f6adeaf965f5b36) - tcp-ic: Add more detail to register msg error (Soumyadeep Chakraborty) [#816](https://github.com/apache/cloudberry/pull/816)
* [`59793b7f290`](https://github.com/apache/cloudberry/commit/59793b7f29099415447206ab01ee64d522c04968) - psql: display the access method name as the storage (#14066) (Wenru Yan) [#816](https://github.com/apache/cloudberry/pull/816)
* [`fb54cfef799`](https://github.com/apache/cloudberry/commit/fb54cfef799de8f5458b81050c43f80a71f2695d) - Break early when creating Memtuple binding from TupleDesc. (Zhang Mingli) [#816](https://github.com/apache/cloudberry/pull/816)
* [`5568896a984`](https://github.com/apache/cloudberry/commit/5568896a984185a102fa828eefeaa6c7dd90b7f8) - Introduce timeout to tcp ic teardown select loop (Soumyadeep Chakraborty) [#816](https://github.com/apache/cloudberry/pull/816)
* [`989902c65da`](https://github.com/apache/cloudberry/commit/989902c65da1fb5e97a4e7d999e453c6412e048f) - Fix typo. (Junwang Zhao) [#816](https://github.com/apache/cloudberry/pull/816)
* [`4106ae50e9b`](https://github.com/apache/cloudberry/commit/4106ae50e9bcc6b6da958d241332c0f39e623c41) - Fix incorrect hash-key of Redistribute-Motion when creating path for multi-DQA expr. (#14135) (Xing Guo) [#816](https://github.com/apache/cloudberry/pull/816)
* [`7b2a49b0dd6`](https://github.com/apache/cloudberry/commit/7b2a49b0dd6879260b9bc3d921db77ac4978b79f) - Update tidscan optimizer expected output (#14132) (gpopt) [#816](https://github.com/apache/cloudberry/pull/816)
* [`1157e367e51`](https://github.com/apache/cloudberry/commit/1157e367e51d96526c183e54212fc8d821d37bd1) - Removed transforming of hasharray to hashstring (#14121) (Rakesh Sharma) [#816](https://github.com/apache/cloudberry/pull/816)
* [`e4310714c5d`](https://github.com/apache/cloudberry/commit/e4310714c5db0812f8618aaf2c2c0866f0422341) - Fix orca target list will be disordered when function's return_type is record. (zhangwenchao) [#813](https://github.com/apache/cloudberry/pull/813)
* [`7e78128b8cf`](https://github.com/apache/cloudberry/commit/7e78128b8cf9dc65c2f3de7cf89d44378fd894b5) - Fix directory table problems and add guc allow_dml_directory_table. (zhangwenchao) [#683](https://github.com/apache/cloudberry/pull/683)
* [`679a1e26ac7`](https://github.com/apache/cloudberry/commit/679a1e26ac75e6c839af144db2f7a0fb1ea1c558) - Fix flake test bfv_meta_track (#817) (Jianghua.yjh) [#817](https://github.com/apache/cloudberry/pull/817)
* [`1efdb2b3a25`](https://github.com/apache/cloudberry/commit/1efdb2b3a258446645e6329bab3775554cf24114) - ci: Introduce installcheck-cbdb-parallel (#819) (Ed Espino) [#819](https://github.com/apache/cloudberry/pull/819)
* [`8d20831b7c8`](https://github.com/apache/cloudberry/commit/8d20831b7c8b8e3f464eb6a4045beacc562fc442) - pg_upgrade: Fix core dump in report_progress() (Brent Doil) [#814](https://github.com/apache/cloudberry/pull/814)
* [`5c173c666a0`](https://github.com/apache/cloudberry/commit/5c173c666a03beeb4909a3b276ed3ef31d258f79) - Fix gpcheckcat false alarms for pg_default_acl (Soumyadeep Chakraborty) [#814](https://github.com/apache/cloudberry/pull/814)
* [`071760faaaf`](https://github.com/apache/cloudberry/commit/071760faaaf1e50e9eb6e0e07257c294bf05b713) - Fix wrong results with a WITH RECURSIVE query (#13715) (Wenru Yan) [#814](https://github.com/apache/cloudberry/pull/814)
* [`50b7d5f6840`](https://github.com/apache/cloudberry/commit/50b7d5f6840e38fba9c8763d297423549a5b6fc7) - [psql] Add support for describing auxiliary tables for ao table. (#14063) (Xing Guo) [#814](https://github.com/apache/cloudberry/pull/814)
* [`27a67d4026c`](https://github.com/apache/cloudberry/commit/27a67d4026cab37233a4cdb9b2df0d3ac0e7d20c) - Fix a panic case in the greenplum_fdw test. (#14033) (Huiliang.liu) [#814](https://github.com/apache/cloudberry/pull/814)
* [`8d539dcc4c8`](https://github.com/apache/cloudberry/commit/8d539dcc4c8093dbfefaacae381c945c8fbb0ebd) - Fix db_size_functions regress test (Jimmy Yih) [#814](https://github.com/apache/cloudberry/pull/814)
* [`97592048125`](https://github.com/apache/cloudberry/commit/975920481252926704ca96d9591877b60b0fbf8c) - change verify checksum FIXME to FEATURE NOT SUPPORTED (Rakesh Sharma) [#814](https://github.com/apache/cloudberry/pull/814)
* [`511933dfb57`](https://github.com/apache/cloudberry/commit/511933dfb57f215403c65d1e2cefe9d9ec89cb35) - Remove -emit-llvm bitcode compile flag work around (David Kimura) [#814](https://github.com/apache/cloudberry/pull/814)
* [`eac1b11d50c`](https://github.com/apache/cloudberry/commit/eac1b11d50cb8fd50b7d7388d45749bb5c559e9f) - Cleanup a FIXME in ATExecAddIndex (Huansong Fu) [#814](https://github.com/apache/cloudberry/pull/814)
* [`c33386e5b59`](https://github.com/apache/cloudberry/commit/c33386e5b59e2032395ae701ed3434f987489ac8) - Disallow Insert and Delete triggers on SplitUpdate (wenru yan) [#814](https://github.com/apache/cloudberry/pull/814)
* [`ee6b1e596cb`](https://github.com/apache/cloudberry/commit/ee6b1e596cbac5aa796eaa2e88e85069954bd213) - Update gp_switch_wal() to include pg_walfile_name() output (Jimmy Yih) [#814](https://github.com/apache/cloudberry/pull/814)
* [`eb475870c9c`](https://github.com/apache/cloudberry/commit/eb475870c9c09ee66c715bcc52a0c801feefea70) - gpinitsystem is not working with debug option (#13942) (Annpurna Shahani) [#814](https://github.com/apache/cloudberry/pull/814)
* [`205c4b11509`](https://github.com/apache/cloudberry/commit/205c4b1150993679f6c4121d84700d56d7166470) - Resolve fixme comment in ATExecAddColumn() about why no lock child table (Huansong Fu) [#814](https://github.com/apache/cloudberry/pull/814)
* [`31fe0c159ab`](https://github.com/apache/cloudberry/commit/31fe0c159ab970ac2e86c729ff61f048b1204f32) - Add xmin, xmax in test cases for troubleshooting flakiness. (Zhang Mingli) [#808](https://github.com/apache/cloudberry/pull/808)
* [`b50e6d1c4bb`](https://github.com/apache/cloudberry/commit/b50e6d1c4bb9832647ef320a7b6cfc5b027d352d) - Fix pipeline failure (yjhjstz) [#809](https://github.com/apache/cloudberry/pull/809)
* [`9389da31d3c`](https://github.com/apache/cloudberry/commit/9389da31d3c43d6d39b65c07544237bfb34c5cf0) - Move test bitmap_union from isolation2 to regress. (Zhenghua Lyu) [#809](https://github.com/apache/cloudberry/pull/809)
* [`8425ed16b0e`](https://github.com/apache/cloudberry/commit/8425ed16b0e54c42dfb382c6c3120d83dc33e3f2) - Remove an redundant case from workfile_limits (#13998) (Adam Lee) [#809](https://github.com/apache/cloudberry/pull/809)
* [`e5d742b8060`](https://github.com/apache/cloudberry/commit/e5d742b80606d5ba711a4ce650bfda37b764ec7c) - Preserve param info when bringing a path to OuterQuery locus (Wenru Yan) [#809](https://github.com/apache/cloudberry/pull/809)
* [`f882215c971`](https://github.com/apache/cloudberry/commit/f882215c97180a66eb19a0745b586ee15db20d20) - Eliminate alien nodes before execution for entry db (Sergey Smirnov) [#809](https://github.com/apache/cloudberry/pull/809)
* [`0bb07c03c65`](https://github.com/apache/cloudberry/commit/0bb07c03c65141aa25b7db8ae0799afd0a285416) - DatumStreamRead teardown: NULL out after pfree (Soumyadeep Chakraborty) [#809](https://github.com/apache/cloudberry/pull/809)
* [`fffa69a19cc`](https://github.com/apache/cloudberry/commit/fffa69a19cc7ea6fecb51cc149457b039e5040e7) - DatumStreamWrite teardown: NULL out after pfree (Soumyadeep Chakraborty) [#809](https://github.com/apache/cloudberry/pull/809)
* [`d8f22d1a15b`](https://github.com/apache/cloudberry/commit/d8f22d1a15bae5a4fe40eb9bb26b45415b595a06) - AOFetchBlockMetadata: Remove dead fields (Soumyadeep Chakraborty) [#809](https://github.com/apache/cloudberry/pull/809)
* [`7349bb4c214`](https://github.com/apache/cloudberry/commit/7349bb4c214ce18e2d694ea62b1e2c7ed9bfec4e) - Fix for ICW test alter_table_aocs2 (Divyesh Vanjare) [#809](https://github.com/apache/cloudberry/pull/809)
* [`029ac9e79c1`](https://github.com/apache/cloudberry/commit/029ac9e79c1ade4804240762cccd3465bae4082d) - Resolve tablecmd fixme for external part check (Divyesh Vanjare) [#809](https://github.com/apache/cloudberry/pull/809)
* [`9916d77c7c0`](https://github.com/apache/cloudberry/commit/9916d77c7c05482bb46774a3b8756621cc93c569) - Removing AOCO add column fixme (Divyesh Vanjare) [#809](https://github.com/apache/cloudberry/pull/809)
* [`6b8da0727e5`](https://github.com/apache/cloudberry/commit/6b8da0727e5e777a65a1ae9be8963af6a14db5b1) - Fix compile-time warn in pg_basebackup code. (reshke) [#809](https://github.com/apache/cloudberry/pull/809)
* [`45496ef56ca`](https://github.com/apache/cloudberry/commit/45496ef56ca7d12ada45f89a75d7102eaf1027f1) - Escape database name for dbconn. (Zhenghua Lyu) [#809](https://github.com/apache/cloudberry/pull/809)
* [`c6931b5ff75`](https://github.com/apache/cloudberry/commit/c6931b5ff75819219f5fe02d1ca42609c8a4d1b1) - Resolve a GPDB_12_MERGE_FIXME in regress/dsp. (Zhenghua Lyu) [#809](https://github.com/apache/cloudberry/pull/809)
* [`6805b17f4eb`](https://github.com/apache/cloudberry/commit/6805b17f4ebf3345a0b149c82d676e94dbdc06d1) - [AORO] change addition assignment to assigment (Junwang Zhao) [#809](https://github.com/apache/cloudberry/pull/809)
* [`4fa18996426`](https://github.com/apache/cloudberry/commit/4fa189964265c550568bfbd315a23f872fad7eb6) - Fix gplogfilter csv generation (t1mursadykov) [#809](https://github.com/apache/cloudberry/pull/809)
* [`2dd5c39969b`](https://github.com/apache/cloudberry/commit/2dd5c39969b06d9b1d9ddada0f2932107699fc9e) - gpcheckcat: Make opt block similar to 6X (Orhan Kislal) [#809](https://github.com/apache/cloudberry/pull/809)
* [`7b95278d6ef`](https://github.com/apache/cloudberry/commit/7b95278d6efd60c1f7698dc39ab97dd78f53df58) - Avoid loading gp_inject_fault extension twice (Huiliang Liu) [#809](https://github.com/apache/cloudberry/pull/809)
* [`4fba8198420`](https://github.com/apache/cloudberry/commit/4fba81984209d4b61ea60f3f6635131057955244) - cdbappendonlystoragewrite: Remove dead functions (Soumyadeep Chakraborty) [#809](https://github.com/apache/cloudberry/pull/809)
* [`2de3a0f65db`](https://github.com/apache/cloudberry/commit/2de3a0f65dbc5d989b2cf91c0de3523abde90d85) - Emit ERROR if gp_fastsequence entry goes backward during update (Ashwin Agrawal) [#809](https://github.com/apache/cloudberry/pull/809)
* [`86d9520c08e`](https://github.com/apache/cloudberry/commit/86d9520c08e8f985cf8a120c59fed307a9692f16) - disallow generated columns in distribution key (wenru yan) [#809](https://github.com/apache/cloudberry/pull/809)
* [`e7e594420d7`](https://github.com/apache/cloudberry/commit/e7e594420d73e4fc68ecfbc859b41fb2ae776003) - Fix leak user information by LDAP (#13831) (Haotian Chen) [#809](https://github.com/apache/cloudberry/pull/809)
* [`615a5d0c483`](https://github.com/apache/cloudberry/commit/615a5d0c4830d70ddba1dd9c607bfdc7880b4fe1) - if encoding is defaulted when creating external tables, we will use the server encoding (#13885) (xiaoxiao)
* [`f90a5a51fe8`](https://github.com/apache/cloudberry/commit/f90a5a51fe81378ce2905341b8398df67b1fe737) - Declare BackoffPriorityIntToValue and ResourceQueueGetPriorityWeight as extern functions. (#13908) (Yini Li) [#809](https://github.com/apache/cloudberry/pull/809)
* [`64cfb48305b`](https://github.com/apache/cloudberry/commit/64cfb48305b66fc9501200bcfcedbfd4a2ebe55d) - Fix groupingsets_optimizer.out (Zhang Mingli) [#810](https://github.com/apache/cloudberry/pull/810)
* [`d6ca9bdd74d`](https://github.com/apache/cloudberry/commit/d6ca9bdd74da1209127c61aa53bc3e0b68f2ca9e) - Fix cases after cherry-pick from GPDB. (Zhang Mingli) [#810](https://github.com/apache/cloudberry/pull/810)
* [`5980168b194`](https://github.com/apache/cloudberry/commit/5980168b194ebe37fbb4ec52b4057957189eb08d) - removed fixme tag as the changes have no impact (#14116) (Rakesh Sharma) [#810](https://github.com/apache/cloudberry/pull/810)
* [`d54296dcae2`](https://github.com/apache/cloudberry/commit/d54296dcae2d864414bb0325124bb9b661f081d1) - Fix gpinitsystem Behave tests that use environment variables (Jimmy Yih) [#810](https://github.com/apache/cloudberry/pull/810)
* [`ebc866e0c41`](https://github.com/apache/cloudberry/commit/ebc866e0c41720547ba6736a112181ca6f866986) - GlobalShellExecutor checks the whole output as the termination condition (Adam Lee) [#810](https://github.com/apache/cloudberry/pull/810)
* [`23544a20db3`](https://github.com/apache/cloudberry/commit/23544a20db3137fdb7e547351a6665b77eec44e4) - Use "initfile" as the filename extension of generated init files (Adam Lee) [#810](https://github.com/apache/cloudberry/pull/810)
* [`0572c4e9ed8`](https://github.com/apache/cloudberry/commit/0572c4e9ed860d2e21333af44ec7c98549dda583) - Put some locale related GUCs to sync guc array (#14068) (dreamedcheng) [#810](https://github.com/apache/cloudberry/pull/810)
* [`36e16d1ec45`](https://github.com/apache/cloudberry/commit/36e16d1ec4514d22f1e2e04ffc2832d5dda9b35a) - Support multi stage hashagg for groupingsets with unsortable refs (#14056) (QingMa) [#810](https://github.com/apache/cloudberry/pull/810)
* [`f34ae724163`](https://github.com/apache/cloudberry/commit/f34ae7241633e4672c7a0bfb6d5e9f5be72f8619) - Maintain Data Status of Materialized Views for Partitioned Tables. (Zhang Mingli) [#786](https://github.com/apache/cloudberry/pull/786)
* [`43ca55d2a9b`](https://github.com/apache/cloudberry/commit/43ca55d2a9beb0266106f877623028f7a33d9601) - Fix the icw tests which generated by dynamic (bitmap/index/table) scan (zhoujiaqi) [#796](https://github.com/apache/cloudberry/pull/796)
* [`5612f4b4d76`](https://github.com/apache/cloudberry/commit/5612f4b4d76a9313fbd67ec6b1bee0c35c2dc793) - Adapt dynamic (bitmap/index/table) scan to PG 14 (zhoujiaqi) [#796](https://github.com/apache/cloudberry/pull/796)
* [`12ed1a8e82a`](https://github.com/apache/cloudberry/commit/12ed1a8e82a0e0ad30cb8af7807fff3915bd2099) - Fix compiler warnings from Dynamic Scan commit (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`80026068d05`](https://github.com/apache/cloudberry/commit/80026068d0508f1f33f0dc043771d17c777047d0) - MDP changes (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`b4476faac51`](https://github.com/apache/cloudberry/commit/b4476faac5196e87f90dd7e19e83bd6402126ea3) - ICW changes (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`10bc16c2452`](https://github.com/apache/cloudberry/commit/10bc16c245295e86cc1a26f28c1c45952ccc196a) - Assorted changes and cleanup for Dynamic Scan support (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`5f5250e70bf`](https://github.com/apache/cloudberry/commit/5f5250e70bf0a3fccc9553106c380d4c6f5e3964) - Adds Orca support for Dynamic Bitmap Heap/Index Scan (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`2617770361b`](https://github.com/apache/cloudberry/commit/2617770361b6b1c8c804dec78040e15301a0d49b) - Adds Orca support for Dynamic Index Scan (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`ef95449dba2`](https://github.com/apache/cloudberry/commit/ef95449dba27cea8c81069f5e888c53ec1f4101b) - Adds Orca support for Dynamic Table Scan (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`38c560f90bc`](https://github.com/apache/cloudberry/commit/38c560f90bcabcf11bba67a5627737ae7adae5f8) - Revert "Remove unused Dynamic (bitmap/index/table) Scan code" (Chris Hajas) [#796](https://github.com/apache/cloudberry/pull/796)
* [`9178c9f6dc0`](https://github.com/apache/cloudberry/commit/9178c9f6dc0708bfcfd7dde45aa49104ab3eeb55) - Fallback to Postgres optimizer on empty target list in CTE producer (Alexey Gordeev) [#796](https://github.com/apache/cloudberry/pull/796)
* [`589191e855a`](https://github.com/apache/cloudberry/commit/589191e855abbb23bc5161c7e6d2965753be0fe0) - Add dynamic test selection and flexible defaults (Ed Espino) [#805](https://github.com/apache/cloudberry/pull/805)
* [`b7c0660e5b3`](https://github.com/apache/cloudberry/commit/b7c0660e5b395ce9f9de2b5714766b3589bb8de7) - Stabilize output of new regression test. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`f83cb86feac`](https://github.com/apache/cloudberry/commit/f83cb86feacf7bc125e275a481cbc63ff7cdc9a4) - In extensions, don't replace objects not belonging to the extension. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`112b2181bb1`](https://github.com/apache/cloudberry/commit/112b2181bb1f4cf0fd811a7e2d60bf30ccb08ee7) - remove useless extern (#13896) (Junwang Zhao) [#802](https://github.com/apache/cloudberry/pull/802)
* [`652633a29ff`](https://github.com/apache/cloudberry/commit/652633a29ff24149246f3ce4cfbbd92f23aa9bd3) - ADBDEV-2872: ALTER TABLE erase pg_appendonly values (Georgy Shelkovy) [#802](https://github.com/apache/cloudberry/pull/802)
* [`7cd776a5ed3`](https://github.com/apache/cloudberry/commit/7cd776a5ed3b195e5a0d1b78e3425c50e154fed4) - [AOCO] remove the useless if condition (Junwang Zhao) [#802](https://github.com/apache/cloudberry/pull/802)
* [`f18d5493821`](https://github.com/apache/cloudberry/commit/f18d54938219ed55e94ad644a990bf54260b363f) - Add CHECK_FOR_INTERRUPTS in ExecInsert's speculative insertion loop. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`841aacdb43c`](https://github.com/apache/cloudberry/commit/841aacdb43c7e23ea445d336bf5ed1937eafc6cd) - Avoid large replication lag due to FPI WAL records from hintbits (Ashwin Agrawal) [#802](https://github.com/apache/cloudberry/pull/802)
* [`9c085803156`](https://github.com/apache/cloudberry/commit/9c085803156520127ba8593f3e8a0664c53c920b) - Reduce test runtime of src/test/modules/snapshot_too_old. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`e92b599526e`](https://github.com/apache/cloudberry/commit/e92b599526e268f7315025ebf96357f0c5786819) - Be more wary about 32-bit integer overflow in pg_stat_statements. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`db8a250295f`](https://github.com/apache/cloudberry/commit/db8a250295fd226ab30bea58d87c859bd3f3384d) - Check maximum number of columns in function RTEs, too. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`208b70ef324`](https://github.com/apache/cloudberry/commit/208b70ef324c5afa9684dac551f9e4cc8b46ebb4) - Fix error reporting after ioctl() call with pg_upgrade --clone (Michael Paquier) [#802](https://github.com/apache/cloudberry/pull/802)
* [`caabc899b5a`](https://github.com/apache/cloudberry/commit/caabc899b5af023b23825eee745eee008d25b054) - Gang loss with gp_dist_wait_status during running transaction. (Dennis Kovalenko) [#802](https://github.com/apache/cloudberry/pull/802)
* [`e382864083e`](https://github.com/apache/cloudberry/commit/e382864083e3590b4f0a70bded9d14e333af846e) - In transformRowExpr(), check for too many columns in the row. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`76d75e260a8`](https://github.com/apache/cloudberry/commit/76d75e260a847ed6e93a4ae362ad949814417aad) - Force immediate commit after CREATE DATABASE etc in extended protocol. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`73da0f32f38`](https://github.com/apache/cloudberry/commit/73da0f32f3860cb9ca128221761bc49929586deb) - Remove palloc() call in ResWaitOnLock (#13701) (dh-cloud) [#802](https://github.com/apache/cloudberry/pull/802)
* [`ed8778b318b`](https://github.com/apache/cloudberry/commit/ed8778b318bc97e0b53f3fb74a985c338e0099fa) - gpcheckcat needs to reset GV.retcode before starting to check a database (#13735) (Dev Swaroop Chattopadhyay) [#802](https://github.com/apache/cloudberry/pull/802)
* [`055f1c00c9e`](https://github.com/apache/cloudberry/commit/055f1c00c9ee7b20596986ed78d869ba817dcfe0) - Fix assertion failure and segmentation fault in backup code. (Fujii Masao) [#802](https://github.com/apache/cloudberry/pull/802)
* [`cdf4171c88b`](https://github.com/apache/cloudberry/commit/cdf4171c88b871f0f041b72686a9077338348f8c) - Prevent BASE_BACKUP in the middle of another backup in the same session. (Fujii Masao) [#802](https://github.com/apache/cloudberry/pull/802)
* [`1c88280b3d8`](https://github.com/apache/cloudberry/commit/1c88280b3d87c9bce6f8c0991936b93f1bb35182) - Fix unit of explain sort info (yjhjstz) [#802](https://github.com/apache/cloudberry/pull/802)
* [`4a1c1b08504`](https://github.com/apache/cloudberry/commit/4a1c1b085042db92ef24d68c580cea19070c6733) - Re-add SPICleanup for ABI compatibility in stable branch (Peter Eisentraut) [#802](https://github.com/apache/cloudberry/pull/802)
* [`d3cbfb9dad1`](https://github.com/apache/cloudberry/commit/d3cbfb9dad13b9bb08e94087631372f4f3189875) - AO read: Avoid use-before-assignment in debug print (Soumyadeep Chakraborty) [#802](https://github.com/apache/cloudberry/pull/802)
* [`f25510b8174`](https://github.com/apache/cloudberry/commit/f25510b817408248556ad3e44f13ebed74319b31) - postgres_fdw: set search_path to 'pg_catalog' while deparsing constants. (Tom Lane) [#802](https://github.com/apache/cloudberry/pull/802)
* [`8fb316acec1`](https://github.com/apache/cloudberry/commit/8fb316acec195e2651ad3e2289c55ab65ff56c3c) - Remove fixme comment in aset.c:AllocSetTransferAccounting. (#13786) (Wenlin Zhang) [#802](https://github.com/apache/cloudberry/pull/802)
* [`ab63a723391`](https://github.com/apache/cloudberry/commit/ab63a723391779aa8c9fce386f062bf033872cc3) - Make dsm_impl_posix_resize more future-proof. (Thomas Munro) [#802](https://github.com/apache/cloudberry/pull/802)
* [`f47d859fcd1`](https://github.com/apache/cloudberry/commit/f47d859fcd19d6e8b483a0f594b630b1f6292a73) - Don't clobber postmaster sigmask in dsm_impl_resize. (Thomas Munro) [#802](https://github.com/apache/cloudberry/pull/802)
* [`e74d40166e2`](https://github.com/apache/cloudberry/commit/e74d40166e2da79712712145a443c1751ce82fae) - [initdb] remove ending \n from pg_log_error (#13794) (Junwang Zhao) [#802](https://github.com/apache/cloudberry/pull/802)
* [`5f4a4a92e20`](https://github.com/apache/cloudberry/commit/5f4a4a92e2075c9201fa48687829a8c3868a57c0) - Block signals while allocating DSM memory. (Thomas Munro) [#802](https://github.com/apache/cloudberry/pull/802)
* [`ccb1f189d74`](https://github.com/apache/cloudberry/commit/ccb1f189d741fef2c3f87f92a271dbd50ffbbcaa) - Fix windows clients compilation of libpgcommon (Brent Doil) [#802](https://github.com/apache/cloudberry/pull/802)
* [`1bf774ef22a`](https://github.com/apache/cloudberry/commit/1bf774ef22af1afe7a592887563218a724b710ec) - Fix lock assertions in dshash.c. (Thomas Munro) [#802](https://github.com/apache/cloudberry/pull/802)
* [`56648eb9b46`](https://github.com/apache/cloudberry/commit/56648eb9b46199517870c51887246fdfd31a3cd6) - Fix \watch's interaction with libedit on ^C. (Thomas Munro) [#802](https://github.com/apache/cloudberry/pull/802)
* [`07670d4699a`](https://github.com/apache/cloudberry/commit/07670d4699abc80208ce836944d8c2628014f7b3) - [gpdemo] only create mirror directories when needed (#13777) (Junwang Zhao) [#802](https://github.com/apache/cloudberry/pull/802)
* [`0aabbe2b607`](https://github.com/apache/cloudberry/commit/0aabbe2b607f79b9081347957cf31a34491a57b6) - Fix alias matching in transformLockingClause(). (Dean Rasheed) [#802](https://github.com/apache/cloudberry/pull/802)
* [`f48d78c28fa`](https://github.com/apache/cloudberry/commit/f48d78c28fa684c931359c86102c70cac287ec03) - Add writing CHECKPOINT to gpactivatestandby (Ashwin Agrawal) [#802](https://github.com/apache/cloudberry/pull/802)
* [`e685db063e8`](https://github.com/apache/cloudberry/commit/e685db063e88bd813293fd5afdbf79f10282711c) - Fix idle_in_transaction_session_timeout works on QE (#13547) (dreamedcheng) [#802](https://github.com/apache/cloudberry/pull/802)
* [`8f94c0a1a71`](https://github.com/apache/cloudberry/commit/8f94c0a1a71d5e8fdcd7fe22a2800331c99d8bd8) - Just use default on commit behavior to child tables when we create partition table (#13687) (Zhenglong Li) [#802](https://github.com/apache/cloudberry/pull/802)
* [`bc3025bafbb`](https://github.com/apache/cloudberry/commit/bc3025bafbb23de97fc446a4d8cbadd2c006b466) - Use pg_ctl -t secs option for promote in gpactivatestandby (Ashwin Agrawal) [#802](https://github.com/apache/cloudberry/pull/802)
* [`94dddae995a`](https://github.com/apache/cloudberry/commit/94dddae995a956ef2debd517b89feefabd83c269) - master: fix gpfdist crash (#13750) (hyongtao-db) [#802](https://github.com/apache/cloudberry/pull/802)
* [`e3a302704cb`](https://github.com/apache/cloudberry/commit/e3a302704cb295023c3a9654acc8b4175619dd55) - ecpglib: call newlocale() once per process. (Noah Misch) [#802](https://github.com/apache/cloudberry/pull/802)
* [`11074eb3e94`](https://github.com/apache/cloudberry/commit/11074eb3e946b5687a02e75623f0e75d90c2e235) - Fix singlenode workfile_mgr_test test (yangjianghua) [#797](https://github.com/apache/cloudberry/pull/797)
* [`0494308e320`](https://github.com/apache/cloudberry/commit/0494308e320dc058163678cc14f83065ec55714c) - Harden dsm_impl.c against unexpected EEXIST. (Thomas Munro) [#797](https://github.com/apache/cloudberry/pull/797)
* [`549dac7fc00`](https://github.com/apache/cloudberry/commit/549dac7fc0066a6ff66555c8c15e05f9a892adb2) - aoblkdir: Remove dead function prototypes (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`c85510a13b9`](https://github.com/apache/cloudberry/commit/c85510a13b9ee49127627a8eed1d8a2a1d57b461) - blkdir: Rename CurrentSegmentFile and CurrentBlock (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`29bb0e77d5e`](https://github.com/apache/cloudberry/commit/29bb0e77d5e33d2bf66ca9a767622725fd6b7daa) - Gut the appendoptimized_basic test (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`88b431ce261`](https://github.com/apache/cloudberry/commit/88b431ce26108c5f8240bdd5823fb86127e7970d) - Fix gppkg's coordinator hook not execute. (Sasasu) [#797](https://github.com/apache/cloudberry/pull/797)
* [`866ec2b91ae`](https://github.com/apache/cloudberry/commit/866ec2b91ae4301595ba42e0e5bb860d6ea672e1) - fix fixme in planner about calculating dNumGroups for grouping sets (zxuejing) [#797](https://github.com/apache/cloudberry/pull/797)
* [`0ea097e2f1c`](https://github.com/apache/cloudberry/commit/0ea097e2f1ce1c57a060040038dae8d4954f8eeb) - Fix small typos in config parameters description. (#13697) (Anton Kurochkin) [#797](https://github.com/apache/cloudberry/pull/797)
* [`59715849fdf`](https://github.com/apache/cloudberry/commit/59715849fdf4ad818154502d6640220d29c24d12) - [typo] fix a typo to make the comment more precise (#13702) (Junwang Zhao) [#797](https://github.com/apache/cloudberry/pull/797)
* [`11f5f19dc92`](https://github.com/apache/cloudberry/commit/11f5f19dc9206ce809146131fee358f45f1ea8f7) - remove a superfluous variable (#13681) (Junwang Zhao) [#797](https://github.com/apache/cloudberry/pull/797)
* [`abd1545a6d0`](https://github.com/apache/cloudberry/commit/abd1545a6d0550470c9053d3353535b1b1d62d1e) - Add logicalEof to errdetail in OpenAOSegmentFile (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`bae05aa8b87`](https://github.com/apache/cloudberry/commit/bae05aa8b87e954e68d24bff759973aa6c343c12) - Fix a flakiness in test recovery test overwrite_contrecord (Huansong Fu) [#797](https://github.com/apache/cloudberry/pull/797)
* [`f19bd8db519`](https://github.com/apache/cloudberry/commit/f19bd8db519bfd18c069f310e243f50061968b3b) - [README][typo] fix gpdemo path typo (#13670) (Junwang Zhao) [#797](https://github.com/apache/cloudberry/pull/797)
* [`b58ca90db5f`](https://github.com/apache/cloudberry/commit/b58ca90db5f2da8762202ba46bddefae5a0ec878) - Resolve GPDB_12_MERGE_FIXME in system_views.sql (#13469) (QingMa) [#797](https://github.com/apache/cloudberry/pull/797)
* [`7faf3748215`](https://github.com/apache/cloudberry/commit/7faf3748215a442e4d55d99c678fcb1c6f796962) - Print errno for QE->QD dispatch checks (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`2da8ab93a7b`](https://github.com/apache/cloudberry/commit/2da8ab93a7b9cdfa711de2aa3d86620049443a32) - Allow max_replication_slots=0 (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`4f08c7fdac7`](https://github.com/apache/cloudberry/commit/4f08c7fdac7d3922e7c630940090ec015c75c933) - Check recoveryTargetAction before put PM_STATUS_STANDBY (Kate Dontsova) [#797](https://github.com/apache/cloudberry/pull/797)
* [`e71edfc1ed9`](https://github.com/apache/cloudberry/commit/e71edfc1ed9f54d44f79aca2ab31cc97a58df170) - pylib: Fix the error while doing remote file operation (#12433) (Sasasu) [#797](https://github.com/apache/cloudberry/pull/797)
* [`0e5498e2db9`](https://github.com/apache/cloudberry/commit/0e5498e2db9f45feed4219e92152a519b0f16181) - Resolve GPDB_12_MERGE_FIXME in pathnode.c (#13466) (QingMa) [#797](https://github.com/apache/cloudberry/pull/797)
* [`56fd24223cb`](https://github.com/apache/cloudberry/commit/56fd24223cb22332d196d0849cb092b949778a94) - Validate sql exec_location in CreateFunction (#13431) (QingMa) [#797](https://github.com/apache/cloudberry/pull/797)
* [`daf51fc1c2e`](https://github.com/apache/cloudberry/commit/daf51fc1c2e7aaa7904c27a1455a8b17781ea727) - Fix export snapshot test (Brent Doil) [#797](https://github.com/apache/cloudberry/pull/797)
* [`edbe8f473c0`](https://github.com/apache/cloudberry/commit/edbe8f473c04c6525cb03cdeffacde59d1bc6248) - Check if the source snapshot already has a distributed snapshot. (Soumyadeep Chakraborty) [#797](https://github.com/apache/cloudberry/pull/797)
* [`04d7ba708c3`](https://github.com/apache/cloudberry/commit/04d7ba708c3dc91bb854acdb994f810759d29650) - Add distributed snapshot support to pg_export_snapshot (Brent Doil) [#797](https://github.com/apache/cloudberry/pull/797)
* [`b54afa4602a`](https://github.com/apache/cloudberry/commit/b54afa4602af9897a6ab909b67a3dcdb29d149e0) - Add test case with ORCA query optimizer enabled (Ed Espino) [#800](https://github.com/apache/cloudberry/pull/800)
* [`ee9207079ad`](https://github.com/apache/cloudberry/commit/ee9207079add3778e17039685a78d680fb95bf4c) - Add isolation2 and parallel-retrieve test matrix entries (#801) (Ed Espino) [#801](https://github.com/apache/cloudberry/pull/801)
* [`a03c0bf7902`](https://github.com/apache/cloudberry/commit/a03c0bf7902523b0d9f5930850d38d32e76b18c5) - Update branch check with current test job names (#799) (Ed Espino) [#799](https://github.com/apache/cloudberry/pull/799)
* [`f2afaadadd1`](https://github.com/apache/cloudberry/commit/f2afaadadd10afd044d70cd153b07d05eeef4cc4) - Process 'T_CustomScanState' in ExecSquelchNode (#768) (Xiaoran Wang) [#768](https://github.com/apache/cloudberry/pull/768)
* [`28eb91a62c6`](https://github.com/apache/cloudberry/commit/28eb91a62c6c98f2075b72e30d62439bd8123f65) - Enhance Build Pipeline with Debug and Core Analysis Support (#784) (Ed Espino) [#784](https://github.com/apache/cloudberry/pull/784)
* [`f84eb2cfac5`](https://github.com/apache/cloudberry/commit/f84eb2cfac5a23b6470a79641ea6e4a07962aee6) - Revert: "Vacuum auxiliary TOAST should not be dispatched." (GongXun) [#794](https://github.com/apache/cloudberry/pull/794)
* [`3d37b925888`](https://github.com/apache/cloudberry/commit/3d37b9258886a77d9ebe23f88772783c179aa8e1) - Refactor setDistributedTransactionContext (Soumyadeep Chakraborty) [#788](https://github.com/apache/cloudberry/pull/788)
* [`74fb1386c86`](https://github.com/apache/cloudberry/commit/74fb1386c865c5ad22fc0424084dc4056c28b62f) - Helper for sizing a distributed snapshot's xip[] (Soumyadeep Chakraborty) [#788](https://github.com/apache/cloudberry/pull/788)
* [`ecb66628d65`](https://github.com/apache/cloudberry/commit/ecb66628d658388c169cccd87479723099baa742) - fix compiler warning for genfile.c,util.c and nodeShareInputScan.c (wenru yan) [#788](https://github.com/apache/cloudberry/pull/788)
* [`50b2124ae8c`](https://github.com/apache/cloudberry/commit/50b2124ae8ce60cf06ace69b2348c4c4e3b80ad0) - gpstate -e: Remove progress for killed recoverseg (#13412) (ravoorsh) [#788](https://github.com/apache/cloudberry/pull/788)
* [`e4f892ad1a1`](https://github.com/apache/cloudberry/commit/e4f892ad1a12a27dc707b6f019fad8eea90735bf) - Fix flaky test checkpoint_dtx_info (Huansong Fu) [#788](https://github.com/apache/cloudberry/pull/788)
* [`31e8870594f`](https://github.com/apache/cloudberry/commit/31e8870594f8c21418628a859f8789724a759158) - use winner QE's nfiltered1 and nfiltered2 in the output of EXPLAIN ANALYZE (#13417) (SmartKeyerror) [#788](https://github.com/apache/cloudberry/pull/788)
* [`ecc0b869c4f`](https://github.com/apache/cloudberry/commit/ecc0b869c4fabd21fb9754397c388be35d78bc10) - remove useless lockmode upgrade in inherit.c (#13434) (SmartKeyerror) [#788](https://github.com/apache/cloudberry/pull/788)
* [`a4179417310`](https://github.com/apache/cloudberry/commit/a4179417310f16ede626db787f75c06a72504616) - Try to lookup commands by $PATH before iterating over hardcoded paths. (#13361) (Xing Guo) [#788](https://github.com/apache/cloudberry/pull/788)
* [`0deffcbac4c`](https://github.com/apache/cloudberry/commit/0deffcbac4c369e88b2878e4d9ffba96963e37f0) - ORCA: allow not enforce distribution key in 3-stage aggregate (#776) (jiaqizho) [#776](https://github.com/apache/cloudberry/pull/776)
* [`57a5320e9a5`](https://github.com/apache/cloudberry/commit/57a5320e9a5fd0e29694408996289222d7d2be7c) - Forbid users from altering the AS part of the ALTER TASK command. (Zhang Mingli) [#778](https://github.com/apache/cloudberry/pull/778)
* [`d42a8c6298c`](https://github.com/apache/cloudberry/commit/d42a8c6298c433436b9d0206c4c3acf5c8ccc096) - Do not match non vars in inner plan's target for LASJ_NOTIN. (Zhenghua Lyu) [#772](https://github.com/apache/cloudberry/pull/772)
* [`a8729fc9f36`](https://github.com/apache/cloudberry/commit/a8729fc9f36d71f5ce2e5caec8e270c52096487c) - Fix banning window agg in recursive queries. (Zhenghua Lyu) [#772](https://github.com/apache/cloudberry/pull/772)
* [`a6b319cb4d4`](https://github.com/apache/cloudberry/commit/a6b319cb4d424abe6cba0e8f6ff6d8f1700f3ecc) - Fix flaky testcase correlated_subquery (David Kimura) [#772](https://github.com/apache/cloudberry/pull/772)
* [`1886c4e79dc`](https://github.com/apache/cloudberry/commit/1886c4e79dc81f24630d968bb7abb912ba22b3b5) - Fix "missing continuation record" after standby promotion (Alvaro Herrera) [#772](https://github.com/apache/cloudberry/pull/772)
* [`2f6e6287886`](https://github.com/apache/cloudberry/commit/2f6e6287886bfac03bbb559447a3a88e3d05e00a) - Remove platform specific limitation from msg type recognition logic (hughcapet) [#772](https://github.com/apache/cloudberry/pull/772)
* [`7c533587c84`](https://github.com/apache/cloudberry/commit/7c533587c841e6eb1737d2336e1ffd370bf8a265) - Increase the timeout of isolation2 GlobalShellExecutor (Adam Lee) [#772](https://github.com/apache/cloudberry/pull/772)
* [`d0cc0909822`](https://github.com/apache/cloudberry/commit/d0cc0909822f303b601e1c4d8d2be3b2fa8d3715) - Dispatch temporary tablespace id to all Gangs. (Sasasu) [#772](https://github.com/apache/cloudberry/pull/772)
* [`23165023668`](https://github.com/apache/cloudberry/commit/2316502366817616081ad217701663fed471f232) - Fix temporary namespace access in guc.sql (Sasasu) [#772](https://github.com/apache/cloudberry/pull/772)
* [`3ec30131c41`](https://github.com/apache/cloudberry/commit/3ec30131c41d3c2ef696217e0d66c4039d15cb7c) - support implementing custom storage manager in extension (GongXun) [#763](https://github.com/apache/cloudberry/pull/763)
* [`9319f8ecaf7`](https://github.com/apache/cloudberry/commit/9319f8ecaf77ccdcb95242a5cd540ccbd323953b) - Process shared_preload_libraries in single-user mode. (Jeff Davis) [#763](https://github.com/apache/cloudberry/pull/763)
* [`735d6a4be0e`](https://github.com/apache/cloudberry/commit/735d6a4be0ec01f8ad1be1fd26da7162a9c24e90) - Collect query info when 'ExecCheckXactReadOnly' reports error (wangxiaoran) [#769](https://github.com/apache/cloudberry/pull/769)
* [`d162ebd28c3`](https://github.com/apache/cloudberry/commit/d162ebd28c3bfc76ee6e3a88399c9d3219889d45) - Add EXTERNAL TABLE to autocomplete (#15350) (Kirill Glisnky) [#774](https://github.com/apache/cloudberry/pull/774)
* [`c5428c57009`](https://github.com/apache/cloudberry/commit/c5428c57009f48873c30a4382a383556892080ad) - Open relation SMGR before use (reshke) [#785](https://github.com/apache/cloudberry/pull/785)
* [`2161ec2c893`](https://github.com/apache/cloudberry/commit/2161ec2c893d2fd65e91476890f5659226bcf7c5) - Allow using table access method when creating partition table (Hao Wu) [#764](https://github.com/apache/cloudberry/pull/764)
* [`04e83483319`](https://github.com/apache/cloudberry/commit/04e8348331905f53b9b43ddd5815395cacc3dc39) - Doc: update the SECURITY.md (Dianjin Wang) [#770](https://github.com/apache/cloudberry/pull/770)
* [`8156fddc575`](https://github.com/apache/cloudberry/commit/8156fddc57526531dbbd02bfa87e241306712b3e) - Docs: avoid confusing use of the word "synchronized" (David Rowley) [#777](https://github.com/apache/cloudberry/pull/777)
* [`6264eba3929`](https://github.com/apache/cloudberry/commit/6264eba3929960b09991e34730731cab565d779d) - Fix potential nullptr dereference issue. (#12780) (Xing Guo) [#777](https://github.com/apache/cloudberry/pull/777)
* [`d649959b1f3`](https://github.com/apache/cloudberry/commit/d649959b1f3bf4e19da0dc5a46f5e6aa97ef6cbf) - gprecoverseg rebalance is failed with timeout while promoting mirrors (hari krishna) [#777](https://github.com/apache/cloudberry/pull/777)
* [`613e9139108`](https://github.com/apache/cloudberry/commit/613e913910825b5e8a35491a450e8ad744d29162) - Update the comments for commit 9116c93 (Huansong Fu) [#777](https://github.com/apache/cloudberry/pull/777)
* [`f9a2cd0bb89`](https://github.com/apache/cloudberry/commit/f9a2cd0bb8975c715bb5555c3d41d7fff38812b3) - Restore visibility of some GUCs to be viewable from pg_settings (Jimmy Yih) [#777](https://github.com/apache/cloudberry/pull/777)
* [`3503adc33fd`](https://github.com/apache/cloudberry/commit/3503adc33fdb6aee9d7b4e91a24d0bcd6f13c580) - doc: Fix typo in ANALYZE documentation (Daniel Gustafsson) [#777](https://github.com/apache/cloudberry/pull/777)
* [`bbc8cc30985`](https://github.com/apache/cloudberry/commit/bbc8cc30985730961ba0c65c4bda13119b09022d) - resolve update distributed key with oids fixme (#13343) (xuejing zhao) [#777](https://github.com/apache/cloudberry/pull/777)
* [`82e85d8b58e`](https://github.com/apache/cloudberry/commit/82e85d8b58e872f56454173d61e8425d8948b771) - drop s3 external table (#13334) (Huiliang.liu) [#777](https://github.com/apache/cloudberry/pull/777)
* [`05e3a73e706`](https://github.com/apache/cloudberry/commit/05e3a73e706b1b9ef5a79871dc1a2d4cd10d78aa) - Fix test case notin. (Zhenghua Lyu) [#777](https://github.com/apache/cloudberry/pull/777)
* [`0942e6c648e`](https://github.com/apache/cloudberry/commit/0942e6c648e089a724ef920eda02ee86ad3736b7) - remove duplicate codes in ExecSetParamPlan (#13328) (SmartKeyerror) [#777](https://github.com/apache/cloudberry/pull/777)
* [`db24b8c4b12`](https://github.com/apache/cloudberry/commit/db24b8c4b1277749a8af81cb914edffbfad8ffe6) - add distribution key for gpload staging table (#13163) (xiaoxiao) [#777](https://github.com/apache/cloudberry/pull/777)
* [`d992c8e3f7f`](https://github.com/apache/cloudberry/commit/d992c8e3f7faad5d886c5ac1bf95b685e97bd84b) - resolve pull_up_sublinks fixme (#13317) (xuejing zhao) [#777](https://github.com/apache/cloudberry/pull/777)
* [`d29a03a10cc`](https://github.com/apache/cloudberry/commit/d29a03a10cc5872354201a876b64ccc593275dc5) - Document autoanalyze limitations for partitioned tables (Tomas Vondra) [#777](https://github.com/apache/cloudberry/pull/777)
* [`b4db9559671`](https://github.com/apache/cloudberry/commit/b4db95596711a0913391e03052c89590698c0701) - Fix error message for unexisted tag (#779) (reshke) [#779](https://github.com/apache/cloudberry/pull/779)
* [`9bd78472350`](https://github.com/apache/cloudberry/commit/9bd784723501b74202ed5baff4ee2287a38e723f) - Add pg_dynamic_tables system view. (#771) (Zhang Mingli) [#771](https://github.com/apache/cloudberry/pull/771)
* [`87503a76513`](https://github.com/apache/cloudberry/commit/87503a76513e41b7e1998c805b1a5ddad5301551) - Format taskcmds.c to follow PostgreSQL coding style. (#775) (Zhang Mingli) [#775](https://github.com/apache/cloudberry/pull/775)
* [`adce2eb39c5`](https://github.com/apache/cloudberry/commit/adce2eb39c5b787dbd3287393b95546f76adb298) - Restore removal of unused directory (#773) (Ed Espino) [#773](https://github.com/apache/cloudberry/pull/773)
* [`c6c03b0fd65`](https://github.com/apache/cloudberry/commit/c6c03b0fd652273a6564df796ff5aa7f911f5617) - test: support multiple make targets in single matrix entry (#766) (Ed Espino) [#766](https://github.com/apache/cloudberry/pull/766)
* [`cb671fe1b78`](https://github.com/apache/cloudberry/commit/cb671fe1b78d0b1d3d13142679661915eb14b275) - Fix some compilation and habitual thinking errors. (zhangwenchao) [#767](https://github.com/apache/cloudberry/pull/767)
* [`20cbc1d1289`](https://github.com/apache/cloudberry/commit/20cbc1d1289e0396903859f3676fc1959d34bf05) - [AQUMV] Answer Aggregation Query Directly. (#705) (Zhang Mingli) [#705](https://github.com/apache/cloudberry/pull/705)
* [`6e93f27f78f`](https://github.com/apache/cloudberry/commit/6e93f27f78f8586fdd5d98da4942681e20a5c76a) - Fix type error of PG_PASSWORD_HISTORY_H macro. (zhangwenchao) [#762](https://github.com/apache/cloudberry/pull/762)
* [`1a6697ee4d8`](https://github.com/apache/cloudberry/commit/1a6697ee4d8271bbee4cc4689a08f8fa98e155a0) - fix coredum when use group by with jit enabled PS: It should be noted that the elementType of v_resnullp needs to be the same as the number of bytes occupied by v_group_id, otherwise llvm-assert will be triggered. (ZhangHuiGui) [#760](https://github.com/apache/cloudberry/pull/760)
* [`cd4a4d2cf66`](https://github.com/apache/cloudberry/commit/cd4a4d2cf669b37a0c8db8ab98df5958facde3ca) - [7X] Skip ssh if segments are on the same host with coordinator. (#16812) (Xing Guo) [#747](https://github.com/apache/cloudberry/pull/747)
* [`0a2d22feb46`](https://github.com/apache/cloudberry/commit/0a2d22feb468c4ffc679483f8a3240c1edd3eeaa) - Add new option start_new_session to Command class (Nihal Jain) [#747](https://github.com/apache/cloudberry/pull/747)
* [`07c4d130e17`](https://github.com/apache/cloudberry/commit/07c4d130e172a8ad515d8c21457eb54fcce47bf0) - gprecoverseg: Add ability to handle interrupts (Nihal Jain) [#747](https://github.com/apache/cloudberry/pull/747)
* [`56632f55f7b`](https://github.com/apache/cloudberry/commit/56632f55f7be21e54e377d94addea22d6499c38e) - Dynamic Table. (#725) (Zhang Mingli) [#725](https://github.com/apache/cloudberry/pull/725)
* [`470a8f2d664`](https://github.com/apache/cloudberry/commit/470a8f2d66490f26b6d0767781453b60059734a3) - Ignore temp files. (#755) (Zhang Mingli) [#755](https://github.com/apache/cloudberry/pull/755)
* [`7a6a6c96030`](https://github.com/apache/cloudberry/commit/7a6a6c96030e521e3f70e07e8063b263aaf69000) - Make gp_matview_aux and gp_matview_tables unshared catalog. (#756) (Zhang Mingli) [#756](https://github.com/apache/cloudberry/pull/756)
* [`17b210d7117`](https://github.com/apache/cloudberry/commit/17b210d7117b29e3fb80387d7648dcc2f5d49d0f) - [ORCA] optimize eliminate self comparison (wangxiaoran) [#722](https://github.com/apache/cloudberry/pull/722)
* [`55c70e4d8e6`](https://github.com/apache/cloudberry/commit/55c70e4d8e60ac82a68439d934725282d044a4ee) - Wrong results by ORCA when NULL TEST on LOJ (#15358) (Hari krishna) [#722](https://github.com/apache/cloudberry/pull/722)
* [`52db9dfcb7b`](https://github.com/apache/cloudberry/commit/52db9dfcb7ba3abbcb7991652eb8652592dceadf) - Update Greetings Workflow for Apache Cloudberry (Incubating) (Ed Espino) [#754](https://github.com/apache/cloudberry/pull/754)
* [`47f626c6cc7`](https://github.com/apache/cloudberry/commit/47f626c6cc7fc6a7dd320e0c170f133a33b69952) - Avoid REFREH fast path if matview has foreign tables. (Zhang Mingli) [#702](https://github.com/apache/cloudberry/pull/702)
* [`7be4f7fe7c7`](https://github.com/apache/cloudberry/commit/7be4f7fe7c751120ae47257aba0f6972865281f0) - Enable answer query using Materialized View for external table. (Zhang Mingli) [#702](https://github.com/apache/cloudberry/pull/702)
* [`fa05a8f1a7c`](https://github.com/apache/cloudberry/commit/fa05a8f1a7cb8807a16aa869c0bcd95e63757178) - Throws ERROR when statement_mem is set to greater than max_statement_mem. (FairyFar) [#752](https://github.com/apache/cloudberry/pull/752)
* [`69462b0425b`](https://github.com/apache/cloudberry/commit/69462b0425b4b17a0c47ec181f98d893fcd49174) - ci: add PR edit trigger and improve CI skip instructions (#751) (Ed Espino) [#751](https://github.com/apache/cloudberry/pull/751)
* [`ca642bfe72c`](https://github.com/apache/cloudberry/commit/ca642bfe72cbbe0dd59346f2da9449e9a2e8d5d4) - Optimize DISTINCT, ORDER BY clause when Aggregation without Group By. (Zhang Mingli) [#685](https://github.com/apache/cloudberry/pull/685)
* [`747b953ab91`](https://github.com/apache/cloudberry/commit/747b953ab91cc0634bbdc31dc1ec2ed5cd31fa04) - Fix unrecognized node type of pg_task (roseduan) [#744](https://github.com/apache/cloudberry/pull/744)
* [`a1e9e2d4a93`](https://github.com/apache/cloudberry/commit/a1e9e2d4a9326092985f82f79bd4155db044cfa4) - Adding additional test suite. (#735) (Ed Espino) [#735](https://github.com/apache/cloudberry/pull/735)
* [`526ff094a8e`](https://github.com/apache/cloudberry/commit/526ff094a8e880289b66661a6e1b4c9ee4a523ff) - Fix maybe uniniatilzed access of variables (reshke) [#709](https://github.com/apache/cloudberry/pull/709)
* [`650a7311d57`](https://github.com/apache/cloudberry/commit/650a7311d578bb510a31c4ddc2c9761fdaac1254) - Fix REFRESH fast path. (Zhang Mingli) [#720](https://github.com/apache/cloudberry/pull/720)
* [`71c6677dd66`](https://github.com/apache/cloudberry/commit/71c6677dd66b71281228e22fa59a679166df4637) - Fix compile error when disable-faultinjector (roseduan) [#660](https://github.com/apache/cloudberry/pull/660)
* [`48591423d31`](https://github.com/apache/cloudberry/commit/48591423d310e2ae189abd1f3d13bbc861d25e6a) - Fix directory table ci test unstable. (zhangwenchao) [#733](https://github.com/apache/cloudberry/pull/733)
* [`6df030cc59c`](https://github.com/apache/cloudberry/commit/6df030cc59c4e31b97efbe646eb32c4f7b434e8f) - test: Add single-node test configurations to CI pipeline (Ed Espino) [#736](https://github.com/apache/cloudberry/pull/736)
* [`43a0c43fc00`](https://github.com/apache/cloudberry/commit/43a0c43fc00f2f1b31e51e0e83bd9b17b139c8ca) - Fix test cases for gp-style partition in single-node mode (Hao Wu) [#736](https://github.com/apache/cloudberry/pull/736)
* [`6ca10b90554`](https://github.com/apache/cloudberry/commit/6ca10b905544360c770645acb7a0e7f02f3542a8) - [ORCA] Avoid pushdown of predicate with set-returning function (#14201) (David Kimura) [#708](https://github.com/apache/cloudberry/pull/708)
* [`f37fec27781`](https://github.com/apache/cloudberry/commit/f37fec2778121fb53de7d9746dce1be35e362a9e) - Fix qp_with_clause testcase without asserts (#13878) (David Kimura) [#708](https://github.com/apache/cloudberry/pull/708)
* [`bc5b47631a1`](https://github.com/apache/cloudberry/commit/bc5b47631a1072fd84d21c451ac0a4e2286288dd) - Doc: update the Apache Security email address (Dianjin Wang) [#730](https://github.com/apache/cloudberry/pull/730)
* [`11333c0b4d3`](https://github.com/apache/cloudberry/commit/11333c0b4d3a4962b0a6610ceb5b6d7a12e45ec4) - Doc: update basic community files for ASF (Dianjin Wang) [#696](https://github.com/apache/cloudberry/pull/696)
* [`d0454c3ccba`](https://github.com/apache/cloudberry/commit/d0454c3ccba02b7b4bad7e44bddc24e9cfff2ce5) - Use revents in struct pollfd to check the result of poll() (YueZhang) [#710](https://github.com/apache/cloudberry/pull/710)
* [`9a670c1a556`](https://github.com/apache/cloudberry/commit/9a670c1a5564d35927f85252ed965600da1a343d) - ci: Re-enable tests with increased shared memory (#727) (Ed Espino) [#727](https://github.com/apache/cloudberry/pull/727)
* [`43c915c1584`](https://github.com/apache/cloudberry/commit/43c915c1584830dadc9e47710f545324bec7c48c) - Doc: link 404 error caused by org changed (yihong0618) [#707](https://github.com/apache/cloudberry/pull/707)
* [`782d9b799e7`](https://github.com/apache/cloudberry/commit/782d9b799e72079c9170b8934a1950b8459ac25e) - Inherit parent's options for child like gp-style partition table (#695) (Hao Wu) [#695](https://github.com/apache/cloudberry/pull/695)
* [`111a7bc62a1`](https://github.com/apache/cloudberry/commit/111a7bc62a1515647fea66acedc99712a936c4a0) - Use `BIO_{get,set}_app_data` instead of `BIO_{get,set}_data`. (#716) (zhangwenchao) [#716](https://github.com/apache/cloudberry/pull/716)
* [`f92faf0fb04`](https://github.com/apache/cloudberry/commit/f92faf0fb0437c9df590090faf0845125fe97f13) - Remove DockerHub auth from workflow (#719) (Ed Espino) [#719](https://github.com/apache/cloudberry/pull/719)
* [`22856f44ea6`](https://github.com/apache/cloudberry/commit/22856f44ea6349894b0b70875370a90c4d133cc2) - ci: add core Apache Cloudberry (incubating) build and test workflow (#714) (Ed Espino) [#714](https://github.com/apache/cloudberry/pull/714)
* [`2818d834927`](https://github.com/apache/cloudberry/commit/2818d834927cb8e7d0b1d2f594abbc7b87b9e021) - feat: Add .asf.yaml for Apache infrastructure integration (#713) (Ed Espino) [#713](https://github.com/apache/cloudberry/pull/713)
* [`f6ba05ff428`](https://github.com/apache/cloudberry/commit/f6ba05ff42855a5112334aaabe6cfc0eb3046270) - Use interface for storage interactions in Append-optimized TAM (reshke) [#650](https://github.com/apache/cloudberry/pull/650)
* [`c9b6cca9fa9`](https://github.com/apache/cloudberry/commit/c9b6cca9fa912f45672d1c25eabe2c439554a0ce) - Enhace tab completion for partition cmds (#692) (reshke) [#692](https://github.com/apache/cloudberry/pull/692)
* [`69ba2c9c423`](https://github.com/apache/cloudberry/commit/69ba2c9c4237d8fb296ad9035ca95aba8987ef95) - Rewrite tryOpenTable to correct support CB (Leonid Borchuk) [#688](https://github.com/apache/cloudberry/pull/688)
* [`9787bfc8c1f`](https://github.com/apache/cloudberry/commit/9787bfc8c1f666d1eca93a6f67011c5428f7d2e3) - [ORCA] Fix memory leaks in translator (#13656) (David Kimura) [#688](https://github.com/apache/cloudberry/pull/688)
* [`42cbb008067`](https://github.com/apache/cloudberry/commit/42cbb008067ad682f13851415c36b07d990d0a63) - Clean up logic in CdbTryOpenTable. (Zhenghua Lyu) [#688](https://github.com/apache/cloudberry/pull/688)
* [`17767dbceb3`](https://github.com/apache/cloudberry/commit/17767dbceb3ab750ce3e97c536d117078482a076) - Avoid opening table in CondUpgradeRelLock() when possible (Huansong Fu) [#688](https://github.com/apache/cloudberry/pull/688)
* [`b2ae6854158`](https://github.com/apache/cloudberry/commit/b2ae68541586ab3f2cfd7df4a9b50066069c7e26) - Fix paramcollid for param in ORCA translator (#13302) (Sambitesh Dash) [#688](https://github.com/apache/cloudberry/pull/688)
* [`f0c4ea7ca43`](https://github.com/apache/cloudberry/commit/f0c4ea7ca43d9230de2e529ade6c6726edbbc9e1) - Fix testexpr translation of outer expr (#13296) (David Kimura) [#688](https://github.com/apache/cloudberry/pull/688)
* [`558efeac444`](https://github.com/apache/cloudberry/commit/558efeac444350bcf816e949621f11ef45922e34) - Materialize aggregations in NL Join inner child (Orhan Kislal) [#688](https://github.com/apache/cloudberry/pull/688)
* [`86118fe28cb`](https://github.com/apache/cloudberry/commit/86118fe28cbb0be2d77c29db9080858b6696c2b0) - Fix missing Redistribute on top of Split Update with Orca (Ekta Khanna) [#688](https://github.com/apache/cloudberry/pull/688)
* [`da0a27c6d35`](https://github.com/apache/cloudberry/commit/da0a27c6d35674b58a86f39357de2cb5362840d6) - [ORCA] Enable more HashAggregate alternative plans  (#13421) (David Kimura) [#688](https://github.com/apache/cloudberry/pull/688)
* [`551f5741dba`](https://github.com/apache/cloudberry/commit/551f5741dba6e592f0ab64080b1985bcae164232) - Remove overload raise function with severity_level (#13376) (David Kimura) [#688](https://github.com/apache/cloudberry/pull/688)
* [`16da3616e37`](https://github.com/apache/cloudberry/commit/16da3616e374dafc14a9be7d4d775f112d9d8839) - Fix subquery all subquery context (#13377) (David Kimura) [#688](https://github.com/apache/cloudberry/pull/688)
* [`39d92f22e84`](https://github.com/apache/cloudberry/commit/39d92f22e8472f3c4ba8b6d3f70fd42da4c06084) - Fix task timezone (roseduan) [#684](https://github.com/apache/cloudberry/pull/684)
* [`9b9dd5856d0`](https://github.com/apache/cloudberry/commit/9b9dd5856d002c43efc16e861d05514d08951a00) - Fix incorrect system target list detection for AO update (Zijie) [#603](https://github.com/apache/cloudberry/pull/603)
* [`f573ee1d39b`](https://github.com/apache/cloudberry/commit/f573ee1d39bfd90ed584358349dd66d52d8d703f) - Fix failure of ORCA non-split update for CBDB (Zijie) [#603](https://github.com/apache/cloudberry/pull/603)
* [`1659ae11ae5`](https://github.com/apache/cloudberry/commit/1659ae11ae5469dd3b4f59b7d5088d8d688772f5) - [ORCA] Implemented InPlaceUpdate to be used for updates made on non-distribution columns. (#13889) (Sanath Kumar Vobilisetty) [#603](https://github.com/apache/cloudberry/pull/603)
* [`3d8612d109f`](https://github.com/apache/cloudberry/commit/3d8612d109f890761ddf56618a9a1da0e9e7202c) - Fix predicate pushdown using cast'd column (#13770) (David Kimura) [#603](https://github.com/apache/cloudberry/pull/603)
* [`1cdccbe5760`](https://github.com/apache/cloudberry/commit/1cdccbe57609a7decf03492ebaf523676d0f63fd) - Fix non-default collation fallbacks (hari krishna) [#603](https://github.com/apache/cloudberry/pull/603)
* [`e53b57c73e9`](https://github.com/apache/cloudberry/commit/e53b57c73e9d245e7b769f0bf7dd4f16f9028821) - [clang-tidy] Add readability braces around statements (#13730) (David Kimura) [#603](https://github.com/apache/cloudberry/pull/603)
* [`fb7a3aec3b5`](https://github.com/apache/cloudberry/commit/fb7a3aec3b5f10a49a386f17592a17995acada96) - Add support in ORCA for splitting ordered-set agg (Ekta Khanna) [#603](https://github.com/apache/cloudberry/pull/603)
* [`a47540c1e3d`](https://github.com/apache/cloudberry/commit/a47540c1e3da38ed58293cc1a47a400ea599385e) - [ORCA] Update compute scalar func cost model (#13506) (David Kimura) [#603](https://github.com/apache/cloudberry/pull/603)
* [`6f10db2333f`](https://github.com/apache/cloudberry/commit/6f10db2333f99316fa28cb54c1baf6225b7df5cb) - Support Const TVF returning composite type in ORCA (#13422) (THANATOSLAVA) [#603](https://github.com/apache/cloudberry/pull/603)
* [`3e68f23be37`](https://github.com/apache/cloudberry/commit/3e68f23be376277bcd5d4557171a120f80100402) - Enable GatherMerge plan alternative for non-EstMaster singleton distribution (GPORCA). (Alexey Gordeev) [#603](https://github.com/apache/cloudberry/pull/603)
* [`b19cfdd4c88`](https://github.com/apache/cloudberry/commit/b19cfdd4c88443532e97fda651345be74c0a1c30) - Introducing parallel array_agg (#13169) (Lei (Alexandra) Wang) [#603](https://github.com/apache/cloudberry/pull/603)
* [`209f897959b`](https://github.com/apache/cloudberry/commit/209f897959bd474e866007b5d18c6abdefce1d6e) - Fix FNullRejecting() error while trying to process zero-placed predicate. (Alexey Gordeev) [#603](https://github.com/apache/cloudberry/pull/603)
* [`8dffe76c48b`](https://github.com/apache/cloudberry/commit/8dffe76c48bd6f8f0c8e7ca539b7701003d9f5ab) - Add tests for GUC of PGC_STRING type value rollback (Sergey Smirnov) [#687](https://github.com/apache/cloudberry/pull/687)
* [`2365a974621`](https://github.com/apache/cloudberry/commit/2365a974621e873fbffba256a76c917dd1b7c626) - Replace usages of InterruptPending to the flag of query cancellation (#13148) (Maksim Milyutin) [#687](https://github.com/apache/cloudberry/pull/687)
* [`9f5f638cda6`](https://github.com/apache/cloudberry/commit/9f5f638cda685fd23468283d9f0688f7a7b7bc56) - Add check for appendonly materialized view to pg_upgrade (#11820) (Yao Wang) [#687](https://github.com/apache/cloudberry/pull/687)
* [`9c8c7e4a7d4`](https://github.com/apache/cloudberry/commit/9c8c7e4a7d43cb90c0b7fac4142669c50709d635) - Wiped out fake "Failed to kill processes for segment" message on gpstop command (#13231) (FairyFar) [#687](https://github.com/apache/cloudberry/pull/687)
* [`8c88f6430ae`](https://github.com/apache/cloudberry/commit/8c88f6430ae56cf97cb0df1918e94d3bd4c0ebfe) - gpstate -e : Display ongoing recovery progress (hari krishna) [#687](https://github.com/apache/cloudberry/pull/687)
* [`e3e1cdb7da3`](https://github.com/apache/cloudberry/commit/e3e1cdb7da3fb32b20dc5f7dbe1a74e987d0d17a) - Fix flaky unit test in buildMirrorSegments (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`ce5ed684711`](https://github.com/apache/cloudberry/commit/ce5ed684711d3f7f3398ec8af409653172cbeb08) - Including PGOPTIONS="-c gp_role=utility" to use utility mode when inv… (#13066) (Annpurna Shahani) [#687](https://github.com/apache/cloudberry/pull/687)
* [`fc502d98fa5`](https://github.com/apache/cloudberry/commit/fc502d98fa5b6dbc1b667cfb87c3570f114261bd) - Prevent gp_tablespace_segment_location() from executing on entrydb QE (#13075) (Hongxu Ma) [#687](https://github.com/apache/cloudberry/pull/687)
* [`6cd3276f566`](https://github.com/apache/cloudberry/commit/6cd3276f566c60ce37d5965fb84aaf1f7e8286c9) - Direct looking for AppendRelInfos by relid in the append_rel_array (#13197) (maqing) [#687](https://github.com/apache/cloudberry/pull/687)
* [`4e5ba146ce5`](https://github.com/apache/cloudberry/commit/4e5ba146ce5a6fba8d1f25a796743b4241482848) - Fix spelling errors in binaries (Bradford D. Boyle) [#687](https://github.com/apache/cloudberry/pull/687)
* [`85e9f50a958`](https://github.com/apache/cloudberry/commit/85e9f50a95895e3b94b8c9546fb1b953eeea1332) - run ALTER TABLE EXPAND PARTITION PREPARE in separate transactions (#13152) (SmartKeyerror) [#687](https://github.com/apache/cloudberry/pull/687)
* [`b789f451890`](https://github.com/apache/cloudberry/commit/b789f4518901b9b7018df0ec5a3c6b17af576fa4) - Fix typos in temp regression test (Jimmy Yih) [#687](https://github.com/apache/cloudberry/pull/687)
* [`1b931d1bcf0`](https://github.com/apache/cloudberry/commit/1b931d1bcf0d7b7405f3be4c615944b68b0b6cd9) - Fix flaky fts_unblock_primary isolation2 test (Jimmy Yih) [#687](https://github.com/apache/cloudberry/pull/687)
* [`8d5ec768dda`](https://github.com/apache/cloudberry/commit/8d5ec768ddaaf939429bf86e3d42ba2a1adb0982) - Resolve GPDB_12_MERGE_FIXMEs: TOAST for AO_ROW Table (Haolin Wang) [#687](https://github.com/apache/cloudberry/pull/687)
* [`9c05d1ec210`](https://github.com/apache/cloudberry/commit/9c05d1ec210abd355ff910e5165e3259f3aca9e3) - Resolve a MERGE 12 FIXME in nodeModifyTable.c. (Zhenghua Lyu) [#687](https://github.com/apache/cloudberry/pull/687)
* [`62c617bfec3`](https://github.com/apache/cloudberry/commit/62c617bfec3a61264725735b0825db9ebbe3d8ed) - Remove obsolete header file 'optimizer/var.h'. (#13187) (Xing Guo) [#687](https://github.com/apache/cloudberry/pull/687)
* [`67598fbdb57`](https://github.com/apache/cloudberry/commit/67598fbdb579808e10dba0b84f552cc8a04ffbea) - Introduce PG_TEST_TIMEOUT_DEFAULT for TAP suite non-elapsing timeouts. (Noah Misch) [#687](https://github.com/apache/cloudberry/pull/687)
* [`b31acdf20e1`](https://github.com/apache/cloudberry/commit/b31acdf20e17dc6fdd6bb16e20fb0bce163e6427) - PR pipeline: Use dedicated worker (icw-centos7). (Ed Espino) [#687](https://github.com/apache/cloudberry/pull/687)
* [`868a727dda8`](https://github.com/apache/cloudberry/commit/868a727dda87a1335f602f9cebc2571b15f41d17) - Run rhel8 ICW jobs in specific workers (Shaoqi Bai) [#687](https://github.com/apache/cloudberry/pull/687)
* [`288a3ac3a64`](https://github.com/apache/cloudberry/commit/288a3ac3a64da63543616acf1abee3a8736ca0b1) - gpmovemirrors: close connection for tablespace information (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`2e1c9903096`](https://github.com/apache/cloudberry/commit/2e1c99030966216b2e1e2b751fe7e577e08bc287) - recoverseg/addmirrors/movemirrors: Add behave and unit tests (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`5daf37f08e8`](https://github.com/apache/cloudberry/commit/5daf37f08e82ab4d5013ae77e59025b8db8b9926) - recoverseg/addmirrors/movemirrors: Revert catalog update for failures (Jamie McAtamney) [#687](https://github.com/apache/cloudberry/pull/687)
* [`00373244f62`](https://github.com/apache/cloudberry/commit/00373244f62e75b019cfe5239de6b13d23592424) - recoverseg/addmirrors/movemirrors: Improve error reporting (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`b557f47eabd`](https://github.com/apache/cloudberry/commit/b557f47eabd8d01230f0ff038c052ee8d731bc93) - recoverseg/addmirrors/movemirrors: Update catalog before recovery (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`fdb6a56e119`](https://github.com/apache/cloudberry/commit/fdb6a56e119896d1e0cbaceaf7e5266105c55244) - recoverseg/addmirrors/movemirrors: Don't stop unreachable failed mirrors (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`db1d363a942`](https://github.com/apache/cloudberry/commit/db1d363a942e5f72ca4c18245f3f075a85a459b6) - recoverseg/addmirrors/movemirrors: Start each mirror independently (Divyesh Vanjare) [#687](https://github.com/apache/cloudberry/pull/687)
* [`948eea101f7`](https://github.com/apache/cloudberry/commit/948eea101f76f582afda7838bc986cf14318ced2) - Fix bug in gpconfigurenewsegment (Nikhil Kak) [#687](https://github.com/apache/cloudberry/pull/687)
* [`1616c91290b`](https://github.com/apache/cloudberry/commit/1616c91290bf2ef304dadddb07d90d08c5e982a4) - make clean `${CMOCKERY_DIR}` and `${MOCK_DIR}` (#13181) (maqing) [#687](https://github.com/apache/cloudberry/pull/687)
* [`91051234b88`](https://github.com/apache/cloudberry/commit/91051234b885cad7a94177eee61957c69a90b296) - make ALTER TABLE EXPAND PARTITION PREPARE reentrant (#13115) (SmartKeyerror) [#687](https://github.com/apache/cloudberry/pull/687)
* [`c548c6ce621`](https://github.com/apache/cloudberry/commit/c548c6ce621b71a1ed04887d7237b4b741d2bc07) - Enable faultinject point 'fault_in_background_writer_main' in builds without assertion. (#13175) (Xing Guo) [#687](https://github.com/apache/cloudberry/pull/687)
* [`077cd163c4f`](https://github.com/apache/cloudberry/commit/077cd163c4faf6aeb1c20ff2ecff1e4f13bf3e83) - Refactor code around encoding clauses and resolve related FIXMEs (Huansong Fu) [#687](https://github.com/apache/cloudberry/pull/687)
* [`fa4de46b350`](https://github.com/apache/cloudberry/commit/fa4de46b35088c5f6dfbfb0022bacb5ef1395ead) - Fix gpfdist loads partial records if the data file is gz compressed (#13161) (Huiliang.liu) [#687](https://github.com/apache/cloudberry/pull/687)
* [`5831820bd81`](https://github.com/apache/cloudberry/commit/5831820bd8103f5a6d95f766b6b634a77cea2496) - add testcase for master_shared_snapshot_collision_fix (QingMa) [#687](https://github.com/apache/cloudberry/pull/687)
* [`b7925346d85`](https://github.com/apache/cloudberry/commit/b7925346d8542e440a961e9a707f70c87145e8e1) - Fix flaky brin testcase (#13138) (David Kimura) [#687](https://github.com/apache/cloudberry/pull/687)
* [`16eb78a5a77`](https://github.com/apache/cloudberry/commit/16eb78a5a779fcf147605930a143c0bea4d040be) - Enhance GPDEMO output to indicate MIRRORED configuration status. (Ed Espino) [#687](https://github.com/apache/cloudberry/pull/687)
* [`0536ec71414`](https://github.com/apache/cloudberry/commit/0536ec7141421bac576fd337d14a4b1c9cb55bd1) - Behave: Pass WITH_MIRRORS env variable from CI task. (Ed Espino) [#687](https://github.com/apache/cloudberry/pull/687)
* [`4c5dcf9c175`](https://github.com/apache/cloudberry/commit/4c5dcf9c175c013bb9be4ce203a5439d1b170fa6) - Pass WITH_MIRRORS env variable from CI task to create-demo-cluster. (Ed Espino) [#687](https://github.com/apache/cloudberry/pull/687)
* [`21d7f84a981`](https://github.com/apache/cloudberry/commit/21d7f84a9813740171f6c32aa5cc12c9122d7a5d) - Remove most msys special processing in TAP tests (Andrew Dunstan) [#687](https://github.com/apache/cloudberry/pull/687)
* [`59bbdd6a156`](https://github.com/apache/cloudberry/commit/59bbdd6a1561e1bcfdc17b2d2c97d2a9290fc379) - Add isolation test for pg_basebackup. Test pg_basebackup with database oid larger than int32 for https://github.com/postgres/postgres/commit/259bbe177808986e5d226ea7ce5a1ebb74657791. (QingMa) [#687](https://github.com/apache/cloudberry/pull/687)
* [`41ac6fdd59d`](https://github.com/apache/cloudberry/commit/41ac6fdd59d46731afef03cedee3c898c0e47557) - Fixed tests: (Leonid Borchuk) [#678](https://github.com/apache/cloudberry/pull/678)
* [`6a95c6391f3`](https://github.com/apache/cloudberry/commit/6a95c6391f3899c2122e4d3c43668149e3cf4288) - Fix FNullRejecting() error while trying to process zero-placed predicate. (Alexey Gordeev) [#678](https://github.com/apache/cloudberry/pull/678)
* [`a4a0a07c2cd`](https://github.com/apache/cloudberry/commit/a4a0a07c2cdc81f7073a6e695434b02b37563982) - Fix flaky qp_misc test case (#13261) (David Kimura) [#678](https://github.com/apache/cloudberry/pull/678)
* [`9b3bf7a65f9`](https://github.com/apache/cloudberry/commit/9b3bf7a65f97fad88cb827da44e35bb5c6323414) - Add ORCA GUC to disable NLJ (David Kimura) [#678](https://github.com/apache/cloudberry/pull/678)
* [`3a8c8c78501`](https://github.com/apache/cloudberry/commit/3a8c8c78501d2331158475ac8c9340eae3c20caa) - Fix Printing Expression Properties (Bhuvnesh Chaudhary) [#678](https://github.com/apache/cloudberry/pull/678)
* [`5633fbb1bc7`](https://github.com/apache/cloudberry/commit/5633fbb1bc72a55734a7a7cabb1b4a49c8fb0a86) - Fast path to REFRESH materialized view. (#682) (Zhang Mingli) [#682](https://github.com/apache/cloudberry/pull/682)
* [`74808061fa6`](https://github.com/apache/cloudberry/commit/74808061fa642568f714f32743f3b64fe6ccd3a2) - gpexpand: TRUNCATE coordinator-only tables for cleanup (Soumyadeep Chakraborty) [#666](https://github.com/apache/cloudberry/pull/666)
* [`87971ba4aa9`](https://github.com/apache/cloudberry/commit/87971ba4aa9236ea32fae281d0338abe491114c7) - Fix explain analyze printing "never executed" uncorrectly (#672) (Xiaoran Wang) [#672](https://github.com/apache/cloudberry/pull/672)
* [`4fe75a9c5b5`](https://github.com/apache/cloudberry/commit/4fe75a9c5b5d41db3b2e196fed73c3acef216c00) - Improve the loading of interconnect with a more flexible way. (#646) (YueZhang) [#646](https://github.com/apache/cloudberry/pull/646)
* [`991fb84509f`](https://github.com/apache/cloudberry/commit/991fb84509ff118d65a69c1f4b6f02c23c5671e2) - fix incorrect first Tid during index scan which using bitmap index (GongXun) [#679](https://github.com/apache/cloudberry/pull/679)
* [`10c6841e6da`](https://github.com/apache/cloudberry/commit/10c6841e6da629327634717b695bc24566c8dcf8) - Suppress compiler warnings in release build. (#674) (reshke) [#674](https://github.com/apache/cloudberry/pull/674)
* [`cc189453263`](https://github.com/apache/cloudberry/commit/cc1894532635da8b5e3d73d796130079a4919f30) - Get back gp_distinct_plans tests. (Zhang Mingli) [#680](https://github.com/apache/cloudberry/pull/680)
* [`63e81316295`](https://github.com/apache/cloudberry/commit/63e81316295d3f605214f6ca556e29bca8ef5797) - FIX multi-level correlated subquery bug (#14453) (Haotian Chen) [#663](https://github.com/apache/cloudberry/pull/663)
* [`1c6da0ed71b`](https://github.com/apache/cloudberry/commit/1c6da0ed71b83ab9bd9be623a13965a79da966dd) - Fix cache lookup failed for type 0 (#14559) (gpopt) [#663](https://github.com/apache/cloudberry/pull/663)
* [`323c0e0059b`](https://github.com/apache/cloudberry/commit/323c0e0059b7dc26a1763858121c97f399509860) - Fix Bitmap index null-array condition failed (#13901) (Haotian Chen) [#663](https://github.com/apache/cloudberry/pull/663)
* [`46de759006a`](https://github.com/apache/cloudberry/commit/46de759006a0bc3cafc538553204763d101a20e5) - resolve compiler warning (#13704) (QingMa) [#663](https://github.com/apache/cloudberry/pull/663)
* [`dfb73aae9c6`](https://github.com/apache/cloudberry/commit/dfb73aae9c6dbae6549efe65d3ac7f67382083bb) - remove duplicate ereport to make the code neat (#13894) (Junwang Zhao) [#663](https://github.com/apache/cloudberry/pull/663)
* [`16df3d57c33`](https://github.com/apache/cloudberry/commit/16df3d57c33077dc4ba0c28530dff1e3505ff2d9) - Fix memory leak due to LogicalRepRelMapEntry.attrmap. (Amit Kapila) [#663](https://github.com/apache/cloudberry/pull/663)
* [`93dd69b9bcd`](https://github.com/apache/cloudberry/commit/93dd69b9bcdc0c8d85bf2b3624034175d0136baf) - Fix gpfdist report "unknown meta type 108" error. (#13457) (zhaorui) [#663](https://github.com/apache/cloudberry/pull/663)
* [`bcbb1c5dd28`](https://github.com/apache/cloudberry/commit/bcbb1c5dd28bc7f6dd85b19b5b27f91710eda828) - Implement 3-phase aggregation with DEDUP HashAgg for DISTINCT. (Zhang Mingli) [#676](https://github.com/apache/cloudberry/pull/676)
* [`e86787e1271`](https://github.com/apache/cloudberry/commit/e86787e1271b51e77f8bd5d8e0ee2975995058c1) - Parallel DEDUP_SEMI and DEDUP_SEMI_REVERSE Join.(A new approach to process Semi Join Query in Parallel of MPP) (#653) (Zhang Mingli) [#653](https://github.com/apache/cloudberry/pull/653)
* [`ff20f9973c6`](https://github.com/apache/cloudberry/commit/ff20f9973c6630c2206a5e82f69ac6b06df796dc) - Fix compile errors that has unused arguments or variables (#673) (Hao Wu) [#673](https://github.com/apache/cloudberry/pull/673)
* [`f70d190de2b`](https://github.com/apache/cloudberry/commit/f70d190de2b81dbe4da6ad20a9fb420dbb09e208) - Refactor parallel scan node (yjhjstz) [#647](https://github.com/apache/cloudberry/pull/647)
* [`fdc5abc0caf`](https://github.com/apache/cloudberry/commit/fdc5abc0cafd3608c54b5a687b89e9647b437349) - Fix reference leak on AO/AOCS partition tables with unique index. (#649) (Zhang Mingli) [#649](https://github.com/apache/cloudberry/pull/649)
* [`734a8a1358a`](https://github.com/apache/cloudberry/commit/734a8a1358a65fcdfa9c3495f183320a601bd5dd) - Update index stats test to be in sync with 3d351d916b20534f973eda760cde17d96545d4c4 (reshke kirill) [#611](https://github.com/apache/cloudberry/pull/611)
* [`1591a3eeee6`](https://github.com/apache/cloudberry/commit/1591a3eeee62b41a2d53429cc9e552e1d979f617) - Update fault injector to better handle tableName argument. (Brent Doil) [#611](https://github.com/apache/cloudberry/pull/611)
* [`e32c49742a9`](https://github.com/apache/cloudberry/commit/e32c49742a9a984ff9cbfd1c5ca1dbf5a55a63cc) - Collect vacuum stats for append-optimized tables (#15262) (Lei (Alexandra) Wang) [#611](https://github.com/apache/cloudberry/pull/611)
* [`c85e8761962`](https://github.com/apache/cloudberry/commit/c85e87619626e9d6f9df88a9d038503c91604040) - Fix 2 compiler warnings. (Zhenghua Lyu) [#611](https://github.com/apache/cloudberry/pull/611)
* [`23b82a83919`](https://github.com/apache/cloudberry/commit/23b82a83919355594b8fd7bf0f3704d9f18340f6) - Report VACUUM progress for append-optimized tables (#15042) (Alexandra Wang) [#611](https://github.com/apache/cloudberry/pull/611)
* [`c60e6acce68`](https://github.com/apache/cloudberry/commit/c60e6acce681914ea8c6a433ea43b66491847c26) - Remove unnecessary GetAppendOnlyEntryAuxOids() call from appendonly_insert() (Haolin Wang) [#611](https://github.com/apache/cloudberry/pull/611)
* [`ea05cb63032`](https://github.com/apache/cloudberry/commit/ea05cb630323c530cde3267f63ec5aa32a977934) - Bring back pg_appendonly to relcache (Huansong Fu) [#611](https://github.com/apache/cloudberry/pull/611)
* [`781c43b63b4`](https://github.com/apache/cloudberry/commit/781c43b63b4a6bc9ba309647cdb8e09b53bf22e3) - Add AO_AUX_ONLY option to VACUUM command (Andrew Repp) [#611](https://github.com/apache/cloudberry/pull/611)
* [`23f65e377eb`](https://github.com/apache/cloudberry/commit/23f65e377eb874d585264f3f381ba662fb931bf9) - Remove visibility check code in aocs compaction (#13886) (Linxu Han) [#611](https://github.com/apache/cloudberry/pull/611)
* [`3882884c30a`](https://github.com/apache/cloudberry/commit/3882884c30a0d015f54e724a5e8d7fa1e8986381) - Fix incorrect index->reltuples after VACUUM (Haolin Wang) [#611](https://github.com/apache/cloudberry/pull/611)
* [`be743ee0cfe`](https://github.com/apache/cloudberry/commit/be743ee0cfe2cc86983ee085ea12e58034563f1d) - vacuum_appendonly_index: Fix IndexVacuumInfo initialization (Vasiliy Ivanov) [#611](https://github.com/apache/cloudberry/pull/611)
* [`1b10c82633c`](https://github.com/apache/cloudberry/commit/1b10c82633cabfe4f408a8a7e4f198ae04563eaf) - aoco: Scan progress reporting for CREATE INDEX (Soumyadeep Chakraborty) [#611](https://github.com/apache/cloudberry/pull/611)
* [`fdfea208877`](https://github.com/apache/cloudberry/commit/fdfea2088778ec63dfd594b4261d6f828d08fabe) - Vacuum auxiliary TOAST should not be dispatched. (Haolin Wang) [#611](https://github.com/apache/cloudberry/pull/611)
* [`6687050bd2a`](https://github.com/apache/cloudberry/commit/6687050bd2a61fc860e741d5468e152bcac9c988) - Add WAL synchronization wait before replica check (yang jianghua) [#655](https://github.com/apache/cloudberry/pull/655)
* [`f26f4ab2053`](https://github.com/apache/cloudberry/commit/f26f4ab2053b455cd7e192c23efbf00d96c3ef1e) - Fix add or alter tag value to object will error out when tag's allowed_values is null. (zhangwenchao) [#642](https://github.com/apache/cloudberry/pull/642)
* [`9e6035c5fef`](https://github.com/apache/cloudberry/commit/9e6035c5fefcbcd94be16b28935e91ba7c4bc339) - Bring back enable_geqo definition for extension compatibility sake (reshke kirill) [#652](https://github.com/apache/cloudberry/pull/652)
* [`fc562a99b14`](https://github.com/apache/cloudberry/commit/fc562a99b1469f0b92742b83bd249365e35c2c5e) - Update appendonly_compaction.c, add helpfull debug via Debug_appendonly_print_compaction (reshke) [#654](https://github.com/apache/cloudberry/pull/654)
* [`5f180c56f83`](https://github.com/apache/cloudberry/commit/5f180c56f830c3c2478ee5c7eb87bf2949e8b99e) - Fix CDatumSortedSet handling of empty arrays causing errors in ORCA (wuyuhao28) [#643](https://github.com/apache/cloudberry/pull/643)
* [`a099d24333c`](https://github.com/apache/cloudberry/commit/a099d24333cd73abb049a856f69bc704f4aad7a2) - Expose two functions to convert string to varchar/bpchar (#644) (Hao Wu) [#644](https://github.com/apache/cloudberry/pull/644)
* [`20b84a725cb`](https://github.com/apache/cloudberry/commit/20b84a725cb1fdafb9f006859150b99f590401be) - need to check the return value of UFileClose, as an error may occur during the close operation (gongxun) [#640](https://github.com/apache/cloudberry/pull/640)
* [`4b3cb341513`](https://github.com/apache/cloudberry/commit/4b3cb341513366e1288b3dc9d10f612df33a79da) - Fix test parallel_retrieve_cursor/explain (wangxiaoran) [#641](https://github.com/apache/cloudberry/pull/641)
* [`f2705e96317`](https://github.com/apache/cloudberry/commit/f2705e9631735dcf28b04a5dd6cbbe45a4d844df) - [AQUMV] Make get_matview_query public to get view query. (Zhang Mingli) [#638](https://github.com/apache/cloudberry/pull/638)
* [`08997519c5f`](https://github.com/apache/cloudberry/commit/08997519c5fddb52adedfe9b97183c0fa8496622) - Update gpshrink: fix reference before assignment (#636) (reshke) [#636](https://github.com/apache/cloudberry/pull/636)
* [`461aee9b327`](https://github.com/apache/cloudberry/commit/461aee9b32720ff65672dae44833bc3c4a5577cc) -  add a new GUC of gp_detect_data_correctness to detect data correctness during OS upgrade (#16333) (reshke) [#628](https://github.com/apache/cloudberry/pull/628)
* [`4d926ede502`](https://github.com/apache/cloudberry/commit/4d926ede502a9c32b192a2d1abf2c6cd03f73642) - Disable gpmapreduce regress test in gpcontrib. (zhangwenchao) [#632](https://github.com/apache/cloudberry/pull/632)
* [`92a97858930`](https://github.com/apache/cloudberry/commit/92a978589301c3ae5225ce2330a450794c8818b1) - Fix typo s/zeors/zeros (reshke) [#639](https://github.com/apache/cloudberry/pull/639)
* [`27764957f9e`](https://github.com/apache/cloudberry/commit/27764957f9e4748ffe5b18917a4824304547e76f) - Disable ssl tap test default. (zhangwenchao) [#637](https://github.com/apache/cloudberry/pull/637)
* [`86663518145`](https://github.com/apache/cloudberry/commit/866635181454abda0477b5800e413e999c99eeab) - Fix volatile EquivalenceClass has no sortref. (Zhang Mingli) [#604](https://github.com/apache/cloudberry/pull/604)
* [`f9f89c3d071`](https://github.com/apache/cloudberry/commit/f9f89c3d0719fa1250935d60b810c6673337d2d6) - Add force param to ExecSquelchNode (wangxiaoran) [#622](https://github.com/apache/cloudberry/pull/622)
* [`1983bc6376a`](https://github.com/apache/cloudberry/commit/1983bc6376a87d8769457d7bc907e59934a7d4d1) - Fix explain analyze hang (wangxiaoran) [#622](https://github.com/apache/cloudberry/pull/622)
* [`fc63ac8dfae`](https://github.com/apache/cloudberry/commit/fc63ac8dfae6d8d68805f04f5778bd998d7dae3d) - Replace usages of InterruptPending to the flag of query cancellation (#13148) (Maksim Milyutin) [#631](https://github.com/apache/cloudberry/pull/631)
* [`c335f01b8d1`](https://github.com/apache/cloudberry/commit/c335f01b8d197161a33007adda92cc9c25e286c9) - Fix ExplainNode forgetting to set sname for node 'T_TupleSplit' (#629) (Xiaoran Wang) [#629](https://github.com/apache/cloudberry/pull/629)
* [`0967e5b42f3`](https://github.com/apache/cloudberry/commit/0967e5b42f346d2ca9ac34c84ebd131925ad2b62) - Add Parallel ANTI join cases. (Zhang Mingli) [#617](https://github.com/apache/cloudberry/pull/617)
* [`cddb51b017d`](https://github.com/apache/cloudberry/commit/cddb51b017dee2b259a52aed0d707decdda7385f) - Fix plpython test cases (Hao Wu) [#624](https://github.com/apache/cloudberry/pull/624)
* [`797c6a1349a`](https://github.com/apache/cloudberry/commit/797c6a1349aa7821a425c34a143f1c72f03420e8) - Fix recursive RECORD-returning plpython functions. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`d388a40bf4b`](https://github.com/apache/cloudberry/commit/d388a40bf4b994474c7a26b6f19b19285229b7ec) - Don't corrupt plpython's "TD" dictionary in a recursive trigger call. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`607b85432e4`](https://github.com/apache/cloudberry/commit/607b85432e45f5d92c15163fecd56c71a2198c58) - Avoid possible longjmp-induced logic error in PLy_trigger_build_args. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`5e0d319123a`](https://github.com/apache/cloudberry/commit/5e0d319123a060b11dbf5ce18eeb9e8bc71b2a79) - Hide warnings from Python headers when using gcc-compatible compiler. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`122e2cf4af9`](https://github.com/apache/cloudberry/commit/122e2cf4af910cd59173ff346b04d68a402df134) - Allow building with MSVC and Strawberry perl (Andrew Dunstan) [#624](https://github.com/apache/cloudberry/pull/624)
* [`c1cd60c63f1`](https://github.com/apache/cloudberry/commit/c1cd60c63f15286aa64dc9afd8ba879ab67ad304) - Fix assertion failure with PL/Python exceptions (Michael Paquier) [#624](https://github.com/apache/cloudberry/pull/624)
* [`1462bba8f5c`](https://github.com/apache/cloudberry/commit/1462bba8f5c7f27c5e42fddcbead7725659ea2f5) - Move return statements out of PG_TRY blocks. (Nathan Bossart) [#624](https://github.com/apache/cloudberry/pull/624)
* [`47c46b9c3ed`](https://github.com/apache/cloudberry/commit/47c46b9c3ed170e36f8f30f467c724eeee2b1835) - Tighten array dimensionality checks in Python -> SQL array conversion. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`ce04d88e9f5`](https://github.com/apache/cloudberry/commit/ce04d88e9f57b850b1193b1440ffe1a2c16cee04) - Handle zero-length sublist correctly in Python -> SQL array conversion. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`8f931febee8`](https://github.com/apache/cloudberry/commit/8f931febee877bfae17ccc31129103dc08a72959) - Fix SPI's handling of errors during transaction commit. (Tom Lane) [#624](https://github.com/apache/cloudberry/pull/624)
* [`f9efdad35b5`](https://github.com/apache/cloudberry/commit/f9efdad35b5e20ef1fe8d99768bdb548408b7c33) - Support sreh in copy from parse stage (#621) (Weinan WANG) [#621](https://github.com/apache/cloudberry/pull/621)
* [`966ae6daf96`](https://github.com/apache/cloudberry/commit/966ae6daf96a576c7c47c6a3711d16b8eb200bfa) - Turn off enable_incremental_sort by default (#623) (Hao Wu) [#623](https://github.com/apache/cloudberry/pull/623)
* [`cdc0fedeb86`](https://github.com/apache/cloudberry/commit/cdc0fedeb86bc712936ffc2a3f2c8537d93fb7d4) - Fix explain analyze (#619) (Xiaoran Wang) [#619](https://github.com/apache/cloudberry/pull/619)
* [`86f07743cb5`](https://github.com/apache/cloudberry/commit/86f07743cb5ebaec43a5719d418daa02a6b1fbc1) - Including complete snapshot message into SharedSnapshot (Weinan WANG) [#613](https://github.com/apache/cloudberry/pull/613)

## Contributors Team

@weinan003, @fanfuxiaoran, @gfphoenix78, @avamingli, @zhangwenchao-123, @reshke, @jiaqizho, @gongxun0928, @zhangyue-hashdata, @roseduan, @leborchuk, @foreyes, @edespino, @yihong0618, 
@tuhaihe, @MisterRaindrop, @HuSen8891, @songdongxiaoa2, @robozmey, @Mulily0513, @tenderwg, @chipitsine, @Tao-Ma, @x4m, @hyongtao-db, @yjhjstz

## New Contributors
* @x4m made their first contribution in [#666](https://github.com/apache/cloudberry/pull/666)
* @hyongtao-db made their first contribution in [#774](https://github.com/apache/cloudberry/pull/774)
* @chipitsine made their first contribution in [#836](https://github.com/apache/cloudberry/pull/836)
* @Tao-Ma made their first contribution in [#828](https://github.com/apache/cloudberry/pull/828)
* @Mulily0513 made their first contribution in [#885](https://github.com/apache/cloudberry/pull/885)
* @tenderwg made their first contribution in [#911](https://github.com/apache/cloudberry/pull/911)
* @robozmey made their first contribution in [#1005](https://github.com/apache/cloudberry/pull/1005)

**Full Changelog**: https://github.com/apache/cloudberry/compare/1.6.0...2.0.0-incubating
