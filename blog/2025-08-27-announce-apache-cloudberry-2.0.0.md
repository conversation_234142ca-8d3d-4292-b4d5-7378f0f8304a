---
slug: "announce-apache-cloudberry-2.0.0"
title: "Apache Cloudberry (Incubating) 2.0.0 Released"
description: "Apache Cloudberry 2.0.0 is the first official release under the Apache Software Foundation."
authors: [asfcloudberry]
tags: [Release]
image: /img/blog/apache-cloudberry-2.0.0-release.png
---

:::note
See the official announcement on the [Apache Cloudberry Dev mailing list](https://lists.apache.org/thread/8vpsybdqzhhk31vd8gfow38f14z7y40m).
:::

The Apache Cloudberry (Incubating) community is pleased to announce the release of Apache Cloudberry (Incubating) version 2.0.0, the project’s first official release under the Apache Software Foundation.

Apache Cloudberry (Incubating) is a Massively Parallel Processing (MPP) database for large-scale data analytics, derived from PostgreSQL and the last open-source version of Greenplum Database. It is designed to support both on-premise and cloud deployments, providing a scalable foundation for data warehousing and advanced analytics.

We’d like to express our gratitude to all contributors to this release, as well as to the mentors and the Apache Incubator community for their invaluable support. This milestone reflects a collaborative effort to meet ASF release requirements and establish Cloudberry as an open and community-driven project.

## Release highlights

- ASF-compliant licensing and NOTICE/DISCLAIMER updates
- Removal of binary artifacts from the source release
- Comprehensive source code header alignment with ASF standards
- Improved build process for Python and C++ components
- Cleaned NOTICE file and refined dependency attributions

## Download

- Download the release: https://cloudberry.apache.org/releases
- Changelog for 2.0.0: https://cloudberry.apache.org/releases/2.0.0-incubating
- For documentation, visit: https://cloudberry.apache.org/docs/

## Contributors team

> @weinan003, @fanfuxiaoran, @gfphoenix78, @avamingli, @zhangwenchao-123, @reshke, @jiaqizho, @gongxun0928, @zhangyue-hashdata, @roseduan, @leborchuk, @foreyes, @edespino, @yihong0618, @tuhaihe, @MisterRaindrop, @HuSen8891, @songdongxiaoa2, @robozmey, @Mulily0513, @tenderwg, @chipitsine, @Tao-Ma, @x4m, @hyongtao-db, @yjhjstz

## Join us

We are eager to expand our community and extend an invitation to new contributors. We genuinely welcome the opportunity to collaborate with individuals who share our passion and expertise.

- Website: https://cloudberry.apache.org
- Repository: https://github.com/apache/cloudberry
- Discussions: https://github.com/apache/cloudberry/discussions
- Issue tracker: https://github.com/apache/cloudberry/issues
- Mailing list: https://lists.apache.org/list.html?<EMAIL>
- Slack channel: https://inviter.co/apache-cloudberry


:::note
Disclaimer:
Apache Cloudberry is an effort undergoing incubation at the Apache Software Foundation (ASF), sponsored by the Apache Incubator PMC. Incubation is required of all newly accepted projects until a review by the Incubator PMC demonstrates that the project has met the ASF’s requirements for community and process. While incubation status is not necessarily a reflection of the project’s stability or readiness for production, it does indicate that the project is working towards compliance with ASF processes and governance.
:::
